<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Adjustments - Professional Image Enhancer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .adjustments-container {
            display: grid;
            grid-template-areas:
                "controls canvas histogram"
                "presets canvas info";
            grid-template-columns: 280px 1fr 280px;
            grid-template-rows: 1fr 180px;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .histogram-panel {
            grid-area: histogram;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
        }
        
        .presets-panel {
            grid-area: presets;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
        }
        
        .info-panel {
            grid-area: info;
            background: #252525;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-value {
            font-size: 11px;
            color: #00d4ff;
            font-weight: 500;
        }
        
        .control-slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 8px;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
        }
        
        .histogram-canvas {
            width: 100%;
            height: 150px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .preset-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            margin: 4px;
            display: inline-block;
        }
        
        .preset-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .auto-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: 1px solid #28a745;
            color: #fff;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .auto-button:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 3px dashed #00d4ff;
            border-radius: 12px;
            padding: 60px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
            z-index: 100;
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.05);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }
        
        .upload-text {
            font-size: 14px;
            color: #888;
        }
        
        .image-info {
            background: #1a1a1a;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 11px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .curves-canvas {
            width: 100%;
            height: 120px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
            cursor: crosshair;
        }
    </style>
</head>
<body>
    <div class="adjustments-container">
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="panel-title">⚡ Základní úpravy</div>
            
            <button class="auto-button" onclick="autoAdjust()">🤖 Automatické úpravy</button>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Exposure</span>
                    <span class="control-value" id="exposureValue">0</span>
                </div>
                <input type="range" class="control-slider" id="exposure" min="-2" max="2" value="0" step="0.1">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Kontrast</span>
                    <span class="control-value" id="contrastValue">0</span>
                </div>
                <input type="range" class="control-slider" id="contrast" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Jas</span>
                    <span class="control-value" id="brightnessValue">0</span>
                </div>
                <input type="range" class="control-slider" id="brightness" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Sytost</span>
                    <span class="control-value" id="saturationValue">0</span>
                </div>
                <input type="range" class="control-slider" id="saturation" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Vibrance</span>
                    <span class="control-value" id="vibranceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="vibrance" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Highlights</span>
                    <span class="control-value" id="highlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="highlights" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Shadows</span>
                    <span class="control-value" id="shadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="shadows" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Whites</span>
                    <span class="control-value" id="whitesValue">0</span>
                </div>
                <input type="range" class="control-slider" id="whites" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Blacks</span>
                    <span class="control-value" id="blacksValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blacks" min="-100" max="100" value="0">
            </div>
            
            <div class="panel-title" style="margin-top: 20px;">📈 Křivky</div>
            <canvas class="curves-canvas" id="curvesCanvas" width="270" height="120"></canvas>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Histogram Panel -->
        <div class="histogram-panel">
            <div class="panel-title">📊 Histogram</div>
            <canvas class="histogram-canvas" id="histogramCanvas" width="270" height="150"></canvas>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="toggleChannel('rgb')" id="rgbBtn">RGB</button>
                <button class="preset-button" onclick="toggleChannel('red')" id="redBtn">R</button>
                <button class="preset-button" onclick="toggleChannel('green')" id="greenBtn">G</button>
                <button class="preset-button" onclick="toggleChannel('blue')" id="blueBtn">B</button>
            </div>
        </div>

        <!-- Presets Panel -->
        <div class="presets-panel">
            <div class="panel-title">🎨 Presets</div>
            
            <button class="preset-button" onclick="applyPreset('natural')">Natural</button>
            <button class="preset-button" onclick="applyPreset('vivid')">Vivid</button>
            <button class="preset-button" onclick="applyPreset('dramatic')">Dramatic</button>
            <button class="preset-button" onclick="applyPreset('soft')">Soft</button>
            <button class="preset-button" onclick="applyPreset('vintage')">Vintage</button>
            <button class="preset-button" onclick="applyPreset('bw')">B&W</button>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="resetAll()" style="width: 100%;">🔄 Reset vše</button>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="info-panel">
            <div class="panel-title">ℹ️ Informace</div>
            
            <div class="image-info" id="imageInfo" style="display: none;">
                <div class="info-row">
                    <span>Rozměry:</span>
                    <span id="imageDimensions">-</span>
                </div>
                <div class="info-row">
                    <span>Velikost:</span>
                    <span id="fileSize">-</span>
                </div>
                <div class="info-row">
                    <span>Formát:</span>
                    <span id="imageFormat">-</span>
                </div>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="downloadImage()" style="width: 100%;">📥 Stáhnout</button>
            </div>
        </div>
    </div>

    <script>
        class BasicAdjustments {
            constructor() {
                this.canvas = document.getElementById('mainCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.histogramCanvas = document.getElementById('histogramCanvas');
                this.histogramCtx = this.histogramCanvas.getContext('2d');
                this.curvesCanvas = document.getElementById('curvesCanvas');
                this.curvesCtx = this.curvesCanvas.getContext('2d');

                this.originalImageData = null;
                this.currentImageData = null;
                this.histogramMode = 'rgb';

                this.adjustments = {
                    exposure: 0,
                    contrast: 0,
                    brightness: 0,
                    saturation: 0,
                    vibrance: 0,
                    highlights: 0,
                    shadows: 0,
                    whites: 0,
                    blacks: 0
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
                this.drawCurvesGrid();
            }

            initializeCanvas() {
                // Set initial canvas size
                this.canvas.width = 800;
                this.canvas.height = 600;
                this.canvas.style.maxWidth = '100%';
                this.canvas.style.maxHeight = '100%';
                this.canvas.style.objectFit = 'contain';
            }

            initializeEventListeners() {
                // File upload
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.1)';
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.style.background = '';
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = '';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                // Adjustment sliders
                Object.keys(this.adjustments).forEach(adjustment => {
                    const slider = document.getElementById(adjustment);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateAdjustment(adjustment, parseFloat(e.target.value));
                        });
                    }
                });
            }

            loadImage(file) {
                console.log('Loading image:', file.name, file.type, file.size);
                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('File read successfully');
                    const img = new Image();
                    img.onload = () => {
                        console.log('Image loaded:', img.width, 'x', img.height);
                        // Get canvas container size
                        const canvasContainer = this.canvas.parentElement;
                        const containerWidth = canvasContainer.clientWidth;
                        const containerHeight = canvasContainer.clientHeight;

                        // Calculate optimal size maintaining aspect ratio
                        let { width, height } = img;
                        const imgRatio = width / height;
                        const containerRatio = containerWidth / containerHeight;

                        if (imgRatio > containerRatio) {
                            // Image is wider than container
                            width = containerWidth * 0.95;
                            height = width / imgRatio;
                        } else {
                            // Image is taller than container
                            height = containerHeight * 0.95;
                            width = height * imgRatio;
                        }

                        // Set canvas size
                        console.log('Setting canvas size:', width, 'x', height);
                        this.canvas.width = width;
                        this.canvas.height = height;
                        this.canvas.style.width = width + 'px';
                        this.canvas.style.height = height + 'px';

                        // Clear and draw image
                        this.ctx.clearRect(0, 0, width, height);
                        this.ctx.drawImage(img, 0, 0, width, height);
                        console.log('Image drawn to canvas');

                        this.originalImageData = this.ctx.getImageData(0, 0, width, height);
                        this.currentImageData = new ImageData(
                            new Uint8ClampedArray(this.originalImageData.data),
                            width,
                            height
                        );

                        document.getElementById('uploadZone').style.display = 'none';
                        this.updateImageInfo(file, { width: img.width, height: img.height });
                        this.updateHistogram();

                        console.log('Image loaded successfully:', file.name);
                    };
                    img.onerror = () => {
                        alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    alert('Chyba při čtení souboru.');
                };
                reader.readAsDataURL(file);
            }

            loadTestImage() {
                // Create test image
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Create gradient background
                const gradient = this.ctx.createLinearGradient(0, 0, 800, 600);
                gradient.addColorStop(0, '#87ceeb');
                gradient.addColorStop(0.5, '#98fb98');
                gradient.addColorStop(1, '#dda0dd');

                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, 800, 600);

                // Add some shapes for testing
                this.ctx.fillStyle = '#ff6b6b';
                this.ctx.fillRect(100, 100, 200, 150);

                this.ctx.fillStyle = '#4ecdc4';
                this.ctx.beginPath();
                this.ctx.arc(600, 200, 80, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#45b7d1';
                this.ctx.beginPath();
                this.ctx.moveTo(400, 350);
                this.ctx.lineTo(500, 350);
                this.ctx.lineTo(450, 450);
                this.ctx.closePath();
                this.ctx.fill();

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );

                document.getElementById('uploadZone').style.display = 'none';
                this.updateHistogram();
            }

            updateAdjustment(type, value) {
                this.adjustments[type] = value;
                document.getElementById(type + 'Value').textContent = value;
                this.applyAdjustments();
                this.updateHistogram();
            }

            applyAdjustments() {
                // Reset to original
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    let r = data[i];
                    let g = data[i + 1];
                    let b = data[i + 2];

                    // Apply exposure
                    const exposureFactor = Math.pow(2, this.adjustments.exposure);
                    r *= exposureFactor;
                    g *= exposureFactor;
                    b *= exposureFactor;

                    // Apply brightness
                    const brightnessFactor = this.adjustments.brightness * 2.55;
                    r += brightnessFactor;
                    g += brightnessFactor;
                    b += brightnessFactor;

                    // Apply contrast
                    const contrastFactor = (this.adjustments.contrast + 100) / 100;
                    r = ((r / 255 - 0.5) * contrastFactor + 0.5) * 255;
                    g = ((g / 255 - 0.5) * contrastFactor + 0.5) * 255;
                    b = ((b / 255 - 0.5) * contrastFactor + 0.5) * 255;

                    // Apply saturation
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const satFactor = (this.adjustments.saturation + 100) / 100;
                    r = gray + (r - gray) * satFactor;
                    g = gray + (g - gray) * satFactor;
                    b = gray + (b - gray) * satFactor;

                    // Apply vibrance (protect skin tones)
                    if (this.adjustments.vibrance !== 0) {
                        const max = Math.max(r, g, b);
                        const min = Math.min(r, g, b);
                        const currentSat = (max - min) / 255;

                        if (currentSat < 0.5) {
                            const vibFactor = 1 + (this.adjustments.vibrance / 100) * (1 - currentSat);
                            r = gray + (r - gray) * vibFactor;
                            g = gray + (g - gray) * vibFactor;
                            b = gray + (b - gray) * vibFactor;
                        }
                    }

                    // Apply highlights/shadows
                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                    const normalizedLum = luminance / 255;

                    if (normalizedLum > 0.7 && this.adjustments.highlights !== 0) {
                        const factor = 1 + (this.adjustments.highlights / 100) * (normalizedLum - 0.7) / 0.3;
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }

                    if (normalizedLum < 0.3 && this.adjustments.shadows !== 0) {
                        const factor = 1 + (this.adjustments.shadows / 100) * (0.3 - normalizedLum) / 0.3;
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }

                    // Apply whites/blacks
                    if (normalizedLum > 0.8 && this.adjustments.whites !== 0) {
                        const factor = 1 + (this.adjustments.whites / 100) * (normalizedLum - 0.8) / 0.2;
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }

                    if (normalizedLum < 0.2 && this.adjustments.blacks !== 0) {
                        const factor = 1 + (this.adjustments.blacks / 100) * (0.2 - normalizedLum) / 0.2;
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }

                    // Clamp values
                    data[i] = Math.max(0, Math.min(255, r));
                    data[i + 1] = Math.max(0, Math.min(255, g));
                    data[i + 2] = Math.max(0, Math.min(255, b));
                }

                this.ctx.putImageData(this.currentImageData, 0, 0);
            }
        }

            updateHistogram() {
                const data = this.currentImageData.data;
                const histogram = {
                    red: new Array(256).fill(0),
                    green: new Array(256).fill(0),
                    blue: new Array(256).fill(0)
                };

                for (let i = 0; i < data.length; i += 4) {
                    histogram.red[data[i]]++;
                    histogram.green[data[i + 1]]++;
                    histogram.blue[data[i + 2]]++;
                }

                this.drawHistogram(histogram);
            }

            drawHistogram(histogram) {
                const ctx = this.histogramCtx;
                const width = this.histogramCanvas.width;
                const height = this.histogramCanvas.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                const maxValue = Math.max(
                    Math.max(...histogram.red),
                    Math.max(...histogram.green),
                    Math.max(...histogram.blue)
                );

                if (maxValue === 0) return;

                const barWidth = width / 256;

                if (this.histogramMode === 'rgb') {
                    // Draw all channels
                    ['red', 'green', 'blue'].forEach((channel, index) => {
                        const colors = ['#ff444444', '#44ff4444', '#4444ff44'];
                        ctx.fillStyle = colors[index];

                        for (let i = 0; i < 256; i++) {
                            const barHeight = (histogram[channel][i] / maxValue) * height;
                            ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                        }
                    });
                } else {
                    // Draw single channel
                    const colors = { red: '#ff4444', green: '#44ff44', blue: '#4444ff' };
                    ctx.fillStyle = colors[this.histogramMode];

                    const channelData = histogram[this.histogramMode];
                    for (let i = 0; i < 256; i++) {
                        const barHeight = (channelData[i] / maxValue) * height;
                        ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                    }
                }
            }

            drawCurvesGrid() {
                const ctx = this.curvesCtx;
                const width = this.curvesCanvas.width;
                const height = this.curvesCanvas.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                // Draw grid
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;

                for (let i = 0; i <= 4; i++) {
                    const x = (i / 4) * width;
                    const y = (i / 4) * height;

                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }

                // Draw diagonal line
                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(0, height);
                ctx.lineTo(width, 0);
                ctx.stroke();
            }

            updateImageInfo(file, originalDimensions) {
                document.getElementById('imageDimensions').textContent =
                    `${originalDimensions.width} × ${originalDimensions.height}`;
                document.getElementById('fileSize').textContent =
                    this.formatFileSize(file.size);
                document.getElementById('imageFormat').textContent =
                    file.type.split('/')[1].toUpperCase();
                document.getElementById('imageInfo').style.display = 'block';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }
        }

        // Global functions
        function toggleChannel(channel) {
            if (!basicAdjustments) return;

            // Update button states
            ['rgb', 'red', 'green', 'blue'].forEach(ch => {
                const btn = document.getElementById(ch + 'Btn');
                if (btn) {
                    btn.style.background = ch === channel ?
                        'linear-gradient(135deg, #00d4ff, #0099cc)' :
                        'linear-gradient(135deg, #404040, #353535)';
                    btn.style.color = ch === channel ? '#000' : '#e0e0e0';
                }
            });

            basicAdjustments.histogramMode = channel;
            basicAdjustments.updateHistogram();
        }

        function applyPreset(presetName) {
            if (!basicAdjustments) return;

            const presets = {
                natural: { contrast: 10, saturation: 5, vibrance: 10 },
                vivid: { contrast: 20, saturation: 25, vibrance: 30 },
                dramatic: { contrast: 40, highlights: -30, shadows: 30, blacks: -20 },
                soft: { contrast: -15, highlights: 20, shadows: -10 },
                vintage: { contrast: -10, saturation: -20, brightness: 10, highlights: -15 },
                bw: { saturation: -100, contrast: 15 }
            };

            const preset = presets[presetName];
            if (preset) {
                Object.keys(preset).forEach(key => {
                    const slider = document.getElementById(key);
                    if (slider) {
                        slider.value = preset[key];
                        basicAdjustments.updateAdjustment(key, preset[key]);
                    }
                });
            }
        }

        function autoAdjust() {
            if (!basicAdjustments) return;

            // Simple auto adjustment
            const autoSettings = {
                contrast: 15,
                brightness: 5,
                saturation: 10,
                vibrance: 15,
                highlights: -10,
                shadows: 10
            };

            Object.keys(autoSettings).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = autoSettings[key];
                    basicAdjustments.updateAdjustment(key, autoSettings[key]);
                }
            });
        }

        function resetAll() {
            if (!basicAdjustments) return;

            Object.keys(basicAdjustments.adjustments).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = 0;
                    basicAdjustments.updateAdjustment(key, 0);
                }
            });
        }

        function downloadImage() {
            if (!basicAdjustments || !basicAdjustments.currentImageData) {
                alert('Žádný obrázek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `enhanced_image_${Date.now()}.png`;
            link.href = basicAdjustments.canvas.toDataURL('image/png');
            link.click();
        }

        // Initialize
        let basicAdjustments;
        document.addEventListener('DOMContentLoaded', () => {
            basicAdjustments = new BasicAdjustments();
        });
