<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced AI Tools</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .ai-container {
            display: grid;
            grid-template-areas: 
                "tools canvas preview"
                "models canvas results";
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 1fr 1fr;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .tools-panel {
            grid-area: tools;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        
        .preview-panel {
            grid-area: preview;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            overflow-y: auto;
        }
        
        .models-panel {
            grid-area: models;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .results-panel {
            grid-area: results;
            background: #252525;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .ai-tool {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .ai-tool:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
            transform: translateY(-1px);
        }
        
        .ai-tool.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }
        
        .tool-icon {
            font-size: 20px;
            width: 30px;
            text-align: center;
        }
        
        .tool-info {
            flex: 1;
        }
        
        .tool-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .tool-desc {
            font-size: 10px;
            opacity: 0.8;
        }
        
        .main-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }
        
        .ai-progress {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            display: none;
            z-index: 1000;
        }
        
        .progress-circle {
            width: 60px;
            height: 60px;
            border: 4px solid #404040;
            border-top: 4px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .progress-text {
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .progress-detail {
            font-size: 11px;
            color: #888;
        }
        
        .model-card {
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .model-card:hover {
            border-color: #00d4ff;
            background: #222;
        }
        
        .model-card.active {
            border-color: #00d4ff;
            background: #1a2a3a;
        }
        
        .model-name {
            font-size: 12px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 4px;
        }
        
        .model-desc {
            font-size: 10px;
            color: #ccc;
            margin-bottom: 6px;
        }
        
        .model-stats {
            display: flex;
            justify-content: space-between;
            font-size: 9px;
            color: #888;
        }
        
        .preview-canvas {
            width: 100%;
            height: 150px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .result-item {
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .result-name {
            font-size: 11px;
            font-weight: bold;
        }
        
        .result-time {
            font-size: 9px;
            color: #888;
        }
        
        .result-preview {
            width: 100%;
            height: 80px;
            background: #0f0f0f;
            border: 1px solid #333;
            border-radius: 3px;
            margin-bottom: 6px;
        }
        
        .result-actions {
            display: flex;
            gap: 4px;
        }
        
        .result-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            transition: all 0.2s;
        }
        
        .result-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .settings-panel {
            background: #222;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .setting-label {
            font-size: 10px;
            color: #ccc;
            width: 80px;
            flex-shrink: 0;
        }
        
        .setting-slider {
            flex: 1;
            margin: 0 8px;
            height: 3px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .setting-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .setting-value {
            font-size: 10px;
            color: #00d4ff;
            width: 30px;
            text-align: right;
        }
        
        .upload-zone {
            border: 2px dashed #555;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.05);
        }
        
        .upload-icon {
            font-size: 32px;
            margin-bottom: 10px;
            opacity: 0.6;
        }
        
        .upload-text {
            font-size: 12px;
            color: #888;
        }
        
        .mask-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .detection-box {
            position: absolute;
            border: 2px solid #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 4px;
        }
        
        .detection-label {
            position: absolute;
            top: -20px;
            left: 0;
            background: #00d4ff;
            color: #000;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="ai-container">
        <!-- Tools Panel -->
        <div class="tools-panel">
            <div class="panel-title">🤖 AI Tools</div>
            
            <div class="ai-tool active" onclick="selectAITool('background-removal')" data-tool="background-removal">
                <div class="tool-icon">🎭</div>
                <div class="tool-info">
                    <div class="tool-name">Background Removal</div>
                    <div class="tool-desc">Automatické odstranění pozadí</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('object-detection')" data-tool="object-detection">
                <div class="tool-icon">🔍</div>
                <div class="tool-info">
                    <div class="tool-name">Object Detection</div>
                    <div class="tool-desc">Detekce a označení objektů</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('face-enhancement')" data-tool="face-enhancement">
                <div class="tool-icon">😊</div>
                <div class="tool-info">
                    <div class="tool-name">Face Enhancement</div>
                    <div class="tool-desc">Vylepšení obličejů a portrétů</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('sky-replacement')" data-tool="sky-replacement">
                <div class="tool-icon">☁️</div>
                <div class="tool-info">
                    <div class="tool-name">Sky Replacement</div>
                    <div class="tool-desc">Výměna oblohy za jinou</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('super-resolution')" data-tool="super-resolution">
                <div class="tool-icon">🔬</div>
                <div class="tool-info">
                    <div class="tool-name">Super Resolution</div>
                    <div class="tool-desc">AI zvětšení s detaily</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('colorization')" data-tool="colorization">
                <div class="tool-icon">🎨</div>
                <div class="tool-info">
                    <div class="tool-name">AI Colorization</div>
                    <div class="tool-desc">Obarvení černobílých fotek</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('style-transfer')" data-tool="style-transfer">
                <div class="tool-icon">🖼️</div>
                <div class="tool-info">
                    <div class="tool-name">Style Transfer</div>
                    <div class="tool-desc">Aplikace uměleckých stylů</div>
                </div>
            </div>
            
            <div class="ai-tool" onclick="selectAITool('denoising')" data-tool="denoising">
                <div class="tool-icon">✨</div>
                <div class="tool-info">
                    <div class="tool-name">AI Denoising</div>
                    <div class="tool-desc">Pokročilá redukce šumu</div>
                </div>
            </div>
            
            <div class="settings-panel" id="toolSettings">
                <div class="panel-title" style="margin-bottom: 8px;">⚙️ Nastavení</div>
                
                <div class="setting-row">
                    <span class="setting-label">Síla</span>
                    <input type="range" class="setting-slider" id="aiStrength" min="0" max="100" value="80">
                    <span class="setting-value" id="aiStrengthValue">80</span>
                </div>
                
                <div class="setting-row">
                    <span class="setting-label">Kvalita</span>
                    <input type="range" class="setting-slider" id="aiQuality" min="1" max="5" value="3">
                    <span class="setting-value" id="aiQualityValue">3</span>
                </div>
                
                <div class="setting-row">
                    <span class="setting-label">Rychlost</span>
                    <input type="range" class="setting-slider" id="aiSpeed" min="1" max="3" value="2">
                    <span class="setting-value" id="aiSpeedValue">2</span>
                </div>
            </div>
            
            <button class="ai-tool" onclick="processWithAI()" style="background: linear-gradient(135deg, #28a745, #20c997); margin-top: 10px;">
                <div class="tool-icon">🚀</div>
                <div class="tool-info">
                    <div class="tool-name">Spustit AI</div>
                    <div class="tool-desc">Zpracovat obrázek</div>
                </div>
            </button>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="aiCanvas"></canvas>
            <canvas class="mask-overlay" id="maskOverlay"></canvas>

            <div class="ai-progress" id="aiProgress">
                <div class="progress-circle"></div>
                <div class="progress-text" id="progressText">Zpracování AI...</div>
                <div class="progress-detail" id="progressDetail">Inicializace modelu</div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <div class="panel-title">👁️ Náhled</div>

            <div class="upload-zone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte pro nahrání obrázku</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>

            <canvas class="preview-canvas" id="beforePreview" width="270" height="150"></canvas>
            <div style="text-align: center; font-size: 10px; color: #888; margin-bottom: 10px;">Před</div>

            <canvas class="preview-canvas" id="afterPreview" width="270" height="150"></canvas>
            <div style="text-align: center; font-size: 10px; color: #888;">Po</div>

            <div style="margin-top: 15px;">
                <button class="result-button" onclick="downloadResult()" style="width: 100%; padding: 8px;">
                    📥 Stáhnout výsledek
                </button>
            </div>
        </div>

        <!-- Models Panel -->
        <div class="models-panel">
            <div class="panel-title">🧠 AI Modely</div>

            <div class="model-card active" onclick="selectModel('esrgan')" data-model="esrgan">
                <div class="model-name">ESRGAN v2.0</div>
                <div class="model-desc">Enhanced Super-Resolution pro fotografie</div>
                <div class="model-stats">
                    <span>Rychlost: ⭐⭐⭐</span>
                    <span>Kvalita: ⭐⭐⭐⭐⭐</span>
                </div>
            </div>

            <div class="model-card" onclick="selectModel('rembg')" data-model="rembg">
                <div class="model-name">RemBG Pro</div>
                <div class="model-desc">Pokročilé odstranění pozadí</div>
                <div class="model-stats">
                    <span>Rychlost: ⭐⭐⭐⭐</span>
                    <span>Kvalita: ⭐⭐⭐⭐</span>
                </div>
            </div>

            <div class="model-card" onclick="selectModel('yolo')" data-model="yolo">
                <div class="model-name">YOLO v8</div>
                <div class="model-desc">Real-time object detection</div>
                <div class="model-stats">
                    <span>Rychlost: ⭐⭐⭐⭐⭐</span>
                    <span>Kvalita: ⭐⭐⭐⭐</span>
                </div>
            </div>

            <div class="model-card" onclick="selectModel('gfpgan')" data-model="gfpgan">
                <div class="model-name">GFPGAN</div>
                <div class="model-desc">Face restoration a enhancement</div>
                <div class="model-stats">
                    <span>Rychlost: ⭐⭐</span>
                    <span>Kvalita: ⭐⭐⭐⭐⭐</span>
                </div>
            </div>

            <div class="model-card" onclick="selectModel('stylegan')" data-model="stylegan">
                <div class="model-name">StyleGAN3</div>
                <div class="model-desc">Neural style transfer</div>
                <div class="model-stats">
                    <span>Rychlost: ⭐⭐</span>
                    <span>Kvalita: ⭐⭐⭐⭐⭐</span>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="results-panel">
            <div class="panel-title">📊 Výsledky</div>

            <div class="result-item">
                <div class="result-header">
                    <span class="result-name">Background Removal</span>
                    <span class="result-time">2 min ago</span>
                </div>
                <canvas class="result-preview" width="250" height="80"></canvas>
                <div class="result-actions">
                    <button class="result-button">👁️ Zobrazit</button>
                    <button class="result-button">📥 Stáhnout</button>
                    <button class="result-button">🗑️ Smazat</button>
                </div>
            </div>

            <div class="result-item">
                <div class="result-header">
                    <span class="result-name">Face Enhancement</span>
                    <span class="result-time">5 min ago</span>
                </div>
                <canvas class="result-preview" width="250" height="80"></canvas>
                <div class="result-actions">
                    <button class="result-button">👁️ Zobrazit</button>
                    <button class="result-button">📥 Stáhnout</button>
                    <button class="result-button">🗑️ Smazat</button>
                </div>
            </div>

            <div class="result-item">
                <div class="result-header">
                    <span class="result-name">Super Resolution</span>
                    <span class="result-time">10 min ago</span>
                </div>
                <canvas class="result-preview" width="250" height="80"></canvas>
                <div class="result-actions">
                    <button class="result-button">👁️ Zobrazit</button>
                    <button class="result-button">📥 Stáhnout</button>
                    <button class="result-button">🗑️ Smazat</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AITools {
            constructor() {
                this.canvas = document.getElementById('aiCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.maskCanvas = document.getElementById('maskOverlay');
                this.maskCtx = this.maskCanvas.getContext('2d');
                this.beforeCanvas = document.getElementById('beforePreview');
                this.beforeCtx = this.beforeCanvas.getContext('2d');
                this.afterCanvas = document.getElementById('afterPreview');
                this.afterCtx = this.afterCanvas.getContext('2d');

                this.currentTool = 'background-removal';
                this.currentModel = 'esrgan';
                this.originalImageData = null;
                this.processedImageData = null;
                this.detectedObjects = [];

                this.settings = {
                    aiStrength: 80,
                    aiQuality: 3,
                    aiSpeed: 2
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;
                this.maskCanvas.width = 800;
                this.maskCanvas.height = 600;
            }

            initializeEventListeners() {
                // File upload
                const fileInput = document.getElementById('fileInput');
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                // Settings sliders
                ['aiStrength', 'aiQuality', 'aiSpeed'].forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateSetting(setting, parseFloat(e.target.value));
                        });
                    }
                });
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Resize to fit canvas
                        const maxWidth = 800;
                        const maxHeight = 600;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        this.canvas.width = width;
                        this.canvas.height = height;
                        this.maskCanvas.width = width;
                        this.maskCanvas.height = height;

                        this.ctx.drawImage(img, 0, 0, width, height);
                        this.originalImageData = this.ctx.getImageData(0, 0, width, height);

                        this.updatePreviews();
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            loadTestImage() {
                // Create a test image
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Background
                const gradient = this.ctx.createLinearGradient(0, 0, 800, 600);
                gradient.addColorStop(0, '#87ceeb');
                gradient.addColorStop(1, '#98fb98');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, 800, 600);

                // Add some objects for AI detection
                // Person silhouette
                this.ctx.fillStyle = '#8b4513';
                this.ctx.fillRect(300, 200, 80, 200);
                this.ctx.beginPath();
                this.ctx.arc(340, 180, 30, 0, 2 * Math.PI);
                this.ctx.fill();

                // Car
                this.ctx.fillStyle = '#ff4500';
                this.ctx.fillRect(500, 350, 120, 60);
                this.ctx.beginPath();
                this.ctx.arc(520, 410, 15, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.beginPath();
                this.ctx.arc(600, 410, 15, 0, 2 * Math.PI);
                this.ctx.fill();

                // Building
                this.ctx.fillStyle = '#696969';
                this.ctx.fillRect(100, 100, 100, 200);

                // Tree
                this.ctx.fillStyle = '#8b4513';
                this.ctx.fillRect(650, 250, 20, 100);
                this.ctx.fillStyle = '#228b22';
                this.ctx.beginPath();
                this.ctx.arc(660, 230, 40, 0, 2 * Math.PI);
                this.ctx.fill();

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.updatePreviews();
            }

            updateSetting(setting, value) {
                this.settings[setting] = value;
                document.getElementById(setting + 'Value').textContent = value;
            }

            updatePreviews() {
                // Update before preview
                this.beforeCtx.clearRect(0, 0, 270, 150);
                this.beforeCtx.drawImage(this.canvas, 0, 0, 270, 150);

                // Update after preview (initially same as before)
                this.afterCtx.clearRect(0, 0, 270, 150);
                this.afterCtx.drawImage(this.canvas, 0, 0, 270, 150);
            }

            showProgress(show, text = 'Zpracování AI...', detail = 'Inicializace modelu') {
                const progress = document.getElementById('aiProgress');
                const progressText = document.getElementById('progressText');
                const progressDetail = document.getElementById('progressDetail');

                progress.style.display = show ? 'flex' : 'none';
                progressText.textContent = text;
                progressDetail.textContent = detail;
            }

            simulateAIProcessing(tool, duration = 3000) {
                return new Promise((resolve) => {
                    this.showProgress(true, `Zpracování: ${this.getToolName(tool)}`, 'Načítání modelu...');

                    const steps = [
                        { text: 'Načítání modelu...', progress: 20 },
                        { text: 'Analýza obrázku...', progress: 40 },
                        { text: 'Aplikace AI...', progress: 70 },
                        { text: 'Finalizace...', progress: 90 },
                        { text: 'Dokončeno!', progress: 100 }
                    ];

                    let currentStep = 0;
                    const stepDuration = duration / steps.length;

                    const interval = setInterval(() => {
                        if (currentStep < steps.length) {
                            document.getElementById('progressDetail').textContent = steps[currentStep].text;
                            currentStep++;
                        } else {
                            clearInterval(interval);
                            this.showProgress(false);
                            resolve();
                        }
                    }, stepDuration);
                });
            }

            getToolName(tool) {
                const names = {
                    'background-removal': 'Background Removal',
                    'object-detection': 'Object Detection',
                    'face-enhancement': 'Face Enhancement',
                    'sky-replacement': 'Sky Replacement',
                    'super-resolution': 'Super Resolution',
                    'colorization': 'AI Colorization',
                    'style-transfer': 'Style Transfer',
                    'denoising': 'AI Denoising'
                };
                return names[tool] || tool;
            }
        }

            async processBackgroundRemoval() {
                await this.simulateAIProcessing('background-removal', 2500);

                // Simulate background removal
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = imageData.data;

                // Simple background removal simulation (remove blue/green background)
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    // If pixel is more blue/green (sky/grass), make it transparent
                    if ((b > r + 30 && b > g + 10) || (g > r + 30 && g > b + 10)) {
                        data[i + 3] = 0; // Make transparent
                    }
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }

            async processObjectDetection() {
                await this.simulateAIProcessing('object-detection', 2000);

                // Simulate object detection
                this.detectedObjects = [
                    { x: 300, y: 200, width: 80, height: 200, class: 'person', confidence: 0.95 },
                    { x: 500, y: 350, width: 120, height: 60, class: 'car', confidence: 0.89 },
                    { x: 100, y: 100, width: 100, height: 200, class: 'building', confidence: 0.76 },
                    { x: 630, y: 190, width: 60, height: 160, class: 'tree', confidence: 0.82 }
                ];

                this.drawDetectionBoxes();
            }

            async processFaceEnhancement() {
                await this.simulateAIProcessing('face-enhancement', 4000);

                // Simulate face enhancement
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = imageData.data;

                // Simple skin smoothing simulation
                for (let y = 170; y < 190; y++) {
                    for (let x = 320; x < 360; x++) {
                        const index = (y * imageData.width + x) * 4;
                        if (index < data.length) {
                            // Smooth skin tones
                            data[index] = Math.min(255, data[index] * 1.1); // Slightly enhance red
                            data[index + 1] = Math.min(255, data[index + 1] * 1.05); // Slightly enhance green
                        }
                    }
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }

            async processSkyReplacement() {
                await this.simulateAIProcessing('sky-replacement', 3500);

                // Simulate sky replacement
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = imageData.data;

                // Replace sky area with sunset colors
                for (let y = 0; y < 250; y++) {
                    for (let x = 0; x < imageData.width; x++) {
                        const index = (y * imageData.width + x) * 4;
                        const progress = y / 250;

                        // Sunset gradient
                        data[index] = Math.round(255 * (1 - progress * 0.3)); // Red
                        data[index + 1] = Math.round(150 * (1 - progress * 0.5)); // Green
                        data[index + 2] = Math.round(50 * (1 - progress * 0.7)); // Blue
                    }
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }

            async processSuperResolution() {
                await this.simulateAIProcessing('super-resolution', 5000);

                // Simulate super resolution by scaling up with sharpening
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');

                tempCanvas.width = this.originalImageData.width * 2;
                tempCanvas.height = this.originalImageData.height * 2;

                tempCtx.putImageData(this.originalImageData, 0, 0);
                tempCtx.imageSmoothingEnabled = false;
                tempCtx.drawImage(this.canvas, 0, 0, tempCanvas.width, tempCanvas.height);

                // Apply sharpening filter
                const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
                this.applySharpeningFilter(imageData);

                // Scale back to original size for display
                this.canvas.width = this.originalImageData.width;
                this.canvas.height = this.originalImageData.height;
                this.ctx.putImageData(this.resizeImageData(imageData, this.originalImageData.width, this.originalImageData.height), 0, 0);

                this.processedImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                this.updateAfterPreview();
            }

            async processColorization() {
                await this.simulateAIProcessing('colorization', 4500);

                // Simulate colorization by converting to grayscale then adding color
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = imageData.data;

                // First convert to grayscale
                for (let i = 0; i < data.length; i += 4) {
                    const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                    data[i] = gray;
                    data[i + 1] = gray;
                    data[i + 2] = gray;
                }

                // Then add realistic colors
                for (let i = 0; i < data.length; i += 4) {
                    const x = (i / 4) % imageData.width;
                    const y = Math.floor((i / 4) / imageData.width);

                    // Sky area - blue tint
                    if (y < 250) {
                        data[i] = Math.min(255, data[i] * 0.8);
                        data[i + 1] = Math.min(255, data[i + 1] * 0.9);
                        data[i + 2] = Math.min(255, data[i + 2] * 1.2);
                    }
                    // Grass area - green tint
                    else if (y > 400) {
                        data[i] = Math.min(255, data[i] * 0.7);
                        data[i + 1] = Math.min(255, data[i + 1] * 1.1);
                        data[i + 2] = Math.min(255, data[i + 2] * 0.6);
                    }
                    // Skin tones
                    else if (x > 320 && x < 360 && y > 170 && y < 190) {
                        data[i] = Math.min(255, data[i] * 1.2);
                        data[i + 1] = Math.min(255, data[i + 1] * 1.0);
                        data[i + 2] = Math.min(255, data[i + 2] * 0.8);
                    }
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }

            async processStyleTransfer() {
                await this.simulateAIProcessing('style-transfer', 6000);

                // Simulate style transfer with artistic effect
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                const data = imageData.data;

                // Apply artistic style (oil painting effect)
                for (let i = 0; i < data.length; i += 4) {
                    // Increase saturation and contrast
                    data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.5 + 128));
                    data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.5 + 128));
                    data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.5 + 128));
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }

            async processDenoising() {
                await this.simulateAIProcessing('denoising', 3000);

                // Simulate AI denoising with bilateral filter
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                this.applyBilateralFilter(imageData);

                this.ctx.putImageData(imageData, 0, 0);
                this.processedImageData = imageData;
                this.updateAfterPreview();
            }
        }

            drawDetectionBoxes() {
                this.maskCtx.clearRect(0, 0, this.maskCanvas.width, this.maskCanvas.height);

                this.detectedObjects.forEach((obj, index) => {
                    // Create detection box element
                    const box = document.createElement('div');
                    box.className = 'detection-box';
                    box.style.left = obj.x + 'px';
                    box.style.top = obj.y + 'px';
                    box.style.width = obj.width + 'px';
                    box.style.height = obj.height + 'px';

                    const label = document.createElement('div');
                    label.className = 'detection-label';
                    label.textContent = `${obj.class} (${Math.round(obj.confidence * 100)}%)`;
                    box.appendChild(label);

                    // Remove existing boxes
                    document.querySelectorAll('.detection-box').forEach(el => el.remove());

                    // Add to canvas area
                    document.querySelector('.canvas-area').appendChild(box);
                });
            }

            updateAfterPreview() {
                this.afterCtx.clearRect(0, 0, 270, 150);
                this.afterCtx.drawImage(this.canvas, 0, 0, 270, 150);
            }

            applySharpeningFilter(imageData) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const output = new Uint8ClampedArray(data);

                // Sharpening kernel
                const kernel = [
                    0, -1, 0,
                    -1, 5, -1,
                    0, -1, 0
                ];

                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        for (let c = 0; c < 3; c++) {
                            let sum = 0;
                            for (let ky = -1; ky <= 1; ky++) {
                                for (let kx = -1; kx <= 1; kx++) {
                                    const index = ((y + ky) * width + (x + kx)) * 4 + c;
                                    const weight = kernel[(ky + 1) * 3 + (kx + 1)];
                                    sum += data[index] * weight;
                                }
                            }
                            const index = (y * width + x) * 4 + c;
                            output[index] = Math.max(0, Math.min(255, sum));
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyBilateralFilter(imageData) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const output = new Uint8ClampedArray(data);

                const radius = 5;
                const sigmaColor = 50;
                const sigmaSpace = 50;

                for (let y = radius; y < height - radius; y++) {
                    for (let x = radius; x < width - radius; x++) {
                        for (let c = 0; c < 3; c++) {
                            let sum = 0;
                            let weightSum = 0;
                            const centerIndex = (y * width + x) * 4 + c;
                            const centerValue = data[centerIndex];

                            for (let dy = -radius; dy <= radius; dy++) {
                                for (let dx = -radius; dx <= radius; dx++) {
                                    const neighborIndex = ((y + dy) * width + (x + dx)) * 4 + c;
                                    const neighborValue = data[neighborIndex];

                                    const spatialWeight = Math.exp(-(dx * dx + dy * dy) / (2 * sigmaSpace * sigmaSpace));
                                    const colorWeight = Math.exp(-Math.pow(centerValue - neighborValue, 2) / (2 * sigmaColor * sigmaColor));
                                    const weight = spatialWeight * colorWeight;

                                    sum += neighborValue * weight;
                                    weightSum += weight;
                                }
                            }

                            output[centerIndex] = sum / weightSum;
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            resizeImageData(imageData, newWidth, newHeight) {
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = imageData.width;
                tempCanvas.height = imageData.height;

                tempCtx.putImageData(imageData, 0, 0);

                const resizedCanvas = document.createElement('canvas');
                const resizedCtx = resizedCanvas.getContext('2d');
                resizedCanvas.width = newWidth;
                resizedCanvas.height = newHeight;

                resizedCtx.drawImage(tempCanvas, 0, 0, newWidth, newHeight);
                return resizedCtx.getImageData(0, 0, newWidth, newHeight);
            }
        }

        // Global functions
        function selectAITool(toolName) {
            if (!aiTools) return;

            // Update button states
            document.querySelectorAll('.ai-tool').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

            aiTools.currentTool = toolName;

            // Clear previous detection boxes
            document.querySelectorAll('.detection-box').forEach(el => el.remove());
        }

        function selectModel(modelName) {
            if (!aiTools) return;

            // Update button states
            document.querySelectorAll('.model-card').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-model="${modelName}"]`).classList.add('active');

            aiTools.currentModel = modelName;
        }

        async function processWithAI() {
            if (!aiTools || !aiTools.originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            // Clear previous detection boxes
            document.querySelectorAll('.detection-box').forEach(el => el.remove());

            switch(aiTools.currentTool) {
                case 'background-removal':
                    await aiTools.processBackgroundRemoval();
                    break;
                case 'object-detection':
                    await aiTools.processObjectDetection();
                    break;
                case 'face-enhancement':
                    await aiTools.processFaceEnhancement();
                    break;
                case 'sky-replacement':
                    await aiTools.processSkyReplacement();
                    break;
                case 'super-resolution':
                    await aiTools.processSuperResolution();
                    break;
                case 'colorization':
                    await aiTools.processColorization();
                    break;
                case 'style-transfer':
                    await aiTools.processStyleTransfer();
                    break;
                case 'denoising':
                    await aiTools.processDenoising();
                    break;
                default:
                    alert('Nástroj není implementován!');
            }

            // Add result to history
            addResultToHistory(aiTools.currentTool);
        }

        function addResultToHistory(toolName) {
            const resultsPanel = document.querySelector('.results-panel');
            const existingResults = resultsPanel.querySelectorAll('.result-item');

            // Remove oldest result if more than 3
            if (existingResults.length >= 4) {
                existingResults[existingResults.length - 1].remove();
            }

            // Create new result item
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div class="result-header">
                    <span class="result-name">${aiTools.getToolName(toolName)}</span>
                    <span class="result-time">právě teď</span>
                </div>
                <canvas class="result-preview" width="250" height="80"></canvas>
                <div class="result-actions">
                    <button class="result-button" onclick="viewResult(this)">👁️ Zobrazit</button>
                    <button class="result-button" onclick="downloadResult()">📥 Stáhnout</button>
                    <button class="result-button" onclick="deleteResult(this)">🗑️ Smazat</button>
                </div>
            `;

            // Draw preview
            const canvas = resultItem.querySelector('.result-preview');
            const ctx = canvas.getContext('2d');
            ctx.drawImage(aiTools.canvas, 0, 0, 250, 80);

            // Insert at the beginning
            const firstResult = resultsPanel.querySelector('.result-item');
            if (firstResult) {
                resultsPanel.insertBefore(resultItem, firstResult);
            } else {
                resultsPanel.appendChild(resultItem);
            }
        }

        function viewResult(button) {
            const resultItem = button.closest('.result-item');
            const canvas = resultItem.querySelector('.result-preview');

            // Copy result back to main canvas
            aiTools.ctx.clearRect(0, 0, aiTools.canvas.width, aiTools.canvas.height);
            aiTools.ctx.drawImage(canvas, 0, 0, aiTools.canvas.width, aiTools.canvas.height);
            aiTools.updateAfterPreview();
        }

        function deleteResult(button) {
            if (confirm('Smazat tento výsledek?')) {
                button.closest('.result-item').remove();
            }
        }

        function downloadResult() {
            if (!aiTools || !aiTools.processedImageData) {
                alert('Žádný výsledek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `ai_processed_${aiTools.currentTool}_${Date.now()}.png`;
            link.href = aiTools.canvas.toDataURL('image/png');
            link.click();
        }

        // Initialize AI Tools
        let aiTools;
        document.addEventListener('DOMContentLoaded', () => {
            aiTools = new AITools();
        });
