<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Grading - <PERSON><PERSON><PERSON><PERSON>pravy</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .grading-container {
            display: grid;
            grid-template-columns: 320px 1fr 280px;
            grid-template-rows: 1fr;
            grid-template-areas: "controls canvas tools";
            height: 100vh;
            gap: 0;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-right: 1px solid #404040;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .tools-panel {
            grid-area: tools;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-left: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
            border: 1px solid #404040;
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px dashed #00d4ff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .control-group {
            margin-bottom: 20px;
            background: #252525;
            padding: 15px;
            border-radius: 6px;
        }
        
        .control-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #ccc;
        }
        
        .control-value {
            color: #00d4ff;
            font-weight: bold;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .auto-button {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .auto-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }
        
        .preset-button {
            background: #404040;
            color: #e0e0e0;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .preset-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .color-wheel {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 10px auto;
            border: 2px solid #404040;
            cursor: crosshair;
        }
        
        .lut-preview {
            width: 60px;
            height: 40px;
            border: 1px solid #404040;
            border-radius: 3px;
            margin: 3px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .lut-preview:hover {
            border-color: #00d4ff;
            transform: scale(1.05);
        }
        
        .lut-preview.active {
            border-color: #00d4ff;
            box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin: 15px 0 10px 0;
            border-bottom: 1px solid #404040;
            padding-bottom: 5px;
        }
        
        /* Help System */
        .help-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
            transition: all 0.3s;
        }
        
        .help-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 212, 255, 0.4);
        }
        
        .help-toggle.active {
            background: #ff6b6b;
            color: #fff;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.4;
            max-width: 280px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            border: 1px solid #00d4ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
        }
        
        .tooltip.show {
            opacity: 1;
            visibility: visible;
        }
        
        .tooltip::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #00d4ff;
        }
        
        .tooltip-title {
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 4px;
        }
        
        .control-group.help-active {
            position: relative;
            border: 2px solid #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Help Toggle Button -->
    <button class="help-toggle" id="helpToggle" onclick="toggleHelp()" title="Zapnout/vypnout nápovědu">?</button>
    
    <div class="grading-container">
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="panel-title">🎨 Color Grading</div>
            
            <button class="auto-button" onclick="document.getElementById('fileInput').click()" style="background: linear-gradient(135deg, #28a745, #20c997); margin-bottom: 10px;" data-help="Import|Nahrajte vlastní obrázek z počítače pro barevné úpravy.">📁 Importovat obrázek</button>
            
            <button class="auto-button" id="autoButton" onclick="autoColorGrade()" data-help="Auto Color Grading|Inteligentní analýza obrázku s 3 různými styly. Klikejte opakovaně pro Natural → Cinematic → Vibrant.">🎨 Auto Color Grading</button>
            
            <div class="section-title">Základní tóny</div>
            
            <div class="control-group" data-help="Teplota|Barevná teplota obrázku. Záporné hodnoty = chladnější (modré), kladné = teplejší (žluté).">
                <div class="control-label">
                    <span>Teplota</span>
                    <span class="control-value" id="temperatureValue">0</span>
                </div>
                <input type="range" class="control-slider" id="temperature" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group" data-help="Tint|Odstín barev. Záporné hodnoty = více zelené, kladné = více purpurové.">
                <div class="control-label">
                    <span>Tint</span>
                    <span class="control-value" id="tintValue">0</span>
                </div>
                <input type="range" class="control-slider" id="tint" min="-100" max="100" value="0">
            </div>
            
            <div class="section-title">HSL úpravy</div>
            
            <div class="control-group" data-help="Hue Shift|Posun všech barev na barevném kole. Mění celkový barevný charakter obrázku.">
                <div class="control-label">
                    <span>Hue Shift</span>
                    <span class="control-value" id="hueShiftValue">0</span>
                </div>
                <input type="range" class="control-slider" id="hueShift" min="-180" max="180" value="0">
            </div>
            
            <div class="control-group" data-help="Globální sytost|Sytost všech barev v obrázku. Ovlivňuje intenzitu všech barevných tónů stejně.">
                <div class="control-label">
                    <span>Globální sytost</span>
                    <span class="control-value" id="globalSaturationValue">0</span>
                </div>
                <input type="range" class="control-slider" id="globalSaturation" min="-100" max="100" value="0">
            </div>
            
            <div style="margin-top: 20px;">
                <button class="preset-button" onclick="applyLook('cinematic')" data-help="Cinematic|Filmový vzhled s teplými tóny a mírně sníženou sytostí.">Cinematic</button>
                <button class="preset-button" onclick="applyLook('vintage')" data-help="Vintage|Retro vzhled s posunutými barvami a vintage nádechem.">Vintage</button>
                <button class="preset-button" onclick="applyLook('cold')" data-help="Cold|Chladný vzhled s modrými tóny, vhodný pro zimní scény.">Cold</button>
                <button class="preset-button" onclick="applyLook('warm')" data-help="Warm|Teplý vzhled se žlutými/oranžovými tóny.">Warm</button>
                <button class="preset-button" onclick="loadTestImage()" style="width: 100%; margin-top: 10px;" data-help="Test obrázek|Načte ukázkový barevný obrázek pro vyzkoušení color grading funkcí.">🎨 Test obrázek</button>
                <button class="preset-button" onclick="resetAll()" style="width: 100%; margin-top: 5px;" data-help="Reset|Vrátí všechny barevné úpravy na výchozí hodnoty.">🔄 Reset vše</button>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas" width="800" height="600"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">🎨</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Tools Panel -->
        <div class="tools-panel">
            <div class="panel-title">🛠️ Nástroje</div>
            
            <div class="section-title">LUT Presety</div>
            <div id="lutPreviews" style="display: flex; flex-wrap: wrap; justify-content: center;" data-help="LUT Presety|Look-Up Tables - přednastavené barevné styly. Klikněte na náhled pro aplikaci.">
                <!-- LUT previews will be generated here -->
            </div>
            
            <div class="section-title">Barevná kola</div>
            <div data-help="Shadows Wheel|Barevné kolo pro úpravu barev ve stínech. Klikněte a táhněte pro změnu barevného tónu tmavých oblastí.">
                <div style="text-align: center; font-size: 11px; margin-bottom: 5px;">Shadows</div>
                <canvas class="color-wheel" id="shadowsWheel" width="120" height="120"></canvas>
            </div>
            
            <div data-help="Midtones Wheel|Barevné kolo pro úpravu barev ve středních tónech. Ovlivňuje většinu obrázku.">
                <div style="text-align: center; font-size: 11px; margin-bottom: 5px;">Midtones</div>
                <canvas class="color-wheel" id="midtonesWheel" width="120" height="120"></canvas>
            </div>
            
            <div data-help="Highlights Wheel|Barevné kolo pro úpravu barev ve světlech. Klikněte a táhněte pro změnu barevného tónu světlých oblastí.">
                <div style="text-align: center; font-size: 11px; margin-bottom: 5px;">Highlights</div>
                <canvas class="color-wheel" id="highlightsWheel" width="120" height="120"></canvas>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="preset-button" onclick="downloadImage()" style="width: 100%;" data-help="Stáhnout obrázek|Uloží upravený obrázek s aplikovaným color gradingem ve formátu PNG.">📥 Stáhnout</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let canvas, ctx;
        let originalImageData, currentImageData;
        let helpMode = false;
        let currentTooltip = null;
        
        const adjustments = {
            temperature: 0,
            tint: 0,
            hueShift: 0,
            globalSaturation: 0,
            shadowsHue: 0,
            shadowsSat: 0,
            midtonesHue: 0,
            midtonesSat: 0,
            highlightsHue: 0,
            highlightsSat: 0
        };

        function initializeApp() {
            console.log('🚀 Initializing Color Grading...');

            // Get canvas elements
            canvas = document.getElementById('mainCanvas');
            if (!canvas) {
                console.error('❌ Main canvas not found!');
                return false;
            }

            ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ Canvas context not found!');
                return false;
            }

            console.log('✅ Canvas elements initialized');

            // Setup event listeners
            setupEventListeners();

            // Load test image
            loadTestImage();

            // Initialize color wheels
            initializeColorWheels();

            // Generate LUT previews
            generateLUTPreviews();

            // Initialize auto button text
            updateAutoButtonText();

            console.log('✅ Color Grading initialized successfully');
            return true;
        }

        function setupEventListeners() {
            // File input
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            // Upload zone drag & drop
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('drop', handleDrop);
                uploadZone.addEventListener('dragleave', (e) => {
                    e.currentTarget.style.background = '';
                });
            }

            // Adjustment sliders
            ['temperature', 'tint', 'hueShift', 'globalSaturation'].forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.addEventListener('input', (e) => {
                        updateAdjustment(key, parseFloat(e.target.value));
                    });
                }
            });
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('📁 Loading file:', file.name);
                loadImageFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.background = 'rgba(0, 212, 255, 0.15)';
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.background = '';
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                console.log('📁 Loading dropped file:', files[0].name);
                loadImageFile(files[0]);
            }
        }

        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    console.log('🖼️ Image loaded:', img.width, 'x', img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    console.error('❌ Error loading image');
                    alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('❌ Error reading file');
                alert('Chyba při čtení souboru.');
            };
            reader.readAsDataURL(file);
        }

        function drawImageToCanvas(img) {
            // Calculate size to fit canvas
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;

            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;

            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;

            // Clear with black background
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, maxWidth, maxHeight);

            // Draw image centered
            ctx.drawImage(img, x, y, width, height);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);
            currentImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);

            // Hide upload zone
            hideUploadZone();

            console.log('✅ Image drawn to canvas');
        }

        function loadTestImage() {
            console.log('🎨 Loading test image...');

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Create colorful test pattern for color grading
            const gradient1 = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient1.addColorStop(0, '#ff0000');
            gradient1.addColorStop(0.33, '#00ff00');
            gradient1.addColorStop(0.66, '#0000ff');
            gradient1.addColorStop(1, '#ffff00');

            ctx.fillStyle = gradient1;
            ctx.fillRect(0, 0, canvas.width, canvas.height / 3);

            const gradient2 = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient2.addColorStop(0, '#ff8800');
            gradient2.addColorStop(0.5, '#8800ff');
            gradient2.addColorStop(1, '#00ff88');

            ctx.fillStyle = gradient2;
            ctx.fillRect(0, canvas.height / 3, canvas.width, canvas.height / 3);

            // Add some geometric shapes with different colors
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(100, 400, 150, 100);

            ctx.fillStyle = '#4ecdc4';
            ctx.beginPath();
            ctx.arc(400, 450, 60, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#45b7d1';
            ctx.beginPath();
            ctx.moveTo(600, 400);
            ctx.lineTo(700, 400);
            ctx.lineTo(650, 500);
            ctx.closePath();
            ctx.fill();

            // Add text
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('COLOR GRADING TEST', 250, 350);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // Hide upload zone
            hideUploadZone();

            console.log('✅ Test image loaded');
        }

        function updateAdjustment(type, value) {
            adjustments[type] = value;

            // Update slider position
            const slider = document.getElementById(type);
            if (slider) {
                slider.value = value;
            }

            // Update value display
            const valueDisplay = document.getElementById(type + 'Value');
            if (valueDisplay) {
                valueDisplay.textContent = value;
            }

            applyColorGrading();
        }

        function applyColorGrading() {
            if (!originalImageData) return;

            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;

            for (let i = 0; i < data.length; i += 4) {
                let r = originalData[i];
                let g = originalData[i + 1];
                let b = originalData[i + 2];

                // Apply temperature
                if (adjustments.temperature !== 0) {
                    const temp = adjustments.temperature / 100;
                    r += temp * 30;
                    b -= temp * 30;
                }

                // Apply tint
                if (adjustments.tint !== 0) {
                    const tint = adjustments.tint / 100;
                    g += tint * 20;
                    r -= tint * 10;
                }

                // Apply hue shift
                if (adjustments.hueShift !== 0) {
                    const hsl = rgbToHsl(r, g, b);
                    hsl[0] = (hsl[0] + adjustments.hueShift / 360) % 1;
                    if (hsl[0] < 0) hsl[0] += 1;
                    const rgb = hslToRgb(hsl[0], hsl[1], hsl[2]);
                    r = rgb[0];
                    g = rgb[1];
                    b = rgb[2];
                }

                // Apply global saturation
                if (adjustments.globalSaturation !== 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const satFactor = 1 + (adjustments.globalSaturation / 100);
                    r = gray + satFactor * (r - gray);
                    g = gray + satFactor * (g - gray);
                    b = gray + satFactor * (b - gray);
                }

                // Apply color wheel adjustments based on luminance
                const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                let wheelType = '';
                let wheelStrength = 0;

                if (luminance < 85) {
                    // Shadows
                    wheelType = 'shadows';
                    wheelStrength = (85 - luminance) / 85;
                } else if (luminance > 170) {
                    // Highlights
                    wheelType = 'highlights';
                    wheelStrength = (luminance - 170) / 85;
                } else {
                    // Midtones
                    wheelType = 'midtones';
                    wheelStrength = 1 - Math.abs(luminance - 127.5) / 42.5;
                }

                if (wheelStrength > 0) {
                    const wheelHue = adjustments[wheelType + 'Hue'] || 0;
                    const wheelSat = adjustments[wheelType + 'Sat'] || 0;

                    if (wheelSat > 0) {
                        // Convert to HSL for better color manipulation
                        const hsl = rgbToHsl(r, g, b);

                        // Apply hue shift
                        const hueShift = (wheelHue / 360) * (wheelSat / 100) * wheelStrength * 0.5;
                        hsl[0] = (hsl[0] + hueShift) % 1;
                        if (hsl[0] < 0) hsl[0] += 1;

                        // Apply saturation boost
                        const satBoost = (wheelSat / 100) * wheelStrength * 0.3;
                        hsl[1] = Math.min(1, hsl[1] + satBoost);

                        // Convert back to RGB
                        const rgb = hslToRgb(hsl[0], hsl[1], hsl[2]);
                        r = rgb[0];
                        g = rgb[1];
                        b = rgb[2];
                    }
                }

                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
                data[i + 3] = originalData[i + 3]; // Alpha
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        // Color conversion functions
        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;

            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }

            return [h, s, l];
        }

        function hslToRgb(h, s, l) {
            let r, g, b;

            if (s === 0) {
                r = g = b = l;
            } else {
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1/6) return p + (q - p) * 6 * t;
                    if (t < 1/2) return q;
                    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                    return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
            }

            return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
        }

        // Auto color grading with multiple modes
        let autoMode = 0; // 0, 1, 2 for different styles

        function autoColorGrade() {
            console.log('🎨 Auto color grading...');

            if (!originalImageData) {
                console.log('❌ No image to grade');
                return;
            }

            // Cycle through 3 different auto modes
            autoMode = (autoMode + 1) % 3;

            // Analyze image for intelligent adjustments
            const analysis = analyzeImageForColorGrading();

            switch(autoMode) {
                case 0:
                    applyNaturalAutoGrading(analysis);
                    console.log('✅ Natural auto grading applied');
                    break;
                case 1:
                    applyCinematicAutoGrading(analysis);
                    console.log('✅ Cinematic auto grading applied');
                    break;
                case 2:
                    applyVibrantAutoGrading(analysis);
                    console.log('✅ Vibrant auto grading applied');
                    break;
            }

            // Update color wheels visual
            updateColorWheels();

            // Apply all changes
            applyColorGrading();

            // Update button text to show current mode
            updateAutoButtonText();
        }

        function updateAutoButtonText() {
            const button = document.getElementById('autoButton');
            if (!button) return;

            const modes = [
                { text: '🌿 Auto: Natural', color: 'linear-gradient(135deg, #28a745, #20c997)' },
                { text: '🎬 Auto: Cinematic', color: 'linear-gradient(135deg, #ff6b35, #f7931e)' },
                { text: '🌈 Auto: Vibrant', color: 'linear-gradient(135deg, #e056fd, #f39c12)' }
            ];

            const currentMode = modes[autoMode];
            button.textContent = currentMode.text;
            button.style.background = currentMode.color;
        }

        function analyzeImageForColorGrading() {
            const data = originalImageData.data;
            let totalR = 0, totalG = 0, totalB = 0;
            let darkPixels = 0, brightPixels = 0;
            let warmPixels = 0, coolPixels = 0;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                totalR += r;
                totalG += g;
                totalB += b;

                const brightness = (r + g + b) / 3;
                if (brightness < 85) darkPixels++;
                if (brightness > 170) brightPixels++;

                // Detect warm vs cool tones
                if (r > b + 20) warmPixels++;
                if (b > r + 20) coolPixels++;
            }

            const totalPixels = data.length / 4;
            const avgR = totalR / totalPixels;
            const avgG = totalG / totalPixels;
            const avgB = totalB / totalPixels;

            return {
                avgR, avgG, avgB,
                darkRatio: darkPixels / totalPixels,
                brightRatio: brightPixels / totalPixels,
                warmRatio: warmPixels / totalPixels,
                coolRatio: coolPixels / totalPixels,
                isWarm: warmPixels > coolPixels,
                isDark: darkPixels > brightPixels,
                avgBrightness: (avgR + avgG + avgB) / 3
            };
        }

        function applyNaturalAutoGrading(analysis) {
            // Natural, subtle enhancement
            updateAdjustment('temperature', analysis.isWarm ? -5 : 8);
            updateAdjustment('tint', analysis.avgG > 130 ? -2 : 3);
            updateAdjustment('globalSaturation', analysis.avgBrightness < 100 ? 15 : 8);
            updateAdjustment('hueShift', 2);

            // Subtle color wheel adjustments
            adjustments.shadowsHue = 240;
            adjustments.shadowsSat = 12;
            adjustments.midtonesHue = 30;
            adjustments.midtonesSat = 6;
            adjustments.highlightsHue = 60;
            adjustments.highlightsSat = 8;
        }

        function applyCinematicAutoGrading(analysis) {
            // Cinematic orange and teal look
            updateAdjustment('temperature', 15);
            updateAdjustment('tint', -8);
            updateAdjustment('globalSaturation', analysis.isDark ? 20 : 10);
            updateAdjustment('hueShift', -5);

            // Strong orange and teal in color wheels
            adjustments.shadowsHue = 180; // Teal in shadows
            adjustments.shadowsSat = 25;
            adjustments.midtonesHue = 25; // Orange in midtones
            adjustments.midtonesSat = 20;
            adjustments.highlightsHue = 45; // Warm highlights
            adjustments.highlightsSat = 18;
        }

        function applyVibrantAutoGrading(analysis) {
            // Vibrant, Instagram-style look
            updateAdjustment('temperature', analysis.coolRatio > 0.3 ? 25 : 12);
            updateAdjustment('tint', 5);
            updateAdjustment('globalSaturation', 25);
            updateAdjustment('hueShift', analysis.isWarm ? 10 : -8);

            // Vibrant color wheel adjustments
            adjustments.shadowsHue = analysis.isWarm ? 270 : 200; // Purple or blue shadows
            adjustments.shadowsSat = 30;
            adjustments.midtonesHue = analysis.isWarm ? 45 : 300; // Orange or magenta midtones
            adjustments.midtonesSat = 25;
            adjustments.highlightsHue = analysis.isWarm ? 60 : 180; // Yellow or cyan highlights
            adjustments.highlightsSat = 22;
        }

        function applyLook(lookName) {
            console.log('🎭 Applying look:', lookName);
            const looks = {
                cinematic: {
                    sliders: { temperature: 15, tint: -5, hueShift: 5, globalSaturation: -10 },
                    wheels: { shadowsHue: 240, shadowsSat: 20, midtonesHue: 30, midtonesSat: 10, highlightsHue: 60, highlightsSat: 15 }
                },
                vintage: {
                    sliders: { temperature: 25, tint: 10, hueShift: -15, globalSaturation: -20 },
                    wheels: { shadowsHue: 45, shadowsSat: 25, midtonesHue: 30, midtonesSat: 15, highlightsHue: 60, highlightsSat: 20 }
                },
                cold: {
                    sliders: { temperature: -30, tint: -10, hueShift: 10, globalSaturation: 5 },
                    wheels: { shadowsHue: 240, shadowsSat: 30, midtonesHue: 200, midtonesSat: 15, highlightsHue: 180, highlightsSat: 10 }
                },
                warm: {
                    sliders: { temperature: 40, tint: 5, hueShift: -5, globalSaturation: 10 },
                    wheels: { shadowsHue: 30, shadowsSat: 20, midtonesHue: 45, midtonesSat: 25, highlightsHue: 60, highlightsSat: 30 }
                }
            };

            const look = looks[lookName];
            if (look) {
                // Apply slider adjustments
                Object.keys(look.sliders).forEach(key => {
                    if (adjustments.hasOwnProperty(key)) {
                        const slider = document.getElementById(key);
                        if (slider) {
                            slider.value = look.sliders[key];
                            updateAdjustment(key, look.sliders[key]);
                        }
                    }
                });

                // Apply wheel adjustments
                Object.keys(look.wheels).forEach(key => {
                    adjustments[key] = look.wheels[key];
                });

                // Update color wheels visual
                updateColorWheels();

                // Apply all changes
                applyColorGrading();
            }
        }

        function resetAll() {
            console.log('🔄 Resetting all adjustments...');

            // Reset slider adjustments
            ['temperature', 'tint', 'hueShift', 'globalSaturation'].forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = 0;
                    updateAdjustment(key, 0);
                }
            });

            // Reset color wheel adjustments
            adjustments.shadowsHue = 0;
            adjustments.shadowsSat = 0;
            adjustments.midtonesHue = 0;
            adjustments.midtonesSat = 0;
            adjustments.highlightsHue = 0;
            adjustments.highlightsSat = 0;

            // Update color wheels visual
            updateColorWheels();

            // Apply changes
            applyColorGrading();

            console.log('✅ All adjustments reset including color wheels');
        }

        function initializeColorWheels() {
            const wheels = [
                { id: 'shadowsWheel', type: 'shadows' },
                { id: 'midtonesWheel', type: 'midtones' },
                { id: 'highlightsWheel', type: 'highlights' }
            ];

            wheels.forEach(wheelData => {
                const wheel = document.getElementById(wheelData.id);
                if (wheel) {
                    const wheelCtx = wheel.getContext('2d');
                    drawColorWheel(wheelCtx, wheelData.type);
                    setupWheelInteraction(wheel, wheelData.type);
                }
            });
        }

        function drawColorWheel(ctx, type) {
            const centerX = 60;
            const centerY = 60;
            const radius = 55;

            // Clear canvas
            ctx.clearRect(0, 0, 120, 120);

            // Draw color wheel
            for (let angle = 0; angle < 360; angle += 1) {
                const startAngle = (angle - 1) * Math.PI / 180;
                const endAngle = angle * Math.PI / 180;

                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                ctx.arc(centerX, centerY, 20, endAngle, startAngle, true);
                ctx.closePath();

                const hue = angle;
                const saturation = 100;
                const lightness = 50;
                ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                ctx.fill();
            }

            // Draw center circle (neutral)
            ctx.beginPath();
            ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
            ctx.fillStyle = '#888888';
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw current position indicator
            const currentHue = adjustments[type + 'Hue'] || 0;
            const currentSat = adjustments[type + 'Sat'] || 0;

            const indicatorRadius = 20 + (currentSat / 100) * 35;
            const indicatorAngle = (currentHue * Math.PI) / 180;
            const indicatorX = centerX + Math.cos(indicatorAngle) * indicatorRadius;
            const indicatorY = centerY + Math.sin(indicatorAngle) * indicatorRadius;

            // Draw indicator dot
            ctx.beginPath();
            ctx.arc(indicatorX, indicatorY, 6, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw crosshair in center
            ctx.beginPath();
            ctx.moveTo(centerX - 8, centerY);
            ctx.lineTo(centerX + 8, centerY);
            ctx.moveTo(centerX, centerY - 8);
            ctx.lineTo(centerX, centerY + 8);
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1;
            ctx.stroke();
        }

        function setupWheelInteraction(wheel, type) {
            let isDragging = false;

            wheel.addEventListener('mousedown', (e) => {
                isDragging = true;
                updateWheelValue(e, wheel, type);
            });

            wheel.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    updateWheelValue(e, wheel, type);
                }
            });

            wheel.addEventListener('mouseup', () => {
                isDragging = false;
            });

            wheel.addEventListener('mouseleave', () => {
                isDragging = false;
            });
        }

        function updateWheelValue(event, wheel, type) {
            const rect = wheel.getBoundingClientRect();
            const centerX = 60;
            const centerY = 60;
            const x = event.clientX - rect.left - centerX;
            const y = event.clientY - rect.top - centerY;

            const distance = Math.sqrt(x * x + y * y);

            // Check if click is within wheel bounds
            if (distance > 55) return; // Outside wheel

            const angle = Math.atan2(y, x) * 180 / Math.PI;

            // Calculate hue (0-360)
            let hue = angle;
            if (hue < 0) hue += 360;

            // Calculate saturation based on distance from center
            const maxRadius = 55;
            const minRadius = 20;
            let saturation = 0;

            if (distance > minRadius) {
                saturation = Math.min(100, ((distance - minRadius) / (maxRadius - minRadius)) * 100);
            }

            // If clicking in center, reset to neutral
            if (distance <= minRadius) {
                hue = 0;
                saturation = 0;
            }

            // Update adjustments
            adjustments[type + 'Hue'] = hue;
            adjustments[type + 'Sat'] = saturation;

            // Redraw wheel
            const ctx = wheel.getContext('2d');
            drawColorWheel(ctx, type);

            // Apply color grading
            applyColorGrading();

            console.log(`🎨 ${type} wheel updated: hue=${hue.toFixed(0)}, sat=${saturation.toFixed(0)}`);
        }

        function updateColorWheels() {
            // Redraw all color wheels to show current values
            const wheels = [
                { id: 'shadowsWheel', type: 'shadows' },
                { id: 'midtonesWheel', type: 'midtones' },
                { id: 'highlightsWheel', type: 'highlights' }
            ];

            wheels.forEach(wheelData => {
                const wheel = document.getElementById(wheelData.id);
                if (wheel) {
                    const ctx = wheel.getContext('2d');
                    drawColorWheel(ctx, wheelData.type);
                }
            });
        }

        function generateLUTPreviews() {
            const lutContainer = document.getElementById('lutPreviews');
            if (!lutContainer) return;

            const luts = [
                { name: 'Original', color: '#888' },
                { name: 'Warm', color: '#ff8800' },
                { name: 'Cool', color: '#0088ff' },
                { name: 'Vintage', color: '#aa6600' },
                { name: 'B&W', color: '#666' },
                { name: 'Sepia', color: '#cc9966' }
            ];

            luts.forEach((lut, index) => {
                const preview = document.createElement('div');
                preview.className = 'lut-preview';
                preview.style.background = `linear-gradient(45deg, ${lut.color}, #333)`;
                preview.title = lut.name;
                preview.onclick = () => applyLUT(lut.name);
                lutContainer.appendChild(preview);
            });
        }

        function applyLUT(lutName) {
            console.log('🎬 Applying LUT:', lutName);
            // Simplified LUT application
            switch(lutName) {
                case 'Warm':
                    applyLook('warm');
                    break;
                case 'Cool':
                    applyLook('cold');
                    break;
                case 'Vintage':
                    applyLook('vintage');
                    break;
                case 'B&W':
                    updateAdjustment('globalSaturation', -100);
                    break;
                case 'Sepia':
                    updateAdjustment('temperature', 30);
                    updateAdjustment('globalSaturation', -50);
                    break;
                default:
                    resetAll();
            }
        }

        function hideUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }
        }

        function downloadImage() {
            if (!canvas) {
                alert('Žádný obrázek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `color_graded_image_${Date.now()}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
            console.log('📥 Image downloaded');
        }

        // Help System Functions (same as basic-adjustments)
        function toggleHelp() {
            helpMode = !helpMode;
            const helpToggle = document.getElementById('helpToggle');

            if (helpMode) {
                helpToggle.classList.add('active');
                helpToggle.textContent = '✕';
                helpToggle.title = 'Vypnout nápovědu';
                setupHelpListeners();
                showWelcomeTooltip();
            } else {
                helpToggle.classList.remove('active');
                helpToggle.textContent = '?';
                helpToggle.title = 'Zapnout nápovědu';
                removeHelpListeners();
                hideTooltip();
            }

            console.log('🔧 Help mode:', helpMode ? 'ON' : 'OFF');
        }

        function setupHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.addEventListener('mouseenter', showTooltip);
                element.addEventListener('mouseleave', hideTooltip);
            });
        }

        function removeHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.removeEventListener('mouseenter', showTooltip);
                element.removeEventListener('mouseleave', hideTooltip);
                element.classList.remove('help-active');
            });
        }

        function showTooltip(event) {
            if (!helpMode) return;

            const element = event.currentTarget;
            const helpText = element.getAttribute('data-help');
            if (!helpText) return;

            const [title, description] = helpText.split('|');

            hideTooltip();

            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">${title}</div>
                <div>${description}</div>
            `;

            const rect = element.getBoundingClientRect();

            // Smart positioning - check available space
            const tooltipWidth = 280; // max-width from CSS
            const tooltipHeight = 80; // estimated height

            let left = rect.left;
            let top = rect.bottom + 10;

            // Check if tooltip would go off right edge
            if (left + tooltipWidth > window.innerWidth) {
                left = window.innerWidth - tooltipWidth - 20;
            }

            // Check if tooltip would go off bottom edge
            if (top + tooltipHeight > window.innerHeight) {
                top = rect.top - tooltipHeight - 10;
                // Update arrow position for top placement
                tooltip.style.setProperty('--arrow-position', 'bottom');
            }

            // Check if tooltip would go off left edge
            if (left < 10) {
                left = 10;
            }

            // Check if tooltip would go off top edge
            if (top < 10) {
                top = rect.bottom + 10;
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            document.body.appendChild(tooltip);
            currentTooltip = tooltip;

            element.classList.add('help-active');
        }

        function hideTooltip() {
            if (currentTooltip) {
                currentTooltip.remove();
                currentTooltip = null;
            }

            const activeElements = document.querySelectorAll('.help-active');
            activeElements.forEach(el => el.classList.remove('help-active'));
        }

        function showWelcomeTooltip() {
            const helpToggle = document.getElementById('helpToggle');
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">Color Grading nápověda! 🎨</div>
                <div>Najeďte myší na jakýkoliv ovládací prvek pro zobrazení nápovědy o color grading technikách.</div>
            `;

            const rect = helpToggle.getBoundingClientRect();
            tooltip.style.left = (rect.left - 200) + 'px';
            tooltip.style.top = (rect.bottom + 10) + 'px';

            document.body.appendChild(tooltip);

            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 3000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded');
            if (initializeApp()) {
                console.log('✅ Color Grading app initialized successfully');
            } else {
                console.log('❌ Color Grading app initialization failed');
            }
        });
    </script>
</body>
</html>
