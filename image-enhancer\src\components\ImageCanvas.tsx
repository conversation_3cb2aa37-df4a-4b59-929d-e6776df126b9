'use client'

import { useEffect, useRef, useState } from 'react'
import { ImageData, ImageAdjustments } from '@/types/image'
import { 
  applyBrightness, 
  applyContrast, 
  applySaturation,
  createCanvasFromImage,
  loadImageFromFile 
} from '@/utils/imageUtils'

interface ImageCanvasProps {
  imageData: ImageData | null
  adjustments: Partial<ImageAdjustments>
  onCanvasReady?: (canvas: HTMLCanvasElement) => void
}

export default function ImageCanvas({ imageData, adjustments, onCanvasReady }: ImageCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null)

  // Načtení původního obrázku
  useEffect(() => {
    if (!imageData) {
      setOriginalImage(null)
      return
    }

    const loadImage = async () => {
      try {
        const img = await loadImageFromFile(imageData.originalFile)
        setOriginalImage(img)
      } catch (error) {
        console.error('Chyba při načítání obrázku:', error)
      }
    }

    loadImage()
  }, [imageData])

  // Aplikace úprav na canvas
  useEffect(() => {
    if (!originalImage || !canvasRef.current) return

    const applyAdjustments = async () => {
      setIsProcessing(true)
      
      try {
        const canvas = canvasRef.current!
        const ctx = canvas.getContext('2d')!
        
        // Nastavení velikosti canvas
        canvas.width = originalImage.width
        canvas.height = originalImage.height
        
        // Vykreslení původního obrázku
        ctx.drawImage(originalImage, 0, 0)
        
        // Aplikace úprav
        if (adjustments.brightness !== undefined && adjustments.brightness !== 0) {
          applyBrightness(canvas, adjustments.brightness)
        }
        
        if (adjustments.contrast !== undefined && adjustments.contrast !== 0) {
          applyContrast(canvas, adjustments.contrast)
        }
        
        if (adjustments.saturation !== undefined && adjustments.saturation !== 0) {
          applySaturation(canvas, adjustments.saturation)
        }
        
        // Callback s hotovým canvas
        if (onCanvasReady) {
          onCanvasReady(canvas)
        }
        
      } catch (error) {
        console.error('Chyba při aplikaci úprav:', error)
      } finally {
        setIsProcessing(false)
      }
    }

    applyAdjustments()
  }, [originalImage, adjustments, onCanvasReady])

  if (!imageData) {
    return (
      <div className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
        <div className="text-center text-gray-400">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-lg font-medium">Nahrajte obrázek</p>
          <p className="text-sm mt-1">Pro začátek úprav</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
      {isProcessing && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center space-x-3">
            <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-sm font-medium">Zpracování...</span>
          </div>
        </div>
      )}
      
      <div className="w-full h-full flex items-center justify-center p-4">
        <canvas
          ref={canvasRef}
          className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
          style={{ 
            maxWidth: '100%', 
            maxHeight: '100%',
            width: 'auto',
            height: 'auto'
          }}
        />
      </div>
      
      {/* Image Info Overlay */}
      <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white text-xs px-3 py-2 rounded-lg">
        <div>{imageData.width} × {imageData.height} px</div>
        <div>{(imageData.size / 1024 / 1024).toFixed(2)} MB</div>
      </div>
    </div>
  )
}
