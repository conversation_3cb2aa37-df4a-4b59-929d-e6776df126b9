{"name": "lucide-react", "description": "A Lucide icon library package for React applications", "version": "0.263.1", "license": "ISC", "homepage": "https://lucide.dev", "bugs": "https://github.com/lucide-icons/lucide/issues", "repository": {"type": "git", "url": "https://github.com/lucide-icons/lucide.git", "directory": "packages/lucide-react"}, "type": "module", "author": "<PERSON>", "amdName": "lucide-react", "exports": {".": {"import": "./dist/esm/lucide-react.mjs", "require": "./dist/cjs/lucide-react.cjs", "types": "./dist/lucide-react.d.ts"}, "./icons": {"import": "./dist/esm/icons/index.mjs", "require": "./dist/cjs/lucide-react.cjs"}, "./dynamicIconImports": {"import": "./dist/esm/dynamicIconImports.mjs", "types": "./dist/dynamicIconImports.d.ts"}, "./src/createLucideIcon": {"import": "./src/createLucideIcon.ts"}}, "typesVersions": {"*": {"dynamicIconImports": ["./dist/dynamicIconImports.d.ts"]}}, "main": "dist/cjs/lucide-react.cjs", "main:umd": "dist/umd/lucide-react.js", "module": "dist/esm/lucide-react.mjs", "unpkg": "dist/umd/lucide-react.min.js", "typings": "dist/lucide-react.d.ts", "sideEffects": false, "files": ["dist"], "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^11.2.6", "@types/prop-types": "^15.7.5", "@types/react": "^18.0.21", "@vitejs/plugin-react": "^2.1.0", "react": "17.0.2", "react-dom": "17.0.2", "rollup": "^3.5.1", "rollup-plugin-dts": "^5.0.0", "typescript": "^4.8.4", "vite": "^4.3.9", "vitest": "^0.32.2", "@lucide/rollup-plugins": "1.0.0", "@lucide/build-icons": "1.0.0"}, "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0"}, "scripts": {"build": "pnpm clean && pnpm copy:license && pnpm build:icons && pnpm typecheck && pnpm build:bundles", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && rm -rf stats && rm -rf ./src/icons/*.ts", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mjs --renderUniqueKey --withAliases --withDynamicImports --aliasesFileExtension=.ts --iconFileExtension=.ts --exportFileName=index.ts", "build:types": "node ./scripts/buildTypes.mjs", "build:bundles": "rollup -c ./rollup.config.mjs", "typecheck": "tsc", "typecheck:watch": "tsc -w", "test": "vitest run", "version": "pnpm version --git-tag-version=false"}}