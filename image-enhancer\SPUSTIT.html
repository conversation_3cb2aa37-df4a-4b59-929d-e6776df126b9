<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Spustit Image Enhancer Pro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e1e, #2d2d2d);
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .launcher-container {
            background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
            border: 2px solid #00d4ff;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 64px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00d4ff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4ff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 18px;
            color: #ccc;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .launch-button {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 20px 40px;
            border-radius: 12px;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }
        
        .launch-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 212, 255, 0.6);
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .tool-link {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 15px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .tool-link:hover {
            border-color: #00d4ff;
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            transform: translateY(-2px);
        }
        
        .tool-icon {
            font-size: 24px;
        }
        
        .tool-name {
            font-weight: bold;
            font-size: 14px;
        }
        
        .info-section {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 10px;
        }
        
        .info-text {
            font-size: 14px;
            color: #ccc;
            line-height: 1.5;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <div class="logo">🎨</div>
        <h1 class="title">Image Enhancer Pro</h1>
        <p class="subtitle">
            Profesionální sada nástrojů pro úpravu fotografií<br>
            s AI technologiemi a pokročilými funkcemi
        </p>
        
        <a href="index.html" class="launch-button pulse">
            🚀 SPUSTIT HLAVNÍ APLIKACI
        </a>
        
        <div class="info-section">
            <div class="info-title">📋 Rychlý přístup k nástrojům:</div>
            <div class="tools-grid">
                <a href="basic-adjustments.html" class="tool-link">
                    <span class="tool-icon">⚡</span>
                    <span class="tool-name">Základní úpravy</span>
                </a>
                
                <a href="color-grading.html" class="tool-link">
                    <span class="tool-icon">🎨</span>
                    <span class="tool-name">Color Grading</span>
                </a>
                
                <a href="retouch-tools.html" class="tool-link">
                    <span class="tool-icon">🖌️</span>
                    <span class="tool-name">Retuš nástroje</span>
                </a>
                
                <a href="selection-tools.html" class="tool-link">
                    <span class="tool-icon">🎯</span>
                    <span class="tool-name">Pokročilé selekce</span>
                </a>
                
                <a href="histogram-scopes.html" class="tool-link">
                    <span class="tool-icon">📊</span>
                    <span class="tool-name">Histogram & Scopes</span>
                </a>
                
                <a href="lens-correction.html" class="tool-link">
                    <span class="tool-icon">🔧</span>
                    <span class="tool-name">Lens Correction</span>
                </a>
                
                <a href="raw-processor.html" class="tool-link">
                    <span class="tool-icon">📸</span>
                    <span class="tool-name">RAW Processing</span>
                </a>
                
                <a href="ai-tools.html" class="tool-link">
                    <span class="tool-icon">🤖</span>
                    <span class="tool-name">AI Nástroje</span>
                </a>
            </div>
        </div>
        
        <div class="info-section">
            <div class="info-title">💡 Jak začít:</div>
            <div class="info-text">
                1. <strong>Klikněte na "SPUSTIT HLAVNÍ APLIKACI"</strong> pro přehled všech nástrojů<br>
                2. <strong>Nebo vyberte konkrétní nástroj</strong> z rychlého přístupu výše<br>
                3. <strong>Nahrajte svůj obrázek</strong> a začněte upravovat!<br>
                4. <strong>Všechny nástroje běží offline</strong> ve vašem prohlížeči
            </div>
        </div>
        
        <div class="info-section">
            <div class="info-title">🎯 Funkce:</div>
            <div class="info-text">
                ✅ <strong>8 profesionálních nástrojů</strong> pro kompletní workflow<br>
                ✅ <strong>50+ pokročilých funkcí</strong> s real-time náhledem<br>
                ✅ <strong>5 AI modelů</strong> pro automatické vylepšení<br>
                ✅ <strong>Profesionální kvalita</strong> srovnatelná s Adobe Lightroom<br>
                ✅ <strong>Zdarma k použití</strong> bez registrace nebo instalace
            </div>
        </div>
    </div>

    <script>
        // Add click animation
        document.querySelectorAll('.launch-button, .tool-link').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // Add loading effect
        document.querySelector('.launch-button').addEventListener('click', function() {
            const originalText = this.textContent;
            this.textContent = '⏳ Načítání...';
            this.style.opacity = '0.8';
            
            setTimeout(() => {
                this.textContent = originalText;
                this.style.opacity = '1';
            }, 1000);
        });
    </script>
</body>
</html>
