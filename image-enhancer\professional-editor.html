<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Enhancer Pro - Professional Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            overflow: hidden;
            height: 100vh;
        }

        /* Professional Dark Theme */
        .app-container {
            display: grid;
            grid-template-areas:
                "header header header"
                "toolbar canvas properties"
                "layers canvas histogram";
            grid-template-columns: 280px 1fr 320px;
            grid-template-rows: 40px 1fr 200px;
            height: 100vh;
            background: #2d2d2d;
        }

        /* Header Bar */
        .header-bar {
            grid-area: header;
            background: linear-gradient(135deg, #3a3a3a, #2d2d2d);
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .logo {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-right: 30px;
        }

        .menu-bar {
            display: flex;
            gap: 20px;
        }

        .menu-item {
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 13px;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
        }

        /* Toolbar */
        .toolbar {
            grid-area: toolbar;
            background: #252525;
            border-right: 1px solid #404040;
            padding: 15px;
            overflow-y: auto;
        }

        .tool-section {
            margin-bottom: 25px;
        }

        .tool-section h3 {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
        }

        .tool-group {
            background: #2a2a2a;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #3a3a3a;
        }

        .tool-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .tool-row:last-child {
            margin-bottom: 0;
        }

        .tool-label {
            font-size: 11px;
            color: #ccc;
            width: 80px;
            flex-shrink: 0;
        }

        .tool-slider {
            flex: 1;
            margin: 0 8px;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }

        .tool-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }

        .tool-value {
            font-size: 11px;
            color: #00d4ff;
            width: 35px;
            text-align: right;
            font-weight: 500;
        }

        .tool-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            margin: 2px;
        }

        .tool-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }

        .tool-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }

        /* Canvas Area */
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            position: relative;
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
        }

        #mainCanvas {
            max-width: 100%;
            max-height: 100%;
            border-radius: 4px;
        }

        .canvas-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 11px;
            color: #ccc;
        }

        /* Properties Panel */
        .properties-panel {
            grid-area: properties;
            background: #252525;
            border-left: 1px solid #404040;
            padding: 15px;
            overflow-y: auto;
        }

        /* Layers Panel */
        .layers-panel {
            grid-area: layers;
            background: #252525;
            border-right: 1px solid #404040;
            border-top: 1px solid #404040;
            padding: 15px;
            overflow-y: auto;
        }

        /* Histogram Panel */
        .histogram-panel {
            grid-area: histogram;
            background: #252525;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            padding: 15px;
        }

        .histogram-canvas {
            width: 100%;
            height: 120px;
            background: #1a1a1a;
            border-radius: 4px;
            border: 1px solid #404040;
        }

        /* Upload Area */
        .upload-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
            pointer-events: none;
        }

        .upload-area.active {
            pointer-events: all;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .upload-text {
            font-size: 14px;
            margin-bottom: 8px;
        }

        .upload-subtext {
            font-size: 11px;
            color: #555;
        }

        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #2a2a2a;
        }

        ::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .app-container {
                grid-template-columns: 250px 1fr 280px;
            }
        }

        /* Loading Animation */
        .loading-spinner {
            border: 2px solid #404040;
            border-top: 2px solid #00d4ff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header Bar -->
        <div class="header-bar">
            <div class="logo">🎨 Image Enhancer Pro</div>
            <div class="menu-bar">
                <div class="menu-item" onclick="newProject()">Nový</div>
                <div class="menu-item" onclick="openFile()">Otevřít</div>
                <div class="menu-item" onclick="saveProject()">Uložit</div>
                <div class="menu-item" onclick="exportImage()">Export</div>
                <div class="menu-item" onclick="showPreferences()">Nastavení</div>
            </div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar">
            <div class="tool-section">
                <h3>Základní úpravy</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Expozice</span>
                        <input type="range" class="tool-slider" id="exposure" min="-3" max="3" step="0.1" value="0">
                        <span class="tool-value" id="exposureValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Kontrast</span>
                        <input type="range" class="tool-slider" id="contrast" min="-100" max="100" value="0">
                        <span class="tool-value" id="contrastValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Světla</span>
                        <input type="range" class="tool-slider" id="highlights" min="-100" max="100" value="0">
                        <span class="tool-value" id="highlightsValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Stíny</span>
                        <input type="range" class="tool-slider" id="shadows" min="-100" max="100" value="0">
                        <span class="tool-value" id="shadowsValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Bílé</span>
                        <input type="range" class="tool-slider" id="whites" min="-100" max="100" value="0">
                        <span class="tool-value" id="whitesValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Černé</span>
                        <input type="range" class="tool-slider" id="blacks" min="-100" max="100" value="0">
                        <span class="tool-value" id="blacksValue">0</span>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Přítomnost</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Clarity</span>
                        <input type="range" class="tool-slider" id="clarity" min="-100" max="100" value="0">
                        <span class="tool-value" id="clarityValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Vibrance</span>
                        <input type="range" class="tool-slider" id="vibrance" min="-100" max="100" value="0">
                        <span class="tool-value" id="vibranceValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Sytost</span>
                        <input type="range" class="tool-slider" id="saturation" min="-100" max="100" value="0">
                        <span class="tool-value" id="saturationValue">0</span>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>HSL / Color</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Teplota</span>
                        <input type="range" class="tool-slider" id="temperature" min="-100" max="100" value="0">
                        <span class="tool-value" id="temperatureValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Odstín</span>
                        <input type="range" class="tool-slider" id="tint" min="-100" max="100" value="0">
                        <span class="tool-value" id="tintValue">0</span>
                    </div>
                </div>
                <div style="display: flex; flex-wrap: wrap; gap: 4px; margin-top: 8px;">
                    <button class="tool-button" onclick="selectHSLChannel('red')">Červená</button>
                    <button class="tool-button" onclick="selectHSLChannel('orange')">Oranžová</button>
                    <button class="tool-button" onclick="selectHSLChannel('yellow')">Žlutá</button>
                    <button class="tool-button" onclick="selectHSLChannel('green')">Zelená</button>
                    <button class="tool-button" onclick="selectHSLChannel('aqua')">Aqua</button>
                    <button class="tool-button" onclick="selectHSLChannel('blue')">Modrá</button>
                    <button class="tool-button" onclick="selectHSLChannel('purple')">Fialová</button>
                    <button class="tool-button" onclick="selectHSLChannel('magenta')">Magenta</button>
                </div>
            </div>

            <div class="tool-section">
                <h3>Detail</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Zaostření</span>
                        <input type="range" class="tool-slider" id="sharpening" min="0" max="150" value="0">
                        <span class="tool-value" id="sharpeningValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Radius</span>
                        <input type="range" class="tool-slider" id="sharpenRadius" min="0.5" max="3" step="0.1" value="1">
                        <span class="tool-value" id="sharpenRadiusValue">1</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Šum</span>
                        <input type="range" class="tool-slider" id="noiseReduction" min="0" max="100" value="0">
                        <span class="tool-value" id="noiseReductionValue">0</span>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Efekty</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Viněta</span>
                        <input type="range" class="tool-slider" id="vignette" min="-100" max="100" value="0">
                        <span class="tool-value" id="vignetteValue">0</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Zrno</span>
                        <input type="range" class="tool-slider" id="grain" min="0" max="100" value="0">
                        <span class="tool-value" id="grainValue">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <div class="canvas-container">
                <canvas id="mainCanvas" style="display: none;"></canvas>
                <div class="canvas-overlay" id="canvasInfo" style="display: none;">
                    <div id="imageInfo"></div>
                </div>
            </div>

            <div class="upload-area active" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Přetáhněte obrázek nebo klikněte pro výběr</div>
                <div class="upload-subtext">Podporované formáty: JPG, PNG, TIFF, RAW</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <div class="tool-section">
                <h3>Transformace</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Šířka</span>
                        <input type="number" id="imageWidth" style="background: #404040; border: 1px solid #555; color: #e0e0e0; padding: 4px; border-radius: 3px; width: 80px;">
                        <span style="margin: 0 8px;">×</span>
                        <input type="number" id="imageHeight" style="background: #404040; border: 1px solid #555; color: #e0e0e0; padding: 4px; border-radius: 3px; width: 80px;">
                    </div>
                    <div class="tool-row">
                        <label style="font-size: 11px; color: #ccc;">
                            <input type="checkbox" id="maintainAspect" checked> Zachovat poměr
                        </label>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Rotace</span>
                        <input type="range" class="tool-slider" id="rotation" min="-180" max="180" value="0">
                        <span class="tool-value" id="rotationValue">0°</span>
                    </div>
                    <div style="display: flex; gap: 4px; margin-top: 8px;">
                        <button class="tool-button" onclick="flipHorizontal()">↔️ H</button>
                        <button class="tool-button" onclick="flipVertical()">↕️ V</button>
                        <button class="tool-button" onclick="rotate90()">↻ 90°</button>
                        <button class="tool-button" onclick="straighten()">📐 Vyrovnat</button>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Crop & Resize</h3>
                <div class="tool-group">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4px;">
                        <button class="tool-button" onclick="setCropRatio('free')">Volný</button>
                        <button class="tool-button" onclick="setCropRatio('1:1')">1:1</button>
                        <button class="tool-button" onclick="setCropRatio('4:3')">4:3</button>
                        <button class="tool-button" onclick="setCropRatio('16:9')">16:9</button>
                        <button class="tool-button" onclick="setCropRatio('3:2')">3:2</button>
                        <button class="tool-button" onclick="setCropRatio('5:4')">5:4</button>
                    </div>
                    <div style="margin-top: 8px;">
                        <button class="tool-button" onclick="aiUpscale(2)" style="width: 100%;">🔍 AI Upscale 2×</button>
                        <button class="tool-button" onclick="aiUpscale(4)" style="width: 100%; margin-top: 4px;">🔍 AI Upscale 4×</button>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Presety</h3>
                <div class="tool-group">
                    <div style="display: grid; grid-template-columns: 1fr; gap: 4px;">
                        <button class="tool-button" onclick="applyPreset('portrait')">📸 Portrét</button>
                        <button class="tool-button" onclick="applyPreset('landscape')">🏞️ Krajina</button>
                        <button class="tool-button" onclick="applyPreset('vintage')">📷 Vintage</button>
                        <button class="tool-button" onclick="applyPreset('bw')">⚫ Černobílá</button>
                        <button class="tool-button" onclick="applyPreset('dramatic')">🎭 Dramatická</button>
                        <button class="tool-button" onclick="applyPreset('soft')">✨ Jemná</button>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Export</h3>
                <div class="tool-group">
                    <div class="tool-row">
                        <span class="tool-label">Formát</span>
                        <select id="exportFormat" style="background: #404040; border: 1px solid #555; color: #e0e0e0; padding: 4px; border-radius: 3px; flex: 1;">
                            <option value="png">PNG</option>
                            <option value="jpeg">JPEG</option>
                            <option value="tiff">TIFF</option>
                            <option value="webp">WebP</option>
                        </select>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">Kvalita</span>
                        <input type="range" class="tool-slider" id="exportQuality" min="1" max="100" value="95">
                        <span class="tool-value" id="exportQualityValue">95</span>
                    </div>
                    <div class="tool-row">
                        <span class="tool-label">DPI</span>
                        <select id="exportDPI" style="background: #404040; border: 1px solid #555; color: #e0e0e0; padding: 4px; border-radius: 3px; flex: 1;">
                            <option value="72">72 DPI (Web)</option>
                            <option value="150">150 DPI (Plakát)</option>
                            <option value="300" selected>300 DPI (Tisk)</option>
                            <option value="600">600 DPI (Vysoký)</option>
                        </select>
                    </div>
                    <button class="tool-button" onclick="exportImage()" style="width: 100%; margin-top: 8px; background: linear-gradient(135deg, #00d4ff, #0099cc);">💾 Exportovat</button>
                </div>
            </div>
        </div>

        <!-- Layers Panel -->
        <div class="layers-panel">
            <div class="tool-section">
                <h3>Vrstvy</h3>
                <div class="tool-group">
                    <div style="display: flex; gap: 4px; margin-bottom: 8px;">
                        <button class="tool-button" onclick="addLayer()" title="Přidat vrstvu">➕</button>
                        <button class="tool-button" onclick="duplicateLayer()" title="Duplikovat vrstvu">📋</button>
                        <button class="tool-button" onclick="deleteLayer()" title="Smazat vrstvu">🗑️</button>
                        <button class="tool-button" onclick="mergeDown()" title="Sloučit dolů">⬇️</button>
                    </div>
                    <div id="layersList" style="max-height: 120px; overflow-y: auto;">
                        <!-- Layers will be populated here -->
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <h3>Blend Modes</h3>
                <div class="tool-group">
                    <select id="blendMode" style="background: #404040; border: 1px solid #555; color: #e0e0e0; padding: 4px; border-radius: 3px; width: 100%; margin-bottom: 8px;">
                        <option value="normal">Normal</option>
                        <option value="multiply">Multiply</option>
                        <option value="screen">Screen</option>
                        <option value="overlay">Overlay</option>
                        <option value="soft-light">Soft Light</option>
                        <option value="hard-light">Hard Light</option>
                        <option value="color-dodge">Color Dodge</option>
                        <option value="color-burn">Color Burn</option>
                        <option value="darken">Darken</option>
                        <option value="lighten">Lighten</option>
                        <option value="difference">Difference</option>
                        <option value="exclusion">Exclusion</option>
                    </select>
                    <div class="tool-row">
                        <span class="tool-label">Opacity</span>
                        <input type="range" class="tool-slider" id="layerOpacity" min="0" max="100" value="100">
                        <span class="tool-value" id="layerOpacityValue">100%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Histogram Panel -->
        <div class="histogram-panel">
            <div class="tool-section">
                <h3>Histogram</h3>
                <canvas class="histogram-canvas" id="histogramCanvas" width="290" height="120"></canvas>
                <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 10px; color: #888;">
                    <span>0</span>
                    <span>128</span>
                    <span>255</span>
                </div>
                <div style="display: flex; gap: 4px; margin-top: 8px;">
                    <button class="tool-button" onclick="showHistogramChannel('rgb')" id="histRGB">RGB</button>
                    <button class="tool-button" onclick="showHistogramChannel('red')" id="histRed">R</button>
                    <button class="tool-button" onclick="showHistogramChannel('green')" id="histGreen">G</button>
                    <button class="tool-button" onclick="showHistogramChannel('blue')" id="histBlue">B</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input -->
    <input type="file" id="hiddenFileInput" accept="image/*" style="display: none;" multiple>

    <script>
        // Professional Image Editor Core
        class ImageEditor {
            constructor() {
                this.canvas = document.getElementById('mainCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.originalImage = null;
                this.originalImageData = null;
                this.currentImageData = null;
                this.layers = [];
                this.currentLayer = 0;
                this.history = [];
                this.historyIndex = -1;
                this.isProcessing = false;

                this.settings = {
                    exposure: 0,
                    contrast: 0,
                    highlights: 0,
                    shadows: 0,
                    whites: 0,
                    blacks: 0,
                    clarity: 0,
                    vibrance: 0,
                    saturation: 0,
                    temperature: 0,
                    tint: 0,
                    sharpening: 0,
                    sharpenRadius: 1,
                    noiseReduction: 0,
                    vignette: 0,
                    grain: 0,
                    rotation: 0
                };

                this.initializeEventListeners();
                this.initializeHistogram();
            }

            initializeEventListeners() {
                // File upload
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');

                uploadArea.addEventListener('click', () => fileInput.click());
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.style.background = 'rgba(0, 212, 255, 0.1)';
                });
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.style.background = '';
                });
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.style.background = '';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                // Sliders
                Object.keys(this.settings).forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.settings[setting] = parseFloat(e.target.value);
                            this.updateValueDisplay(setting, e.target.value);
                            this.applyAdjustments();
                        });
                    }
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'z':
                                e.preventDefault();
                                if (e.shiftKey) {
                                    this.redo();
                                } else {
                                    this.undo();
                                }
                                break;
                            case 's':
                                e.preventDefault();
                                this.exportImage();
                                break;
                            case 'o':
                                e.preventDefault();
                                fileInput.click();
                                break;
                        }
                    }
                });
            }

            updateValueDisplay(setting, value) {
                const valueElement = document.getElementById(setting + 'Value');
                if (valueElement) {
                    if (setting === 'rotation') {
                        valueElement.textContent = value + '°';
                    } else if (setting.includes('opacity')) {
                        valueElement.textContent = value + '%';
                    } else {
                        valueElement.textContent = value;
                    }
                }
            }

            async loadImage(file) {
                try {
                    this.showLoading(true);

                    const img = await this.createImageFromFile(file);
                    this.originalImage = img;

                    // Setup canvas
                    this.canvas.width = img.width;
                    this.canvas.height = img.height;
                    this.ctx.drawImage(img, 0, 0);

                    // Store original data
                    this.originalImageData = this.ctx.getImageData(0, 0, img.width, img.height);
                    this.currentImageData = new ImageData(
                        new Uint8ClampedArray(this.originalImageData.data),
                        img.width,
                        img.height
                    );

                    // Show canvas
                    document.getElementById('uploadArea').classList.remove('active');
                    this.canvas.style.display = 'block';
                    document.getElementById('canvasInfo').style.display = 'block';

                    // Update UI
                    this.updateImageInfo(file, img);
                    this.updateHistogram();
                    this.resetSettings();

                    // Initialize layers
                    this.initializeLayers();

                    this.showLoading(false);

                } catch (error) {
                    console.error('Error loading image:', error);
                    alert('Chyba při načítání obrázku: ' + error.message);
                    this.showLoading(false);
                }
            }

            createImageFromFile(file) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => resolve(img);
                    img.onerror = reject;
                    img.src = URL.createObjectURL(file);
                });
            }

            showLoading(show) {
                // Implementation for loading indicator
                this.isProcessing = show;
            }
        }

            updateImageInfo(file, img) {
                const info = document.getElementById('imageInfo');
                const formatFileSize = (bytes) => {
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
                };

                info.innerHTML = `
                    <div>${img.width} × ${img.height} px</div>
                    <div>${formatFileSize(file.size)}</div>
                    <div>${file.type}</div>
                `;

                // Update dimension inputs
                document.getElementById('imageWidth').value = img.width;
                document.getElementById('imageHeight').value = img.height;
            }

            applyAdjustments() {
                if (!this.originalImageData || this.isProcessing) return;

                this.isProcessing = true;

                // Use requestAnimationFrame for smooth updates
                requestAnimationFrame(() => {
                    try {
                        // Copy original data
                        const imageData = new ImageData(
                            new Uint8ClampedArray(this.originalImageData.data),
                            this.originalImageData.width,
                            this.originalImageData.height
                        );

                        // Apply all adjustments
                        this.applyExposure(imageData);
                        this.applyContrast(imageData);
                        this.applyHighlightsShadows(imageData);
                        this.applyWhitesBlacks(imageData);
                        this.applyClarity(imageData);
                        this.applyVibranceSaturation(imageData);
                        this.applyTemperatureTint(imageData);
                        this.applyVignette(imageData);
                        this.applyGrain(imageData);

                        // Apply sharpening if needed
                        if (this.settings.sharpening > 0) {
                            this.applySharpening(imageData);
                        }

                        // Apply noise reduction if needed
                        if (this.settings.noiseReduction > 0) {
                            this.applyNoiseReduction(imageData);
                        }

                        // Update canvas
                        this.ctx.putImageData(imageData, 0, 0);
                        this.currentImageData = imageData;

                        // Update histogram
                        this.updateHistogram();

                    } catch (error) {
                        console.error('Error applying adjustments:', error);
                    } finally {
                        this.isProcessing = false;
                    }
                });
            }

            applyExposure(imageData) {
                if (this.settings.exposure === 0) return;

                const data = imageData.data;
                const factor = Math.pow(2, this.settings.exposure);

                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * factor);
                    data[i + 1] = Math.min(255, data[i + 1] * factor);
                    data[i + 2] = Math.min(255, data[i + 2] * factor);
                }
            }

            applyContrast(imageData) {
                if (this.settings.contrast === 0) return;

                const data = imageData.data;
                const factor = (259 * (this.settings.contrast + 255)) / (255 * (259 - this.settings.contrast));

                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.max(0, Math.min(255, factor * (data[i] - 128) + 128));
                    data[i + 1] = Math.max(0, Math.min(255, factor * (data[i + 1] - 128) + 128));
                    data[i + 2] = Math.max(0, Math.min(255, factor * (data[i + 2] - 128) + 128));
                }
            }

            applyHighlightsShadows(imageData) {
                if (this.settings.highlights === 0 && this.settings.shadows === 0) return;

                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

                    if (luminance > 128) { // Highlights
                        const factor = 1 + (this.settings.highlights / 100) * (luminance / 255);
                        data[i] = Math.max(0, Math.min(255, data[i] * factor));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                    } else { // Shadows
                        const factor = 1 + (this.settings.shadows / 100) * ((255 - luminance) / 255);
                        data[i] = Math.max(0, Math.min(255, data[i] * factor));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                    }
                }
            }

            applyWhitesBlacks(imageData) {
                if (this.settings.whites === 0 && this.settings.blacks === 0) return;

                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

                    if (luminance > 192) { // Whites
                        const factor = 1 + (this.settings.whites / 100);
                        data[i] = Math.max(0, Math.min(255, data[i] * factor));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                    } else if (luminance < 64) { // Blacks
                        const factor = 1 + (this.settings.blacks / 100);
                        data[i] = Math.max(0, Math.min(255, data[i] * factor));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                    }
                }
            }

            applyClarity(imageData) {
                if (this.settings.clarity === 0) return;

                // Clarity is essentially local contrast enhancement
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const factor = this.settings.clarity / 100;

                // Simple unsharp mask for clarity
                const kernel = [
                    0, -1, 0,
                    -1, 5 + factor, -1,
                    0, -1, 0
                ];

                this.applyConvolution(imageData, kernel);
            }
        }

            applyVibranceSaturation(imageData) {
                if (this.settings.vibrance === 0 && this.settings.saturation === 0) return;

                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    // Calculate saturation level
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    const currentSat = max > 0 ? (max - min) / max : 0;

                    // Apply vibrance (affects less saturated colors more)
                    if (this.settings.vibrance !== 0) {
                        const vibranceFactor = 1 + (this.settings.vibrance / 100) * (1 - currentSat);
                        const gray = 0.299 * r + 0.587 * g + 0.114 * b;

                        data[i] = Math.max(0, Math.min(255, gray + vibranceFactor * (r - gray)));
                        data[i + 1] = Math.max(0, Math.min(255, gray + vibranceFactor * (g - gray)));
                        data[i + 2] = Math.max(0, Math.min(255, gray + vibranceFactor * (b - gray)));
                    }

                    // Apply saturation (affects all colors equally)
                    if (this.settings.saturation !== 0) {
                        const satFactor = 1 + (this.settings.saturation / 100);
                        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];

                        data[i] = Math.max(0, Math.min(255, gray + satFactor * (data[i] - gray)));
                        data[i + 1] = Math.max(0, Math.min(255, gray + satFactor * (data[i + 1] - gray)));
                        data[i + 2] = Math.max(0, Math.min(255, gray + satFactor * (data[i + 2] - gray)));
                    }
                }
            }

            applyTemperatureTint(imageData) {
                if (this.settings.temperature === 0 && this.settings.tint === 0) return;

                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    // Temperature adjustment (blue-orange axis)
                    if (this.settings.temperature !== 0) {
                        const tempFactor = this.settings.temperature / 100;
                        if (tempFactor > 0) { // Warmer
                            data[i] = Math.min(255, data[i] * (1 + tempFactor * 0.3)); // More red
                            data[i + 1] = Math.min(255, data[i + 1] * (1 + tempFactor * 0.1)); // Slightly more green
                            data[i + 2] = Math.max(0, data[i + 2] * (1 - tempFactor * 0.2)); // Less blue
                        } else { // Cooler
                            data[i] = Math.max(0, data[i] * (1 + tempFactor * 0.2)); // Less red
                            data[i + 1] = Math.max(0, data[i + 1] * (1 + tempFactor * 0.1)); // Slightly less green
                            data[i + 2] = Math.min(255, data[i + 2] * (1 - tempFactor * 0.3)); // More blue
                        }
                    }

                    // Tint adjustment (green-magenta axis)
                    if (this.settings.tint !== 0) {
                        const tintFactor = this.settings.tint / 100;
                        if (tintFactor > 0) { // More magenta
                            data[i] = Math.min(255, data[i] * (1 + tintFactor * 0.2)); // More red
                            data[i + 1] = Math.max(0, data[i + 1] * (1 - tintFactor * 0.2)); // Less green
                            data[i + 2] = Math.min(255, data[i + 2] * (1 + tintFactor * 0.2)); // More blue
                        } else { // More green
                            data[i] = Math.max(0, data[i] * (1 + tintFactor * 0.1)); // Slightly less red
                            data[i + 1] = Math.min(255, data[i + 1] * (1 - tintFactor * 0.3)); // More green
                            data[i + 2] = Math.max(0, data[i + 2] * (1 + tintFactor * 0.1)); // Slightly less blue
                        }
                    }
                }
            }

            applyVignette(imageData) {
                if (this.settings.vignette === 0) return;

                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const centerX = width / 2;
                const centerY = height / 2;
                const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
                const intensity = Math.abs(this.settings.vignette) / 100;

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                        const vignetteFactor = this.settings.vignette > 0 ?
                            1 - (distance / maxDistance) * intensity : // Darken edges
                            1 + (distance / maxDistance) * intensity;   // Lighten edges

                        const index = (y * width + x) * 4;
                        data[index] = Math.max(0, Math.min(255, data[index] * vignetteFactor));
                        data[index + 1] = Math.max(0, Math.min(255, data[index + 1] * vignetteFactor));
                        data[index + 2] = Math.max(0, Math.min(255, data[index + 2] * vignetteFactor));
                    }
                }
            }

            applyGrain(imageData) {
                if (this.settings.grain === 0) return;

                const data = imageData.data;
                const intensity = this.settings.grain / 100;

                for (let i = 0; i < data.length; i += 4) {
                    const noise = (Math.random() - 0.5) * intensity * 50;
                    data[i] = Math.max(0, Math.min(255, data[i] + noise));
                    data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise));
                    data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise));
                }
            }

            applyConvolution(imageData, kernel) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const output = new Uint8ClampedArray(data);

                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        for (let channel = 0; channel < 3; channel++) {
                            let sum = 0;
                            for (let ky = -1; ky <= 1; ky++) {
                                for (let kx = -1; kx <= 1; kx++) {
                                    const index = ((y + ky) * width + (x + kx)) * 4 + channel;
                                    const weight = kernel[(ky + 1) * 3 + (kx + 1)];
                                    sum += data[index] * weight;
                                }
                            }
                            const index = (y * width + x) * 4 + channel;
                            output[index] = Math.max(0, Math.min(255, sum));
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            resetSettings() {
                Object.keys(this.settings).forEach(setting => {
                    this.settings[setting] = 0;
                    const slider = document.getElementById(setting);
                    if (slider) {
                        if (setting === 'sharpenRadius') {
                            slider.value = 1;
                            this.settings[setting] = 1;
                        } else {
                            slider.value = 0;
                        }
                        this.updateValueDisplay(setting, slider.value);
                    }
                });
            }
        }

            initializeHistogram() {
                this.histogramCanvas = document.getElementById('histogramCanvas');
                this.histogramCtx = this.histogramCanvas.getContext('2d');
                this.histogramChannel = 'rgb';
            }

            updateHistogram() {
                if (!this.currentImageData) return;

                const data = this.currentImageData.data;
                const histogram = {
                    red: new Array(256).fill(0),
                    green: new Array(256).fill(0),
                    blue: new Array(256).fill(0),
                    luminance: new Array(256).fill(0)
                };

                // Calculate histogram
                for (let i = 0; i < data.length; i += 4) {
                    histogram.red[data[i]]++;
                    histogram.green[data[i + 1]]++;
                    histogram.blue[data[i + 2]]++;

                    const luminance = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
                    histogram.luminance[luminance]++;
                }

                this.drawHistogram(histogram);
            }

            drawHistogram(histogram) {
                const ctx = this.histogramCtx;
                const width = this.histogramCanvas.width;
                const height = this.histogramCanvas.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#1a1a1a';
                ctx.fillRect(0, 0, width, height);

                const maxValue = Math.max(
                    Math.max(...histogram.red),
                    Math.max(...histogram.green),
                    Math.max(...histogram.blue),
                    Math.max(...histogram.luminance)
                );

                if (maxValue === 0) return;

                const barWidth = width / 256;

                if (this.histogramChannel === 'rgb' || this.histogramChannel === 'red') {
                    ctx.fillStyle = this.histogramChannel === 'rgb' ? 'rgba(255, 0, 0, 0.7)' : '#ff4444';
                    for (let i = 0; i < 256; i++) {
                        const barHeight = (histogram.red[i] / maxValue) * height;
                        ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                    }
                }

                if (this.histogramChannel === 'rgb' || this.histogramChannel === 'green') {
                    ctx.fillStyle = this.histogramChannel === 'rgb' ? 'rgba(0, 255, 0, 0.7)' : '#44ff44';
                    for (let i = 0; i < 256; i++) {
                        const barHeight = (histogram.green[i] / maxValue) * height;
                        ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                    }
                }

                if (this.histogramChannel === 'rgb' || this.histogramChannel === 'blue') {
                    ctx.fillStyle = this.histogramChannel === 'rgb' ? 'rgba(0, 0, 255, 0.7)' : '#4444ff';
                    for (let i = 0; i < 256; i++) {
                        const barHeight = (histogram.blue[i] / maxValue) * height;
                        ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                    }
                }
            }

            initializeLayers() {
                this.layers = [{
                    id: 'background',
                    name: 'Background',
                    visible: true,
                    opacity: 100,
                    blendMode: 'normal',
                    imageData: new ImageData(
                        new Uint8ClampedArray(this.originalImageData.data),
                        this.originalImageData.width,
                        this.originalImageData.height
                    )
                }];
                this.updateLayersList();
            }

            updateLayersList() {
                const layersList = document.getElementById('layersList');
                layersList.innerHTML = '';

                this.layers.forEach((layer, index) => {
                    const layerDiv = document.createElement('div');
                    layerDiv.style.cssText = `
                        background: ${index === this.currentLayer ? '#404040' : '#2a2a2a'};
                        border: 1px solid #555;
                        margin: 2px 0;
                        padding: 8px;
                        border-radius: 4px;
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    `;

                    layerDiv.innerHTML = `
                        <div>
                            <div style="font-size: 11px; font-weight: 500;">${layer.name}</div>
                            <div style="font-size: 9px; color: #888;">${layer.blendMode} • ${layer.opacity}%</div>
                        </div>
                        <div>
                            <input type="checkbox" ${layer.visible ? 'checked' : ''}
                                   onchange="editor.toggleLayerVisibility(${index})"
                                   style="margin-left: 8px;">
                        </div>
                    `;

                    layerDiv.addEventListener('click', () => {
                        this.currentLayer = index;
                        this.updateLayersList();
                    });

                    layersList.appendChild(layerDiv);
                });
            }

            toggleLayerVisibility(index) {
                this.layers[index].visible = !this.layers[index].visible;
                this.composeLayers();
            }

            composeLayers() {
                // Composite all visible layers
                const result = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                // Apply layers from bottom to top
                this.layers.forEach(layer => {
                    if (layer.visible && layer.imageData) {
                        this.blendLayer(result, layer);
                    }
                });

                this.ctx.putImageData(result, 0, 0);
                this.currentImageData = result;
                this.updateHistogram();
            }

            blendLayer(base, layer) {
                // Simple normal blend mode for now
                const baseData = base.data;
                const layerData = layer.imageData.data;
                const opacity = layer.opacity / 100;

                for (let i = 0; i < baseData.length; i += 4) {
                    baseData[i] = baseData[i] * (1 - opacity) + layerData[i] * opacity;
                    baseData[i + 1] = baseData[i + 1] * (1 - opacity) + layerData[i + 1] * opacity;
                    baseData[i + 2] = baseData[i + 2] * (1 - opacity) + layerData[i + 2] * opacity;
                }
            }
        }

        // Global functions for UI
        function showHistogramChannel(channel) {
            if (editor) {
                editor.histogramChannel = channel;
                editor.updateHistogram();

                // Update button states
                ['rgb', 'red', 'green', 'blue'].forEach(c => {
                    const btn = document.getElementById('hist' + c.charAt(0).toUpperCase() + c.slice(1));
                    if (btn) {
                        btn.classList.toggle('active', c === channel);
                    }
                });
            }
        }

        function applyPreset(presetName) {
            if (!editor || !editor.originalImageData) return;

            const presets = {
                portrait: { exposure: 0.2, shadows: 20, highlights: -10, clarity: 15, vibrance: 10 },
                landscape: { exposure: 0.1, shadows: 15, highlights: -15, clarity: 25, vibrance: 20, saturation: 5 },
                vintage: { exposure: -0.3, shadows: 30, highlights: -20, temperature: 15, tint: -5, vignette: 30 },
                bw: { saturation: -100, contrast: 15, clarity: 20 },
                dramatic: { exposure: -0.2, shadows: 40, highlights: -30, contrast: 25, clarity: 30, vignette: 20 },
                soft: { exposure: 0.3, shadows: 25, highlights: -5, clarity: -20, vibrance: 5 }
            };

            const preset = presets[presetName];
            if (preset) {
                Object.keys(preset).forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.value = preset[setting];
                        editor.settings[setting] = preset[setting];
                        editor.updateValueDisplay(setting, preset[setting]);
                    }
                });
                editor.applyAdjustments();
            }
        }

        function exportImage() {
            if (!editor || !editor.currentImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const format = document.getElementById('exportFormat').value;
            const quality = document.getElementById('exportQuality').value / 100;

            let mimeType = 'image/png';
            let extension = 'png';

            switch(format) {
                case 'jpeg':
                    mimeType = 'image/jpeg';
                    extension = 'jpg';
                    break;
                case 'webp':
                    mimeType = 'image/webp';
                    extension = 'webp';
                    break;
            }

            const link = document.createElement('a');
            link.download = `enhanced-image.${extension}`;
            link.href = editor.canvas.toDataURL(mimeType, quality);
            link.click();
        }

        // Additional UI functions
        function newProject() {
            if (confirm('Vytvořit nový projekt? Neuložené změny budou ztraceny.')) {
                location.reload();
            }
        }

        function openFile() {
            document.getElementById('fileInput').click();
        }

        function saveProject() {
            if (!editor || !editor.currentImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const projectData = {
                settings: editor.settings,
                layers: editor.layers,
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'image-project.json';
            link.click();
        }

        function showPreferences() {
            alert('Nastavení bude implementováno v další verzi.');
        }

        function flipHorizontal() {
            if (editor && editor.originalImage) {
                // Implementation for horizontal flip
                alert('Horizontální překlopení bude implementováno.');
            }
        }

        function flipVertical() {
            if (editor && editor.originalImage) {
                // Implementation for vertical flip
                alert('Vertikální překlopení bude implementováno.');
            }
        }

        function rotate90() {
            if (editor && editor.originalImage) {
                // Implementation for 90 degree rotation
                alert('Rotace o 90° bude implementována.');
            }
        }

        function straighten() {
            if (editor && editor.originalImage) {
                // Implementation for straightening
                alert('Vyrovnání bude implementováno.');
            }
        }

        function setCropRatio(ratio) {
            // Implementation for crop ratio
            alert(`Crop ratio ${ratio} bude implementován.`);
        }

        function aiUpscale(factor) {
            if (!editor || !editor.originalImage) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            alert(`AI Upscaling ${factor}× bude implementován v další verzi.`);
        }

        function selectHSLChannel(color) {
            // Implementation for HSL channel selection
            alert(`HSL úpravy pro ${color} budou implementovány.`);
        }

        function addLayer() {
            if (editor) {
                const newLayer = {
                    id: 'layer_' + Date.now(),
                    name: `Vrstva ${editor.layers.length + 1}`,
                    visible: true,
                    opacity: 100,
                    blendMode: 'normal',
                    imageData: new ImageData(
                        new Uint8ClampedArray(editor.currentImageData.data),
                        editor.currentImageData.width,
                        editor.currentImageData.height
                    )
                };

                editor.layers.push(newLayer);
                editor.currentLayer = editor.layers.length - 1;
                editor.updateLayersList();
            }
        }

        function duplicateLayer() {
            if (editor && editor.layers[editor.currentLayer]) {
                const currentLayer = editor.layers[editor.currentLayer];
                const duplicatedLayer = {
                    ...currentLayer,
                    id: 'layer_' + Date.now(),
                    name: currentLayer.name + ' kopie',
                    imageData: new ImageData(
                        new Uint8ClampedArray(currentLayer.imageData.data),
                        currentLayer.imageData.width,
                        currentLayer.imageData.height
                    )
                };

                editor.layers.splice(editor.currentLayer + 1, 0, duplicatedLayer);
                editor.currentLayer++;
                editor.updateLayersList();
            }
        }

        function deleteLayer() {
            if (editor && editor.layers.length > 1) {
                if (confirm('Smazat vybranou vrstvu?')) {
                    editor.layers.splice(editor.currentLayer, 1);
                    editor.currentLayer = Math.max(0, editor.currentLayer - 1);
                    editor.updateLayersList();
                    editor.composeLayers();
                }
            } else {
                alert('Nelze smazat poslední vrstvu.');
            }
        }

        function mergeDown() {
            if (editor && editor.currentLayer > 0) {
                // Implementation for merge down
                alert('Sloučení vrstev bude implementováno.');
            }
        }

        // Initialize the editor
        let editor;
        document.addEventListener('DOMContentLoaded', () => {
            editor = new ImageEditor();

            // Set initial histogram channel
            showHistogramChannel('rgb');
        });
    </script>
</body>
</html>