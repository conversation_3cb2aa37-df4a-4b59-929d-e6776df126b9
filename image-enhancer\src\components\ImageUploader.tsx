'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, Image as ImageIcon, X, FileImage } from 'lucide-react'
import { getImageInfo } from '@/utils/imageUtils'
import { ImageData } from '@/types/image'

interface ImageUploaderProps {
  onImageSelect: (imageData: ImageData) => void
  selectedImage?: ImageData | null
}

export default function ImageUploader({ onImageSelect, selectedImage }: ImageUploaderProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setIsLoading(true)
    setError(null)

    try {
      // Kontrola velikosti souboru (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('So<PERSON>or je <PERSON><PERSON><PERSON><PERSON> velk<PERSON>. Maximum je 10MB.')
      }

      // Kontrola typu souboru
      if (!file.type.startsWith('image/')) {
        throw new Error('Podporovány jsou pouze obrázky.')
      }

      const imageInfo = await getImageInfo(file)
      const imageData: ImageData = {
        id: Date.now().toString(),
        name: file.name,
        originalFile: file,
        originalUrl: imageInfo.originalUrl!,
        width: imageInfo.width!,
        height: imageInfo.height!,
        size: file.size,
        format: file.type,
      }

      onImageSelect(imageData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Chyba při načítání obrázku')
    } finally {
      setIsLoading(false)
    }
  }, [onImageSelect])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.bmp', '.tiff']
    },
    multiple: false,
    disabled: isLoading
  })

  const clearImage = () => {
    setError(null)
    // Můžeme přidat callback pro vymazání obrázku
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700/50'
          }
          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-spin mx-auto w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <p className="text-gray-600 dark:text-gray-300">Načítání obrázku...</p>
          </div>
        ) : (
          <div className="space-y-4">
            <Upload className="mx-auto text-gray-400" size={48} />
            <div>
              <p className="text-lg font-medium text-gray-700 dark:text-gray-200">
                {isDragActive ? 'Pusťte obrázek zde' : 'Klikněte nebo přetáhněte obrázek'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                PNG, JPG, WEBP, BMP, TIFF až 10MB
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <X className="text-red-500 mr-2" size={16} />
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Selected Image Info */}
      {selectedImage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <FileImage className="text-green-600 dark:text-green-400 mt-1" size={20} />
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-200 truncate">
                {selectedImage.name}
              </h4>
              <div className="mt-1 text-xs text-green-600 dark:text-green-400 space-y-1">
                <div>Rozměry: {selectedImage.width} × {selectedImage.height} px</div>
                <div>Velikost: {formatFileSize(selectedImage.size)}</div>
                <div>Formát: {selectedImage.format}</div>
              </div>
            </div>
            <button
              onClick={clearImage}
              className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
