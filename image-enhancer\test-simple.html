<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Enhancer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .upload-area {
            border: 2px dashed #fff;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        button:hover {
            background: #45a049;
        }
        #canvas {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .preview-area {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Image Enhancer Pro - Test</h1>
        <p>Jednoduchý test základních funkcí pro úpravu obrázků</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div style="font-size: 48px; margin-bottom: 10px;">📁</div>
            <p>Klikněte pro nahrání obrázku</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Jas: <span id="brightnessValue">0</span></label>
                <input type="range" id="brightness" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Kontrast: <span id="contrastValue">0</span></label>
                <input type="range" id="contrast" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Sytost: <span id="saturationValue">0</span></label>
                <input type="range" id="saturation" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Šířka (px):</label>
                <input type="number" id="width" min="1" max="5000" placeholder="Auto">
                <label>Výška (px):</label>
                <input type="number" id="height" min="1" max="5000" placeholder="Auto">
                <label>
                    <input type="checkbox" id="maintainAspect" checked> Zachovat poměr stran
                </label>
            </div>
            <div class="control-group">
                <label>Rotace: <span id="rotationValue">0</span>°</label>
                <input type="range" id="rotation" min="0" max="360" value="0">
                <button onclick="flipHorizontal()">↔️ Překlopit H</button>
                <button onclick="flipVertical()">↕️ Překlopit V</button>
            </div>
            <div class="control-group">
                <button onclick="resetFilters()">🔄 Reset</button>
                <button onclick="resizeImage()">📏 Změnit velikost</button>
                <button onclick="downloadImage()">💾 Stáhnout</button>
            </div>
        </div>
        
        <div class="preview-area">
            <canvas id="canvas" style="display: none;"></canvas>
            <div id="placeholder" style="padding: 40px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <div style="font-size: 64px; margin-bottom: 10px;">🖼️</div>
                <p>Nahrajte obrázek pro začátek</p>
            </div>
        </div>
    </div>

    <script>
        let originalImageData = null;
        let originalImage = null;
        let canvas = document.getElementById('canvas');
        let ctx = canvas.getContext('2d');

        // Upload handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        originalImage = img;
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);
                        originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                        // Update size inputs
                        document.getElementById('width').value = img.width;
                        document.getElementById('height').value = img.height;

                        document.getElementById('placeholder').style.display = 'none';
                        canvas.style.display = 'block';

                        applyFilters();
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Filter controls
        ['brightness', 'contrast', 'saturation', 'rotation'].forEach(filter => {
            const slider = document.getElementById(filter);
            const valueSpan = document.getElementById(filter + 'Value');

            slider.addEventListener('input', function() {
                valueSpan.textContent = this.value;
                if (filter === 'rotation') {
                    applyTransformations();
                } else {
                    applyFilters();
                }
            });
        });
        
        function applyFilters() {
            if (!originalImageData) return;
            
            const brightness = parseInt(document.getElementById('brightness').value);
            const contrast = parseInt(document.getElementById('contrast').value);
            const saturation = parseInt(document.getElementById('saturation').value);
            
            // Copy original data
            const imageData = new ImageData(
                new Uint8ClampedArray(originalImageData.data),
                originalImageData.width,
                originalImageData.height
            );
            
            const data = imageData.data;
            
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i];
                let g = data[i + 1];
                let b = data[i + 2];
                
                // Apply brightness
                r += brightness * 2.55;
                g += brightness * 2.55;
                b += brightness * 2.55;
                
                // Apply contrast
                const contrastFactor = (259 * (contrast + 255)) / (255 * (259 - contrast));
                r = contrastFactor * (r - 128) + 128;
                g = contrastFactor * (g - 128) + 128;
                b = contrastFactor * (b - 128) + 128;
                
                // Apply saturation
                const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                const satFactor = (saturation + 100) / 100;
                r = gray + satFactor * (r - gray);
                g = gray + satFactor * (g - gray);
                b = gray + satFactor * (b - gray);
                
                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
            }
            
            ctx.putImageData(imageData, 0, 0);
        }
        
        function applyTransformations() {
            if (!originalImage) return;

            const rotation = parseInt(document.getElementById('rotation').value);

            // Calculate new canvas size for rotation
            const radians = (rotation * Math.PI) / 180;
            const cos = Math.abs(Math.cos(radians));
            const sin = Math.abs(Math.sin(radians));
            const newWidth = originalImage.width * cos + originalImage.height * sin;
            const newHeight = originalImage.width * sin + originalImage.height * cos;

            canvas.width = newWidth;
            canvas.height = newHeight;

            // Clear and apply transformations
            ctx.clearRect(0, 0, newWidth, newHeight);
            ctx.save();

            // Move to center
            ctx.translate(newWidth / 2, newHeight / 2);

            // Apply rotation
            ctx.rotate(radians);

            // Draw image centered
            ctx.drawImage(originalImage, -originalImage.width / 2, -originalImage.height / 2);

            ctx.restore();

            // Update original data
            originalImageData = ctx.getImageData(0, 0, newWidth, newHeight);

            // Apply filters
            applyFilters();
        }

        function flipHorizontal() {
            if (!originalImage) return;

            canvas.width = originalImage.width;
            canvas.height = originalImage.height;

            ctx.save();
            ctx.scale(-1, 1);
            ctx.drawImage(originalImage, -originalImage.width, 0);
            ctx.restore();

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyFilters();
        }

        function flipVertical() {
            if (!originalImage) return;

            canvas.width = originalImage.width;
            canvas.height = originalImage.height;

            ctx.save();
            ctx.scale(1, -1);
            ctx.drawImage(originalImage, 0, -originalImage.height);
            ctx.restore();

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyFilters();
        }

        function resetFilters() {
            document.getElementById('brightness').value = 0;
            document.getElementById('contrast').value = 0;
            document.getElementById('saturation').value = 0;
            document.getElementById('rotation').value = 0;

            document.getElementById('brightnessValue').textContent = '0';
            document.getElementById('contrastValue').textContent = '0';
            document.getElementById('saturationValue').textContent = '0';
            document.getElementById('rotationValue').textContent = '0';

            // Reset to original image
            if (originalImage) {
                canvas.width = originalImage.width;
                canvas.height = originalImage.height;
                ctx.drawImage(originalImage, 0, 0);
                originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                document.getElementById('width').value = originalImage.width;
                document.getElementById('height').value = originalImage.height;
            }

            applyFilters();
        }
        
        function resizeImage() {
            if (!originalImage) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            let newWidth = parseInt(document.getElementById('width').value) || originalImage.width;
            let newHeight = parseInt(document.getElementById('height').value) || originalImage.height;
            const maintainAspect = document.getElementById('maintainAspect').checked;

            if (maintainAspect) {
                const aspectRatio = originalImage.width / originalImage.height;
                if (newWidth && !newHeight) {
                    newHeight = Math.round(newWidth / aspectRatio);
                } else if (newHeight && !newWidth) {
                    newWidth = Math.round(newHeight * aspectRatio);
                } else if (newWidth && newHeight) {
                    // Adjust to maintain aspect ratio
                    if (newWidth / newHeight > aspectRatio) {
                        newWidth = Math.round(newHeight * aspectRatio);
                    } else {
                        newHeight = Math.round(newWidth / aspectRatio);
                    }
                }
            }

            // Update canvas size
            canvas.width = newWidth;
            canvas.height = newHeight;

            // Draw resized image
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(originalImage, 0, 0, newWidth, newHeight);

            // Update original data for filters
            originalImageData = ctx.getImageData(0, 0, newWidth, newHeight);

            // Update input values
            document.getElementById('width').value = newWidth;
            document.getElementById('height').value = newHeight;

            // Apply current filters
            applyFilters();
        }

        // Aspect ratio maintenance
        document.getElementById('width').addEventListener('input', function() {
            if (document.getElementById('maintainAspect').checked && originalImage) {
                const aspectRatio = originalImage.width / originalImage.height;
                const newWidth = parseInt(this.value);
                if (newWidth) {
                    document.getElementById('height').value = Math.round(newWidth / aspectRatio);
                }
            }
        });

        document.getElementById('height').addEventListener('input', function() {
            if (document.getElementById('maintainAspect').checked && originalImage) {
                const aspectRatio = originalImage.width / originalImage.height;
                const newHeight = parseInt(this.value);
                if (newHeight) {
                    document.getElementById('width').value = Math.round(newHeight * aspectRatio);
                }
            }
        });

        function downloadImage() {
            if (!originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const link = document.createElement('a');
            link.download = 'enhanced-image.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
