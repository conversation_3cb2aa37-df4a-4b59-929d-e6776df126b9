<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Enhancer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .upload-area {
            border: 2px dashed #fff;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        button:hover {
            background: #45a049;
        }
        #canvas {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .preview-area {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Image Enhancer Pro - Test</h1>
        <p>Jednoduchý test základních funkcí pro úpravu obrázků</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div style="font-size: 48px; margin-bottom: 10px;">📁</div>
            <p>Klikněte pro nahrání obrázku</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Jas: <span id="brightnessValue">0</span></label>
                <input type="range" id="brightness" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Kontrast: <span id="contrastValue">0</span></label>
                <input type="range" id="contrast" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Sytost: <span id="saturationValue">0</span></label>
                <input type="range" id="saturation" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Šířka (px):</label>
                <input type="number" id="width" min="1" max="5000" placeholder="Auto">
                <label>Výška (px):</label>
                <input type="number" id="height" min="1" max="5000" placeholder="Auto">
                <label>
                    <input type="checkbox" id="maintainAspect" checked> Zachovat poměr stran
                </label>
            </div>
            <div class="control-group">
                <label>Rotace: <span id="rotationValue">0</span>°</label>
                <input type="range" id="rotation" min="0" max="360" value="0">
                <button onclick="flipHorizontal()">↔️ Překlopit H</button>
                <button onclick="flipVertical()">↕️ Překlopit V</button>
            </div>
            <div class="control-group">
                <label>Zaostření: <span id="sharpnessValue">0</span></label>
                <input type="range" id="sharpness" min="0" max="200" value="0">
                <label>Rozostření: <span id="blurValue">0</span></label>
                <input type="range" id="blur" min="0" max="20" value="0">
            </div>
            <div class="control-group">
                <label>Gamma: <span id="gammaValue">1.0</span></label>
                <input type="range" id="gamma" min="0.1" max="3.0" step="0.1" value="1.0">
                <label>Expozice: <span id="exposureValue">0</span></label>
                <input type="range" id="exposure" min="-2" max="2" step="0.1" value="0">
            </div>
            <div class="control-group">
                <label>Stíny: <span id="shadowsValue">0</span></label>
                <input type="range" id="shadows" min="-100" max="100" value="0">
                <label>Světla: <span id="highlightsValue">0</span></label>
                <input type="range" id="highlights" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Efekty:</label>
                <button onclick="applySepia()">🟤 Sepia</button>
                <button onclick="applyGrayscale()">⚫ Černobílá</button>
                <button onclick="applyVintage()">📸 Vintage</button>
                <button onclick="applyNoise()">📺 Šum</button>
            </div>
            <div class="control-group">
                <label>Viněta: <span id="vignetteValue">0</span></label>
                <input type="range" id="vignette" min="0" max="100" value="0">
                <label>Pixelace: <span id="pixelateValue">0</span></label>
                <input type="range" id="pixelate" min="0" max="20" value="0">
            </div>
            <div class="control-group">
                <label>AI Upscaling:</label>
                <button onclick="aiUpscale(2)">🔍 2× Zvětšit</button>
                <button onclick="aiUpscale(4)">🔍 4× Zvětšit</button>
                <button onclick="smartEnhance()">✨ Smart Enhance</button>
                <button onclick="denoiseImage()">🧹 Odstranit šum</button>
            </div>
            <div class="control-group">
                <label>Presety pro potisk:</label>
                <button onclick="applyPreset('tshirt')">👕 Tričko</button>
                <button onclick="applyPreset('poster')">📄 Plakát</button>
                <button onclick="applyPreset('business')">💼 Vizitka</button>
                <button onclick="applyPreset('canvas')">🖼️ Plátno</button>
            </div>
            <div class="control-group">
                <label>Vrstvy:</label>
                <button onclick="saveLayer()">💾 Uložit vrstvu</button>
                <button onclick="loadLayer()">📂 Načíst vrstvu</button>
                <button onclick="toggleLayerPanel()">📋 Panel vrstev</button>
            </div>
            <div class="control-group">
                <label>Batch zpracování:</label>
                <input type="file" id="batchFiles" multiple accept="image/*" style="display: none;">
                <button onclick="document.getElementById('batchFiles').click()">📁 Vybrat více obrázků</button>
                <button onclick="processBatch()">⚡ Zpracovat dávku</button>
            </div>
            <div class="control-group">
                <button onclick="resetFilters()">🔄 Reset</button>
                <button onclick="resizeImage()">📏 Změnit velikost</button>
                <button onclick="downloadImage()">💾 Stáhnout</button>
            </div>
        </div>
        
        <div class="preview-area">
            <canvas id="canvas" style="display: none;"></canvas>
            <div id="placeholder" style="padding: 40px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <div style="font-size: 64px; margin-bottom: 10px;">🖼️</div>
                <p>Nahrajte obrázek pro začátek</p>
            </div>
        </div>

        <div id="imageInfo" style="display: none; margin-top: 20px; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
            <h3>📊 Informace o obrázku</h3>
            <div id="infoContent"></div>
        </div>

        <div id="layerPanel" style="display: none; margin-top: 20px; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
            <h3>📋 Panel vrstev</h3>
            <div id="layerList" style="max-height: 200px; overflow-y: auto;"></div>
            <div style="margin-top: 10px;">
                <button onclick="addAdjustmentLayer()">➕ Přidat vrstvu úprav</button>
                <button onclick="clearLayers()">🗑️ Vymazat vrstvy</button>
            </div>
        </div>
    </div>

    <script>
        let originalImageData = null;
        let originalImage = null;
        let canvas = document.getElementById('canvas');
        let ctx = canvas.getContext('2d');
        let layers = [];
        let currentLayerIndex = -1;

        // Upload handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        originalImage = img;
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);
                        originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                        // Update size inputs
                        document.getElementById('width').value = img.width;
                        document.getElementById('height').value = img.height;

                        document.getElementById('placeholder').style.display = 'none';
                        canvas.style.display = 'block';

                        // Show image info
                        showImageInfo(file, img);

                        applyFilters();
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Filter controls
        ['brightness', 'contrast', 'saturation', 'rotation', 'sharpness', 'blur', 'gamma', 'exposure', 'shadows', 'highlights', 'vignette', 'pixelate'].forEach(filter => {
            const slider = document.getElementById(filter);
            const valueSpan = document.getElementById(filter + 'Value');

            slider.addEventListener('input', function() {
                valueSpan.textContent = this.value;
                if (filter === 'rotation') {
                    applyTransformations();
                } else {
                    applyFilters();
                }
            });
        });
        
        function applyFilters() {
            if (!originalImageData) return;

            const brightness = parseInt(document.getElementById('brightness').value);
            const contrast = parseInt(document.getElementById('contrast').value);
            const saturation = parseInt(document.getElementById('saturation').value);
            const sharpness = parseInt(document.getElementById('sharpness').value);
            const blur = parseInt(document.getElementById('blur').value);
            const gamma = parseFloat(document.getElementById('gamma').value);
            const exposure = parseFloat(document.getElementById('exposure').value);
            const shadows = parseInt(document.getElementById('shadows').value);
            const highlights = parseInt(document.getElementById('highlights').value);
            const vignette = parseInt(document.getElementById('vignette').value);
            const pixelate = parseInt(document.getElementById('pixelate').value);

            // Copy original data
            const imageData = new ImageData(
                new Uint8ClampedArray(originalImageData.data),
                originalImageData.width,
                originalImageData.height
            );

            const data = imageData.data;
            const width = imageData.width;
            const height = imageData.height;

            for (let i = 0; i < data.length; i += 4) {
                let r = data[i];
                let g = data[i + 1];
                let b = data[i + 2];

                // Apply gamma correction
                r = 255 * Math.pow(r / 255, 1 / gamma);
                g = 255 * Math.pow(g / 255, 1 / gamma);
                b = 255 * Math.pow(b / 255, 1 / gamma);

                // Apply exposure
                const exposureFactor = Math.pow(2, exposure);
                r *= exposureFactor;
                g *= exposureFactor;
                b *= exposureFactor;

                // Apply brightness
                r += brightness * 2.55;
                g += brightness * 2.55;
                b += brightness * 2.55;

                // Apply contrast
                const contrastFactor = (259 * (contrast + 255)) / (255 * (259 - contrast));
                r = contrastFactor * (r - 128) + 128;
                g = contrastFactor * (g - 128) + 128;
                b = contrastFactor * (b - 128) + 128;

                // Apply shadows/highlights
                const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                if (luminance < 128) { // Shadows
                    const shadowFactor = (shadows + 100) / 100;
                    r = r * shadowFactor;
                    g = g * shadowFactor;
                    b = b * shadowFactor;
                } else { // Highlights
                    const highlightFactor = (100 - highlights) / 100;
                    r = 255 - (255 - r) * highlightFactor;
                    g = 255 - (255 - g) * highlightFactor;
                    b = 255 - (255 - b) * highlightFactor;
                }

                // Apply saturation
                const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                const satFactor = (saturation + 100) / 100;
                r = gray + satFactor * (r - gray);
                g = gray + satFactor * (g - gray);
                b = gray + satFactor * (b - gray);

                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
            }

            // Apply vignette effect
            if (vignette > 0) {
                applyVignetteEffect(data, width, height, vignette);
            }

            // Apply pixelate effect
            if (pixelate > 0) {
                applyPixelateEffect(data, width, height, pixelate);
            }

            ctx.putImageData(imageData, 0, 0);

            // Apply blur (using canvas filter)
            if (blur > 0) {
                ctx.filter = `blur(${blur}px)`;
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = width;
                tempCanvas.height = height;
                const tempCtx = tempCanvas.getContext('2d');
                tempCtx.drawImage(canvas, 0, 0);
                ctx.filter = 'none';
                ctx.clearRect(0, 0, width, height);
                ctx.drawImage(tempCanvas, 0, 0);
            }

            // Apply sharpening
            if (sharpness > 0) {
                applySharpening(sharpness);
            }
        }
        
        function applyTransformations() {
            if (!originalImage) return;

            const rotation = parseInt(document.getElementById('rotation').value);

            // Calculate new canvas size for rotation
            const radians = (rotation * Math.PI) / 180;
            const cos = Math.abs(Math.cos(radians));
            const sin = Math.abs(Math.sin(radians));
            const newWidth = originalImage.width * cos + originalImage.height * sin;
            const newHeight = originalImage.width * sin + originalImage.height * cos;

            canvas.width = newWidth;
            canvas.height = newHeight;

            // Clear and apply transformations
            ctx.clearRect(0, 0, newWidth, newHeight);
            ctx.save();

            // Move to center
            ctx.translate(newWidth / 2, newHeight / 2);

            // Apply rotation
            ctx.rotate(radians);

            // Draw image centered
            ctx.drawImage(originalImage, -originalImage.width / 2, -originalImage.height / 2);

            ctx.restore();

            // Update original data
            originalImageData = ctx.getImageData(0, 0, newWidth, newHeight);

            // Apply filters
            applyFilters();
        }

        function flipHorizontal() {
            if (!originalImage) return;

            canvas.width = originalImage.width;
            canvas.height = originalImage.height;

            ctx.save();
            ctx.scale(-1, 1);
            ctx.drawImage(originalImage, -originalImage.width, 0);
            ctx.restore();

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyFilters();
        }

        function flipVertical() {
            if (!originalImage) return;

            canvas.width = originalImage.width;
            canvas.height = originalImage.height;

            ctx.save();
            ctx.scale(1, -1);
            ctx.drawImage(originalImage, 0, -originalImage.height);
            ctx.restore();

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            applyFilters();
        }

        function applyVignetteEffect(data, width, height, intensity) {
            const centerX = width / 2;
            const centerY = height / 2;
            const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);

            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                    const vignetteFactor = 1 - (distance / maxDistance) * (intensity / 100);

                    const index = (y * width + x) * 4;
                    data[index] *= Math.max(0, vignetteFactor);     // R
                    data[index + 1] *= Math.max(0, vignetteFactor); // G
                    data[index + 2] *= Math.max(0, vignetteFactor); // B
                }
            }
        }

        function applyPixelateEffect(data, width, height, size) {
            if (size <= 1) return;

            for (let y = 0; y < height; y += size) {
                for (let x = 0; x < width; x += size) {
                    let r = 0, g = 0, b = 0, count = 0;

                    // Calculate average color in block
                    for (let dy = 0; dy < size && y + dy < height; dy++) {
                        for (let dx = 0; dx < size && x + dx < width; dx++) {
                            const index = ((y + dy) * width + (x + dx)) * 4;
                            r += data[index];
                            g += data[index + 1];
                            b += data[index + 2];
                            count++;
                        }
                    }

                    r /= count;
                    g /= count;
                    b /= count;

                    // Apply average color to entire block
                    for (let dy = 0; dy < size && y + dy < height; dy++) {
                        for (let dx = 0; dx < size && x + dx < width; dx++) {
                            const index = ((y + dy) * width + (x + dx)) * 4;
                            data[index] = r;
                            data[index + 1] = g;
                            data[index + 2] = b;
                        }
                    }
                }
            }
        }

        function applySharpening(intensity) {
            const kernel = [
                0, -1, 0,
                -1, 5 + intensity / 50, -1,
                0, -1, 0
            ];
            applyConvolution(kernel);
        }

        function applyConvolution(kernel) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const width = canvas.width;
            const height = canvas.height;
            const output = new Uint8ClampedArray(data);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let r = 0, g = 0, b = 0;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const index = ((y + ky) * width + (x + kx)) * 4;
                            const weight = kernel[(ky + 1) * 3 + (kx + 1)];

                            r += data[index] * weight;
                            g += data[index + 1] * weight;
                            b += data[index + 2] * weight;
                        }
                    }

                    const index = (y * width + x) * 4;
                    output[index] = Math.max(0, Math.min(255, r));
                    output[index + 1] = Math.max(0, Math.min(255, g));
                    output[index + 2] = Math.max(0, Math.min(255, b));
                }
            }

            const newImageData = new ImageData(output, width, height);
            ctx.putImageData(newImageData, 0, 0);
        }

        function resetFilters() {
            const controls = ['brightness', 'contrast', 'saturation', 'rotation', 'sharpness', 'blur', 'gamma', 'exposure', 'shadows', 'highlights', 'vignette', 'pixelate'];
            const defaults = [0, 0, 0, 0, 0, 0, 1.0, 0, 0, 0, 0, 0];

            controls.forEach((control, index) => {
                document.getElementById(control).value = defaults[index];
                document.getElementById(control + 'Value').textContent = defaults[index];
            });

            // Reset to original image
            if (originalImage) {
                canvas.width = originalImage.width;
                canvas.height = originalImage.height;
                ctx.drawImage(originalImage, 0, 0);
                originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                document.getElementById('width').value = originalImage.width;
                document.getElementById('height').value = originalImage.height;
            }

            applyFilters();
        }
        
        function resizeImage() {
            if (!originalImage) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            let newWidth = parseInt(document.getElementById('width').value) || originalImage.width;
            let newHeight = parseInt(document.getElementById('height').value) || originalImage.height;
            const maintainAspect = document.getElementById('maintainAspect').checked;

            if (maintainAspect) {
                const aspectRatio = originalImage.width / originalImage.height;
                if (newWidth && !newHeight) {
                    newHeight = Math.round(newWidth / aspectRatio);
                } else if (newHeight && !newWidth) {
                    newWidth = Math.round(newHeight * aspectRatio);
                } else if (newWidth && newHeight) {
                    // Adjust to maintain aspect ratio
                    if (newWidth / newHeight > aspectRatio) {
                        newWidth = Math.round(newHeight * aspectRatio);
                    } else {
                        newHeight = Math.round(newWidth / aspectRatio);
                    }
                }
            }

            // Update canvas size
            canvas.width = newWidth;
            canvas.height = newHeight;

            // Draw resized image
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(originalImage, 0, 0, newWidth, newHeight);

            // Update original data for filters
            originalImageData = ctx.getImageData(0, 0, newWidth, newHeight);

            // Update input values
            document.getElementById('width').value = newWidth;
            document.getElementById('height').value = newHeight;

            // Apply current filters
            applyFilters();
        }

        // Aspect ratio maintenance
        document.getElementById('width').addEventListener('input', function() {
            if (document.getElementById('maintainAspect').checked && originalImage) {
                const aspectRatio = originalImage.width / originalImage.height;
                const newWidth = parseInt(this.value);
                if (newWidth) {
                    document.getElementById('height').value = Math.round(newWidth / aspectRatio);
                }
            }
        });

        document.getElementById('height').addEventListener('input', function() {
            if (document.getElementById('maintainAspect').checked && originalImage) {
                const aspectRatio = originalImage.width / originalImage.height;
                const newHeight = parseInt(this.value);
                if (newHeight) {
                    document.getElementById('width').value = Math.round(newHeight * aspectRatio);
                }
            }
        });

        function applySepia() {
            if (!originalImageData) return;

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                data[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
                data[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
                data[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function applyGrayscale() {
            if (!originalImageData) return;

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                data[i] = gray;
                data[i + 1] = gray;
                data[i + 2] = gray;
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function applyVintage() {
            if (!originalImageData) return;

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                // Vintage effect: warm tones, reduced contrast
                data[i] = Math.min(255, data[i] * 1.1 + 20);     // More red
                data[i + 1] = Math.min(255, data[i + 1] * 1.05 + 10); // Slightly more green
                data[i + 2] = Math.max(0, data[i + 2] * 0.9 - 10);    // Less blue
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function applyNoise() {
            if (!originalImageData) return;

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const noise = (Math.random() - 0.5) * 50;
                data[i] = Math.max(0, Math.min(255, data[i] + noise));
                data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise));
                data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise));
            }

            ctx.putImageData(imageData, 0, 0);
        }

        function applyPreset(type) {
            if (!originalImage) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const presets = {
                tshirt: {
                    contrast: 10, saturation: 15, sharpness: 120,
                    dpi: 300, maxWidth: 3000, maxHeight: 3000,
                    description: 'Optimalizováno pro potisk na trička'
                },
                poster: {
                    contrast: 8, saturation: 10, sharpness: 100, brightness: 2,
                    dpi: 150, maxWidth: 5000, maxHeight: 7000,
                    description: 'Optimalizováno pro plakáty'
                },
                business: {
                    contrast: 12, saturation: 8, sharpness: 150,
                    dpi: 600, maxWidth: 2100, maxHeight: 1400,
                    description: 'Optimalizováno pro vizitky'
                },
                canvas: {
                    contrast: 5, saturation: 12, sharpness: 110, gamma: 1.05,
                    dpi: 200, maxWidth: 4000, maxHeight: 4000,
                    description: 'Optimalizováno pro potisk na plátno'
                }
            };

            const preset = presets[type];
            if (!preset) return;

            // Apply preset values
            Object.keys(preset).forEach(key => {
                if (key !== 'dpi' && key !== 'maxWidth' && key !== 'maxHeight' && key !== 'description') {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = preset[key];
                        document.getElementById(key + 'Value').textContent = preset[key];
                    }
                }
            });

            // Resize if needed
            let newWidth = originalImage.width;
            let newHeight = originalImage.height;

            if (newWidth > preset.maxWidth || newHeight > preset.maxHeight) {
                const aspectRatio = newWidth / newHeight;
                if (newWidth > preset.maxWidth) {
                    newWidth = preset.maxWidth;
                    newHeight = newWidth / aspectRatio;
                }
                if (newHeight > preset.maxHeight) {
                    newHeight = preset.maxHeight;
                    newWidth = newHeight * aspectRatio;
                }

                document.getElementById('width').value = Math.round(newWidth);
                document.getElementById('height').value = Math.round(newHeight);
                resizeImage();
            }

            applyFilters();

            alert(`Preset "${type}" aplikován!\n${preset.description}\nDoporučené DPI: ${preset.dpi}`);
        }

        function showImageInfo(file, img) {
            const formatFileSize = (bytes) => {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            };

            const aspectRatio = (img.width / img.height).toFixed(2);
            const megapixels = ((img.width * img.height) / 1000000).toFixed(1);

            const info = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    <div><strong>Název:</strong> ${file.name}</div>
                    <div><strong>Rozměry:</strong> ${img.width} × ${img.height} px</div>
                    <div><strong>Velikost:</strong> ${formatFileSize(file.size)}</div>
                    <div><strong>Formát:</strong> ${file.type}</div>
                    <div><strong>Poměr stran:</strong> ${aspectRatio}:1</div>
                    <div><strong>Megapixely:</strong> ${megapixels} MP</div>
                    <div><strong>Poslední úprava:</strong> ${new Date(file.lastModified).toLocaleDateString('cs-CZ')}</div>
                    <div><strong>Barevná hloubka:</strong> 24-bit RGB</div>
                </div>
            `;

            document.getElementById('infoContent').innerHTML = info;
            document.getElementById('imageInfo').style.display = 'block';
        }

        function aiUpscale(factor) {
            if (!originalImage) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const newWidth = originalImage.width * factor;
            const newHeight = originalImage.height * factor;

            if (newWidth > 8000 || newHeight > 8000) {
                alert('Výsledný obrázek by byl příliš velký! Maximum je 8000×8000px.');
                return;
            }

            // Show progress
            const progressDiv = document.createElement('div');
            progressDiv.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 10px; z-index: 1000;';
            progressDiv.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 24px; margin-bottom: 10px;">🤖 AI Upscaling</div>
                    <div>Zvětšování ${factor}× (${originalImage.width}×${originalImage.height} → ${newWidth}×${newHeight})</div>
                    <div style="margin: 10px 0;">
                        <div style="width: 200px; height: 10px; background: #333; border-radius: 5px; overflow: hidden;">
                            <div id="progressBar" style="width: 0%; height: 100%; background: linear-gradient(90deg, #4CAF50, #45a049); transition: width 0.3s;"></div>
                        </div>
                    </div>
                    <div id="progressText">Příprava...</div>
                </div>
            `;
            document.body.appendChild(progressDiv);

            setTimeout(() => {
                try {
                    // Advanced bicubic interpolation
                    const result = bicubicUpscale(originalImage, factor, (progress) => {
                        document.getElementById('progressBar').style.width = progress + '%';
                        document.getElementById('progressText').textContent = `Zpracování: ${Math.round(progress)}%`;
                    });

                    canvas.width = newWidth;
                    canvas.height = newHeight;
                    ctx.putImageData(result, 0, 0);

                    // Update original data
                    originalImageData = ctx.getImageData(0, 0, newWidth, newHeight);

                    // Update size inputs
                    document.getElementById('width').value = newWidth;
                    document.getElementById('height').value = newHeight;

                    document.body.removeChild(progressDiv);
                    alert(`AI Upscaling dokončen!\nNové rozměry: ${newWidth}×${newHeight}px`);

                } catch (error) {
                    document.body.removeChild(progressDiv);
                    alert('Chyba při AI upscalingu: ' + error.message);
                }
            }, 100);
        }

        function bicubicUpscale(img, factor, progressCallback) {
            const srcCanvas = document.createElement('canvas');
            const srcCtx = srcCanvas.getContext('2d');
            srcCanvas.width = img.width;
            srcCanvas.height = img.height;
            srcCtx.drawImage(img, 0, 0);

            const srcData = srcCtx.getImageData(0, 0, img.width, img.height);
            const newWidth = img.width * factor;
            const newHeight = img.height * factor;
            const newData = new ImageData(newWidth, newHeight);

            // Bicubic interpolation function
            function bicubicInterpolate(p, x) {
                return p[1] + 0.5 * x * (p[2] - p[0] + x * (2.0 * p[0] - 5.0 * p[1] + 4.0 * p[2] - p[3] + x * (3.0 * (p[1] - p[2]) + p[3] - p[0])));
            }

            function getPixel(data, width, height, x, y, channel) {
                x = Math.max(0, Math.min(width - 1, Math.floor(x)));
                y = Math.max(0, Math.min(height - 1, Math.floor(y)));
                return data.data[(y * width + x) * 4 + channel];
            }

            const totalPixels = newWidth * newHeight;
            let processedPixels = 0;

            for (let y = 0; y < newHeight; y++) {
                for (let x = 0; x < newWidth; x++) {
                    const srcX = x / factor;
                    const srcY = y / factor;

                    // Get surrounding 4x4 pixel grid
                    const pixels = [];
                    for (let dy = -1; dy <= 2; dy++) {
                        const row = [];
                        for (let dx = -1; dx <= 2; dx++) {
                            const px = srcX + dx;
                            const py = srcY + dy;
                            row.push([
                                getPixel(srcData, img.width, img.height, px, py, 0), // R
                                getPixel(srcData, img.width, img.height, px, py, 1), // G
                                getPixel(srcData, img.width, img.height, px, py, 2), // B
                                getPixel(srcData, img.width, img.height, px, py, 3)  // A
                            ]);
                        }
                        pixels.push(row);
                    }

                    // Bicubic interpolation for each channel
                    const fx = srcX - Math.floor(srcX);
                    const fy = srcY - Math.floor(srcY);

                    const result = [0, 0, 0, 255]; // RGBA

                    for (let channel = 0; channel < 3; channel++) {
                        const col = [];
                        for (let i = 0; i < 4; i++) {
                            const row = [pixels[i][0][channel], pixels[i][1][channel], pixels[i][2][channel], pixels[i][3][channel]];
                            col.push(bicubicInterpolate(row, fx));
                        }
                        result[channel] = Math.max(0, Math.min(255, bicubicInterpolate(col, fy)));
                    }

                    const index = (y * newWidth + x) * 4;
                    newData.data[index] = result[0];
                    newData.data[index + 1] = result[1];
                    newData.data[index + 2] = result[2];
                    newData.data[index + 3] = result[3];

                    processedPixels++;
                    if (processedPixels % 1000 === 0 && progressCallback) {
                        progressCallback((processedPixels / totalPixels) * 100);
                    }
                }
            }

            return newData;
        }

        function smartEnhance() {
            if (!originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            // Kombinace několika technik pro vylepšení
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // 1. Mírné zaostření
            applySharpening(80);

            // 2. Redukce šumu
            denoiseImageData(data, canvas.width, canvas.height);

            // 3. Adaptivní kontrast
            for (let i = 0; i < data.length; i += 4) {
                const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                const factor = luminance < 128 ? 1.1 : 0.95;

                data[i] = Math.max(0, Math.min(255, data[i] * factor));
                data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
            }

            ctx.putImageData(imageData, 0, 0);
            alert('Smart Enhancement aplikován!');
        }

        function denoiseImage() {
            if (!originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            denoiseImageData(imageData.data, canvas.width, canvas.height);
            ctx.putImageData(imageData, 0, 0);
            alert('Šum odstraněn!');
        }

        function denoiseImageData(data, width, height) {
            const output = new Uint8ClampedArray(data);

            // Median filter pro redukci šumu
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    for (let channel = 0; channel < 3; channel++) {
                        const values = [];

                        // Získej hodnoty z 3x3 okolí
                        for (let dy = -1; dy <= 1; dy++) {
                            for (let dx = -1; dx <= 1; dx++) {
                                const index = ((y + dy) * width + (x + dx)) * 4 + channel;
                                values.push(data[index]);
                            }
                        }

                        // Seřaď a vezmi median
                        values.sort((a, b) => a - b);
                        const median = values[4]; // střední hodnota z 9

                        const index = (y * width + x) * 4 + channel;
                        output[index] = median;
                    }
                }
            }

            // Zkopíruj zpět
            for (let i = 0; i < data.length; i++) {
                data[i] = output[i];
            }
        }

        function saveLayer() {
            if (!originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            const currentSettings = {};
            ['brightness', 'contrast', 'saturation', 'sharpness', 'blur', 'gamma', 'exposure', 'shadows', 'highlights', 'vignette', 'pixelate'].forEach(control => {
                currentSettings[control] = document.getElementById(control).value;
            });

            const layer = {
                id: Date.now(),
                name: `Vrstva ${layers.length + 1}`,
                settings: currentSettings,
                imageData: ctx.getImageData(0, 0, canvas.width, canvas.height),
                enabled: true,
                opacity: 100
            };

            layers.push(layer);
            updateLayerPanel();
            alert('Vrstva uložena!');
        }

        function loadLayer() {
            if (layers.length === 0) {
                alert('Žádné uložené vrstvy!');
                return;
            }

            const layerNames = layers.map((layer, index) => `${index}: ${layer.name}`);
            const choice = prompt('Vyberte vrstvu k načtení:\n' + layerNames.join('\n'));

            if (choice !== null) {
                const index = parseInt(choice);
                if (index >= 0 && index < layers.length) {
                    const layer = layers[index];

                    // Restore settings
                    Object.keys(layer.settings).forEach(control => {
                        const element = document.getElementById(control);
                        if (element) {
                            element.value = layer.settings[control];
                            document.getElementById(control + 'Value').textContent = layer.settings[control];
                        }
                    });

                    // Restore image
                    ctx.putImageData(layer.imageData, 0, 0);
                    currentLayerIndex = index;
                    alert(`Vrstva "${layer.name}" načtena!`);
                }
            }
        }

        function toggleLayerPanel() {
            const panel = document.getElementById('layerPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            if (panel.style.display === 'block') {
                updateLayerPanel();
            }
        }

        function updateLayerPanel() {
            const layerList = document.getElementById('layerList');
            layerList.innerHTML = '';

            layers.forEach((layer, index) => {
                const layerDiv = document.createElement('div');
                layerDiv.style.cssText = 'background: rgba(255,255,255,0.1); margin: 5px 0; padding: 10px; border-radius: 5px; display: flex; justify-content: space-between; align-items: center;';

                layerDiv.innerHTML = `
                    <div>
                        <strong>${layer.name}</strong>
                        <div style="font-size: 12px; opacity: 0.8;">
                            Jas: ${layer.settings.brightness}, Kontrast: ${layer.settings.contrast}
                        </div>
                    </div>
                    <div>
                        <label style="font-size: 12px;">
                            <input type="checkbox" ${layer.enabled ? 'checked' : ''} onchange="toggleLayer(${index})"> Zapnuto
                        </label>
                        <button onclick="deleteLayer(${index})" style="margin-left: 10px; background: #ff4444; color: white; border: none; padding: 5px; border-radius: 3px;">🗑️</button>
                    </div>
                `;

                layerList.appendChild(layerDiv);
            });
        }

        function toggleLayer(index) {
            layers[index].enabled = !layers[index].enabled;
            applyLayers();
        }

        function deleteLayer(index) {
            if (confirm(`Smazat vrstvu "${layers[index].name}"?`)) {
                layers.splice(index, 1);
                updateLayerPanel();
                if (layers.length > 0) {
                    applyLayers();
                }
            }
        }

        function addAdjustmentLayer() {
            const name = prompt('Název nové vrstvy úprav:');
            if (name) {
                const layer = {
                    id: Date.now(),
                    name: name,
                    settings: {
                        brightness: 0, contrast: 0, saturation: 0,
                        sharpness: 0, blur: 0, gamma: 1.0,
                        exposure: 0, shadows: 0, highlights: 0,
                        vignette: 0, pixelate: 0
                    },
                    enabled: true,
                    opacity: 100,
                    type: 'adjustment'
                };

                layers.push(layer);
                updateLayerPanel();
            }
        }

        function clearLayers() {
            if (confirm('Smazat všechny vrstvy?')) {
                layers = [];
                updateLayerPanel();
                // Reset to original
                if (originalImage) {
                    canvas.width = originalImage.width;
                    canvas.height = originalImage.height;
                    ctx.drawImage(originalImage, 0, 0);
                    originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                }
            }
        }

        function applyLayers() {
            if (!originalImage || layers.length === 0) return;

            // Start with original
            canvas.width = originalImage.width;
            canvas.height = originalImage.height;
            ctx.drawImage(originalImage, 0, 0);

            // Apply each enabled layer
            layers.forEach(layer => {
                if (layer.enabled && layer.imageData) {
                    // Blend layer with current canvas
                    const layerCanvas = document.createElement('canvas');
                    layerCanvas.width = canvas.width;
                    layerCanvas.height = canvas.height;
                    const layerCtx = layerCanvas.getContext('2d');
                    layerCtx.putImageData(layer.imageData, 0, 0);

                    ctx.globalAlpha = layer.opacity / 100;
                    ctx.drawImage(layerCanvas, 0, 0);
                    ctx.globalAlpha = 1.0;
                }
            });
        }

        function processBatch() {
            const files = document.getElementById('batchFiles').files;
            if (files.length === 0) {
                alert('Nejprve vyberte obrázky pro batch zpracování!');
                return;
            }

            if (files.length > 20) {
                alert('Maximum je 20 obrázků najednou!');
                return;
            }

            // Get current settings
            const currentSettings = {};
            ['brightness', 'contrast', 'saturation', 'sharpness', 'blur', 'gamma', 'exposure', 'shadows', 'highlights', 'vignette', 'pixelate'].forEach(control => {
                currentSettings[control] = parseFloat(document.getElementById(control).value);
            });

            // Create progress modal
            const progressModal = document.createElement('div');
            progressModal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; display: flex; align-items: center; justify-content: center;';
            progressModal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%;">
                    <h3 style="color: #333; margin-top: 0;">🔄 Batch zpracování</h3>
                    <div id="batchProgress" style="margin: 20px 0;">
                        <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div id="batchProgressBar" style="width: 0%; height: 100%; background: linear-gradient(90deg, #4CAF50, #45a049); transition: width 0.3s;"></div>
                        </div>
                        <div id="batchProgressText" style="text-align: center; margin-top: 10px; color: #333;">Příprava...</div>
                    </div>
                    <div id="batchResults" style="max-height: 200px; overflow-y: auto; color: #333;"></div>
                    <button id="batchCloseBtn" onclick="closeBatchModal()" style="display: none; margin-top: 15px; background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Zavřít</button>
                </div>
            `;
            document.body.appendChild(progressModal);

            // Process files
            processBatchFiles(files, currentSettings, progressModal);
        }

        async function processBatchFiles(files, settings, modal) {
            const results = [];
            const progressBar = document.getElementById('batchProgressBar');
            const progressText = document.getElementById('batchProgressText');
            const resultsDiv = document.getElementById('batchResults');

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;

                progressBar.style.width = progress + '%';
                progressText.textContent = `Zpracování ${i + 1}/${files.length}: ${file.name}`;

                try {
                    // Load image
                    const img = await loadImageFromFile(file);

                    // Create temporary canvas
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');
                    tempCanvas.width = img.width;
                    tempCanvas.height = img.height;
                    tempCtx.drawImage(img, 0, 0);

                    // Apply all settings
                    const imageData = tempCtx.getImageData(0, 0, img.width, img.height);
                    applyBatchSettings(imageData, settings, img.width, img.height);
                    tempCtx.putImageData(imageData, 0, 0);

                    // Convert to blob and download
                    const blob = await new Promise(resolve => tempCanvas.toBlob(resolve, 'image/png'));
                    const url = URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `enhanced-${file.name.replace(/\.[^/.]+$/, '')}.png`;
                    link.click();

                    URL.revokeObjectURL(url);

                    results.push(`✅ ${file.name} - úspěšně zpracován`);

                } catch (error) {
                    results.push(`❌ ${file.name} - chyba: ${error.message}`);
                }

                // Update results
                resultsDiv.innerHTML = results.map(result => `<div style="margin: 5px 0; padding: 5px; background: #f9f9f9; border-radius: 3px;">${result}</div>`).join('');

                // Small delay to prevent browser freeze
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            progressText.textContent = `Dokončeno! Zpracováno ${files.length} obrázků.`;
            document.getElementById('batchCloseBtn').style.display = 'block';
        }

        function applyBatchSettings(imageData, settings, width, height) {
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                let r = data[i];
                let g = data[i + 1];
                let b = data[i + 2];

                // Apply gamma
                if (settings.gamma !== 1.0) {
                    r = 255 * Math.pow(r / 255, 1 / settings.gamma);
                    g = 255 * Math.pow(g / 255, 1 / settings.gamma);
                    b = 255 * Math.pow(b / 255, 1 / settings.gamma);
                }

                // Apply exposure
                if (settings.exposure !== 0) {
                    const exposureFactor = Math.pow(2, settings.exposure);
                    r *= exposureFactor;
                    g *= exposureFactor;
                    b *= exposureFactor;
                }

                // Apply brightness
                if (settings.brightness !== 0) {
                    r += settings.brightness * 2.55;
                    g += settings.brightness * 2.55;
                    b += settings.brightness * 2.55;
                }

                // Apply contrast
                if (settings.contrast !== 0) {
                    const contrastFactor = (259 * (settings.contrast + 255)) / (255 * (259 - settings.contrast));
                    r = contrastFactor * (r - 128) + 128;
                    g = contrastFactor * (g - 128) + 128;
                    b = contrastFactor * (b - 128) + 128;
                }

                // Apply saturation
                if (settings.saturation !== 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const satFactor = (settings.saturation + 100) / 100;
                    r = gray + satFactor * (r - gray);
                    g = gray + satFactor * (g - gray);
                    b = gray + satFactor * (b - gray);
                }

                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
            }

            // Apply vignette if needed
            if (settings.vignette > 0) {
                applyVignetteEffect(data, width, height, settings.vignette);
            }

            // Apply pixelate if needed
            if (settings.pixelate > 0) {
                applyPixelateEffect(data, width, height, settings.pixelate);
            }
        }

        function closeBatchModal() {
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        function loadImageFromFile(file) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = reject;
                img.src = URL.createObjectURL(file);
            });
        }

        function downloadImage() {
            if (!originalImageData) {
                alert('Nejprve nahrajte obrázek!');
                return;
            }

            // Get current canvas dimensions for filename
            const width = canvas.width;
            const height = canvas.height;
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

            const link = document.createElement('a');
            link.download = `enhanced-image-${width}x${height}-${timestamp}.png`;
            link.href = canvas.toDataURL('image/png', 1.0);
            link.click();

            // Show download info
            const fileSize = Math.round(canvas.toDataURL('image/png').length * 0.75 / 1024);
            alert(`Obrázek stažen!\nRozměry: ${width}×${height}px\nOdhadovaná velikost: ~${fileSize}KB`);
        }
    </script>
</body>
</html>
