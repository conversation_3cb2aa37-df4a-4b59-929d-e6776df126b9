<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Histogram & Scopes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .scopes-container {
            display: grid;
            grid-template-areas: 
                "image histogram"
                "image waveform"
                "vectorscope parade";
            grid-template-columns: 1fr 400px;
            grid-template-rows: 300px 300px 300px;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .image-area {
            grid-area: image;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        
        .histogram-panel {
            grid-area: histogram;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
        }
        
        .waveform-panel {
            grid-area: waveform;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
        }
        
        .vectorscope-panel {
            grid-area: vectorscope;
            background: #2d2d2d;
            padding: 15px;
            border-top: 1px solid #404040;
        }
        
        .parade-panel {
            grid-area: parade;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 10px;
            letter-spacing: 1px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .scope-canvas {
            width: 100%;
            height: 200px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .histogram-canvas {
            width: 100%;
            height: 180px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .vectorscope-canvas {
            width: 180px;
            height: 180px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 50%;
            margin: 0 auto;
        }
        
        .main-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .controls {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        
        .control-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
        }
        
        .control-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .control-button.active {
            background: #00d4ff;
            border-color: #00d4ff;
            color: #000;
        }
        
        .stats-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            min-width: 150px;
        }
        
        .color-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            display: none;
        }
        
        .color-sample {
            width: 30px;
            height: 30px;
            border: 2px solid #555;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .histogram-legend {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 9px;
            color: #666;
        }
        
        .waveform-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .vectorscope-grid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 180px;
            height: 180px;
            pointer-events: none;
        }
        
        .scope-settings {
            margin-top: 10px;
            padding: 8px;
            background: #252525;
            border-radius: 4px;
            border: 1px solid #3a3a3a;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .setting-label {
            font-size: 10px;
            color: #ccc;
            width: 60px;
            flex-shrink: 0;
        }
        
        .setting-slider {
            flex: 1;
            margin: 0 6px;
            height: 3px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .setting-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .setting-value {
            font-size: 10px;
            color: #00d4ff;
            width: 30px;
            text-align: right;
        }
        
        .upload-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
            pointer-events: none;
        }
        
        .upload-overlay.active {
            pointer-events: all;
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .upload-text {
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 10;
        }
        
        .crosshair-h {
            width: 100%;
            height: 1px;
            background: rgba(0, 212, 255, 0.7);
        }
        
        .crosshair-v {
            width: 1px;
            height: 100%;
            background: rgba(0, 212, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="scopes-container">
        <!-- Image Area -->
        <div class="image-area">
            <img class="main-image" id="mainImage" style="display: none;">
            <canvas id="imageCanvas" style="display: none; width: 100%; height: 100%;"></canvas>
            
            <div class="upload-overlay active" id="uploadOverlay">
                <div class="upload-icon">📊</div>
                <div class="upload-text">Nahrajte obrázek pro analýzu</div>
                <div style="font-size: 11px; color: #555;">Podporované formáty: JPG, PNG, TIFF</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
            
            <div class="stats-overlay" id="statsOverlay" style="display: none;">
                <div class="stats-row">
                    <span>Rozměry:</span>
                    <span id="imageDimensions">0 × 0</span>
                </div>
                <div class="stats-row">
                    <span>Velikost:</span>
                    <span id="fileSize">0 KB</span>
                </div>
                <div class="stats-row">
                    <span>Formát:</span>
                    <span id="imageFormat">-</span>
                </div>
                <div class="stats-row">
                    <span>Barevný prostor:</span>
                    <span id="colorSpace">sRGB</span>
                </div>
                <div class="stats-row">
                    <span>Bitová hloubka:</span>
                    <span id="bitDepth">8-bit</span>
                </div>
            </div>
            
            <div class="color-info" id="colorInfo">
                <div class="color-sample" id="colorSample"></div>
                <div id="colorValues">
                    <div>RGB: <span id="rgbValues">0, 0, 0</span></div>
                    <div>HSL: <span id="hslValues">0°, 0%, 0%</span></div>
                    <div>LAB: <span id="labValues">0, 0, 0</span></div>
                    <div>CMYK: <span id="cmykValues">0%, 0%, 0%, 0%</span></div>
                </div>
            </div>
        </div>

        <!-- Histogram Panel -->
        <div class="histogram-panel">
            <div class="panel-title">
                📊 Histogram
                <div class="controls">
                    <button class="control-button active" onclick="setHistogramMode('rgb')" id="histRGB">RGB</button>
                    <button class="control-button" onclick="setHistogramMode('red')" id="histRed">R</button>
                    <button class="control-button" onclick="setHistogramMode('green')" id="histGreen">G</button>
                    <button class="control-button" onclick="setHistogramMode('blue')" id="histBlue">B</button>
                    <button class="control-button" onclick="setHistogramMode('luminance')" id="histLum">L</button>
                </div>
            </div>
            <canvas class="histogram-canvas" id="histogramCanvas" width="370" height="180"></canvas>
            <div class="histogram-legend">
                <span>0</span>
                <span>64</span>
                <span>128</span>
                <span>192</span>
                <span>255</span>
            </div>
            <div class="scope-settings">
                <div class="setting-row">
                    <span class="setting-label">Logaritmický</span>
                    <label style="margin-left: auto;">
                        <input type="checkbox" id="logScale" onchange="updateHistogram()">
                    </label>
                </div>
            </div>
        </div>

        <!-- Waveform Panel -->
        <div class="waveform-panel">
            <div class="panel-title">
                〰️ Waveform Monitor
                <div class="controls">
                    <button class="control-button active" onclick="setWaveformMode('luma')" id="waveLuma">Luma</button>
                    <button class="control-button" onclick="setWaveformMode('rgb')" id="waveRGB">RGB</button>
                    <button class="control-button" onclick="setWaveformMode('parade')" id="waveParade">Parade</button>
                </div>
            </div>
            <div style="position: relative;">
                <canvas class="scope-canvas" id="waveformCanvas" width="370" height="200"></canvas>
                <svg class="waveform-grid" width="370" height="200">
                    <!-- Grid lines will be drawn here -->
                </svg>
            </div>
            <div class="scope-settings">
                <div class="setting-row">
                    <span class="setting-label">Gain:</span>
                    <input type="range" class="setting-slider" id="waveformGain" min="1" max="10" value="5">
                    <span class="setting-value" id="waveformGainValue">5</span>
                </div>
            </div>
        </div>

        <!-- Vectorscope Panel -->
        <div class="vectorscope-panel">
            <div class="panel-title">
                🎯 Vectorscope
                <div class="controls">
                    <button class="control-button active" onclick="setVectorscopeMode('75')" id="vector75">75%</button>
                    <button class="control-button" onclick="setVectorscopeMode('100')" id="vector100">100%</button>
                </div>
            </div>
            <div style="position: relative; text-align: center;">
                <canvas class="vectorscope-canvas" id="vectorscopeCanvas" width="180" height="180"></canvas>
                <svg class="vectorscope-grid" width="180" height="180">
                    <!-- Vectorscope grid and color targets -->
                </svg>
            </div>
            <div class="scope-settings">
                <div class="setting-row">
                    <span class="setting-label">Mag:</span>
                    <input type="range" class="setting-slider" id="vectorMag" min="1" max="5" value="2">
                    <span class="setting-value" id="vectorMagValue">2</span>
                </div>
            </div>
        </div>

        <!-- Parade Panel -->
        <div class="parade-panel">
            <div class="panel-title">
                🌈 RGB Parade
                <div class="controls">
                    <button class="control-button active" onclick="setParadeMode('rgb')" id="paradeRGB">RGB</button>
                    <button class="control-button" onclick="setParadeMode('yuv')" id="paradeYUV">YUV</button>
                </div>
            </div>
            <canvas class="scope-canvas" id="paradeCanvas" width="370" height="200"></canvas>
            <div class="scope-settings">
                <div class="setting-row">
                    <span class="setting-label">Scale:</span>
                    <input type="range" class="setting-slider" id="paradeScale" min="1" max="10" value="5">
                    <span class="setting-value" id="paradeScaleValue">5</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        class HistogramScopes {
            constructor() {
                this.imageCanvas = document.getElementById('imageCanvas');
                this.imageCtx = this.imageCanvas.getContext('2d');
                this.mainImage = document.getElementById('mainImage');
                this.currentImageData = null;

                this.histogramCanvas = document.getElementById('histogramCanvas');
                this.histogramCtx = this.histogramCanvas.getContext('2d');
                this.histogramMode = 'rgb';

                this.waveformCanvas = document.getElementById('waveformCanvas');
                this.waveformCtx = this.waveformCanvas.getContext('2d');
                this.waveformMode = 'luma';

                this.vectorscopeCanvas = document.getElementById('vectorscopeCanvas');
                this.vectorscopeCtx = this.vectorscopeCanvas.getContext('2d');
                this.vectorscopeMode = '75';

                this.paradeCanvas = document.getElementById('paradeCanvas');
                this.paradeCtx = this.paradeCanvas.getContext('2d');
                this.paradeMode = 'rgb';

                this.settings = {
                    waveformGain: 5,
                    vectorMag: 2,
                    paradeScale: 5,
                    logScale: false
                };

                this.initializeEventListeners();
                this.initializeGrids();
                this.loadTestImage();
            }

            initializeEventListeners() {
                // File upload
                const uploadOverlay = document.getElementById('uploadOverlay');
                const fileInput = document.getElementById('fileInput');

                uploadOverlay.addEventListener('click', () => fileInput.click());
                uploadOverlay.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadOverlay.style.background = 'rgba(0, 212, 255, 0.1)';
                });
                uploadOverlay.addEventListener('dragleave', () => {
                    uploadOverlay.style.background = '';
                });
                uploadOverlay.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadOverlay.style.background = '';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                // Mouse tracking for color info
                this.imageCanvas.addEventListener('mousemove', (e) => {
                    this.updateColorInfo(e);
                });

                this.imageCanvas.addEventListener('mouseleave', () => {
                    document.getElementById('colorInfo').style.display = 'none';
                });

                // Settings sliders
                ['waveformGain', 'vectorMag', 'paradeScale'].forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.settings[setting] = parseFloat(e.target.value);
                            document.getElementById(setting + 'Value').textContent = e.target.value;
                            this.updateScopes();
                        });
                    }
                });

                // Log scale checkbox
                document.getElementById('logScale').addEventListener('change', (e) => {
                    this.settings.logScale = e.target.checked;
                    this.updateHistogram();
                });
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Resize canvas to fit image
                        const maxWidth = 800;
                        const maxHeight = 600;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        this.imageCanvas.width = width;
                        this.imageCanvas.height = height;
                        this.imageCtx.drawImage(img, 0, 0, width, height);

                        this.currentImageData = this.imageCtx.getImageData(0, 0, width, height);
                        this.imageCanvas.style.display = 'block';
                        document.getElementById('uploadOverlay').classList.remove('active');

                        this.updateImageStats(file, { width: img.width, height: img.height });
                        this.updateAllScopes();
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            updateImageStats(file, originalDimensions) {
                document.getElementById('imageDimensions').textContent =
                    `${originalDimensions.width} × ${originalDimensions.height}`;
                document.getElementById('fileSize').textContent =
                    this.formatFileSize(file.size);
                document.getElementById('imageFormat').textContent =
                    file.type.split('/')[1].toUpperCase();
                document.getElementById('statsOverlay').style.display = 'block';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }

            updateColorInfo(e) {
                if (!this.currentImageData) return;

                const rect = this.imageCanvas.getBoundingClientRect();
                const x = Math.floor((e.clientX - rect.left) * (this.imageCanvas.width / rect.width));
                const y = Math.floor((e.clientY - rect.top) * (this.imageCanvas.height / rect.height));

                if (x >= 0 && x < this.imageCanvas.width && y >= 0 && y < this.imageCanvas.height) {
                    const index = (y * this.imageCanvas.width + x) * 4;
                    const data = this.currentImageData.data;

                    const r = data[index];
                    const g = data[index + 1];
                    const b = data[index + 2];

                    // Convert to other color spaces
                    const hsl = this.rgbToHsl(r, g, b);
                    const lab = this.rgbToLab(r, g, b);
                    const cmyk = this.rgbToCmyk(r, g, b);

                    // Update color info display
                    const colorInfo = document.getElementById('colorInfo');
                    const colorSample = document.getElementById('colorSample');

                    colorSample.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
                    document.getElementById('rgbValues').textContent = `${r}, ${g}, ${b}`;
                    document.getElementById('hslValues').textContent =
                        `${Math.round(hsl.h)}°, ${Math.round(hsl.s)}%, ${Math.round(hsl.l)}%`;
                    document.getElementById('labValues').textContent =
                        `${Math.round(lab.l)}, ${Math.round(lab.a)}, ${Math.round(lab.b)}`;
                    document.getElementById('cmykValues').textContent =
                        `${Math.round(cmyk.c)}%, ${Math.round(cmyk.m)}%, ${Math.round(cmyk.y)}%, ${Math.round(cmyk.k)}%`;

                    colorInfo.style.display = 'block';
                }
            }

            rgbToHsl(r, g, b) {
                r /= 255; g /= 255; b /= 255;
                const max = Math.max(r, g, b), min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;

                if (max === min) {
                    h = s = 0;
                } else {
                    const d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }

                return { h: h * 360, s: s * 100, l: l * 100 };
            }

            rgbToLab(r, g, b) {
                // Simplified RGB to LAB conversion
                const x = r * 0.4124 + g * 0.3576 + b * 0.1805;
                const y = r * 0.2126 + g * 0.7152 + b * 0.0722;
                const z = r * 0.0193 + g * 0.1192 + b * 0.9505;

                const l = 116 * Math.pow(y / 100, 1/3) - 16;
                const a = 500 * (Math.pow(x / 95.047, 1/3) - Math.pow(y / 100, 1/3));
                const bLab = 200 * (Math.pow(y / 100, 1/3) - Math.pow(z / 108.883, 1/3));

                return { l, a, b: bLab };
            }

            rgbToCmyk(r, g, b) {
                r /= 255; g /= 255; b /= 255;
                const k = 1 - Math.max(r, Math.max(g, b));
                const c = (1 - r - k) / (1 - k) || 0;
                const m = (1 - g - k) / (1 - k) || 0;
                const y = (1 - b - k) / (1 - k) || 0;

                return { c: c * 100, m: m * 100, y: y * 100, k: k * 100 };
            }
        }

            initializeGrids() {
                this.drawWaveformGrid();
                this.drawVectorscopeGrid();
            }

            drawWaveformGrid() {
                const svg = document.querySelector('.waveform-grid');
                svg.innerHTML = '';

                // Horizontal lines (IRE levels)
                const ireLines = [0, 25, 50, 75, 100];
                ireLines.forEach(ire => {
                    const y = 200 - (ire / 100) * 200;
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', '0');
                    line.setAttribute('y1', y);
                    line.setAttribute('x2', '370');
                    line.setAttribute('y2', y);
                    line.setAttribute('stroke', ire === 0 || ire === 100 ? '#666' : '#333');
                    line.setAttribute('stroke-width', '1');
                    svg.appendChild(line);
                });

                // Vertical lines
                for (let x = 0; x <= 370; x += 37) {
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', x);
                    line.setAttribute('y1', '0');
                    line.setAttribute('x2', x);
                    line.setAttribute('y2', '200');
                    line.setAttribute('stroke', '#333');
                    line.setAttribute('stroke-width', '1');
                    svg.appendChild(line);
                }
            }

            drawVectorscopeGrid() {
                const svg = document.querySelector('.vectorscope-grid');
                svg.innerHTML = '';

                const centerX = 90;
                const centerY = 90;

                // Concentric circles
                [30, 60, 90].forEach(radius => {
                    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    circle.setAttribute('cx', centerX);
                    circle.setAttribute('cy', centerY);
                    circle.setAttribute('r', radius);
                    circle.setAttribute('fill', 'none');
                    circle.setAttribute('stroke', radius === 90 ? '#666' : '#333');
                    circle.setAttribute('stroke-width', '1');
                    svg.appendChild(circle);
                });

                // Color target boxes
                const colorTargets = [
                    { angle: 103, color: '#ff0000', name: 'R' }, // Red
                    { angle: 241, color: '#ffff00', name: 'Yl' }, // Yellow
                    { angle: 61, color: '#00ff00', name: 'G' }, // Green
                    { angle: 283, color: '#00ffff', name: 'Cy' }, // Cyan
                    { angle: 347, color: '#0000ff', name: 'B' }, // Blue
                    { angle: 167, color: '#ff00ff', name: 'Mg' } // Magenta
                ];

                colorTargets.forEach(target => {
                    const angle = (target.angle - 90) * Math.PI / 180;
                    const x = centerX + Math.cos(angle) * 75;
                    const y = centerY + Math.sin(angle) * 75;

                    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                    rect.setAttribute('x', x - 8);
                    rect.setAttribute('y', y - 8);
                    rect.setAttribute('width', '16');
                    rect.setAttribute('height', '16');
                    rect.setAttribute('fill', 'none');
                    rect.setAttribute('stroke', target.color);
                    rect.setAttribute('stroke-width', '2');
                    svg.appendChild(rect);

                    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                    text.setAttribute('x', x);
                    text.setAttribute('y', y + 25);
                    text.setAttribute('text-anchor', 'middle');
                    text.setAttribute('fill', target.color);
                    text.setAttribute('font-size', '10');
                    text.textContent = target.name;
                    svg.appendChild(text);
                });

                // Center crosshair
                const hLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                hLine.setAttribute('x1', centerX - 5);
                hLine.setAttribute('y1', centerY);
                hLine.setAttribute('x2', centerX + 5);
                hLine.setAttribute('y2', centerY);
                hLine.setAttribute('stroke', '#888');
                hLine.setAttribute('stroke-width', '1');
                svg.appendChild(hLine);

                const vLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                vLine.setAttribute('x1', centerX);
                vLine.setAttribute('y1', centerY - 5);
                vLine.setAttribute('x2', centerX);
                vLine.setAttribute('y2', centerY + 5);
                vLine.setAttribute('stroke', '#888');
                vLine.setAttribute('stroke-width', '1');
                svg.appendChild(vLine);
            }

            loadTestImage() {
                // Create a test image with color gradients
                this.imageCanvas.width = 800;
                this.imageCanvas.height = 600;

                // Create color gradient test pattern
                const gradient1 = this.imageCtx.createLinearGradient(0, 0, 800, 0);
                gradient1.addColorStop(0, '#ff0000');
                gradient1.addColorStop(0.16, '#ffff00');
                gradient1.addColorStop(0.33, '#00ff00');
                gradient1.addColorStop(0.5, '#00ffff');
                gradient1.addColorStop(0.66, '#0000ff');
                gradient1.addColorStop(0.83, '#ff00ff');
                gradient1.addColorStop(1, '#ff0000');

                this.imageCtx.fillStyle = gradient1;
                this.imageCtx.fillRect(0, 0, 800, 200);

                // Grayscale gradient
                const gradient2 = this.imageCtx.createLinearGradient(0, 200, 800, 200);
                gradient2.addColorStop(0, '#000000');
                gradient2.addColorStop(1, '#ffffff');

                this.imageCtx.fillStyle = gradient2;
                this.imageCtx.fillRect(0, 200, 800, 100);

                // Color bars
                const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                colors.forEach((color, index) => {
                    this.imageCtx.fillStyle = color;
                    this.imageCtx.fillRect(index * 133, 300, 133, 100);
                });

                // Skin tone test
                this.imageCtx.fillStyle = '#d4a574';
                this.imageCtx.fillRect(0, 400, 200, 200);

                // Sky test
                this.imageCtx.fillStyle = '#87ceeb';
                this.imageCtx.fillRect(200, 400, 200, 200);

                // Grass test
                this.imageCtx.fillStyle = '#228b22';
                this.imageCtx.fillRect(400, 400, 200, 200);

                // Neutral gray
                this.imageCtx.fillStyle = '#808080';
                this.imageCtx.fillRect(600, 400, 200, 200);

                this.currentImageData = this.imageCtx.getImageData(0, 0, 800, 600);
                this.imageCanvas.style.display = 'block';
                document.getElementById('uploadOverlay').classList.remove('active');

                this.updateImageStats({
                    name: 'test-pattern.png',
                    size: 800 * 600 * 4,
                    type: 'image/png'
                }, { width: 800, height: 600 });

                this.updateAllScopes();
            }

            updateAllScopes() {
                if (!this.currentImageData) return;

                this.updateHistogram();
                this.updateWaveform();
                this.updateVectorscope();
                this.updateParade();
            }

            updateHistogram() {
                if (!this.currentImageData) return;

                const data = this.currentImageData.data;
                const histogram = {
                    red: new Array(256).fill(0),
                    green: new Array(256).fill(0),
                    blue: new Array(256).fill(0),
                    luminance: new Array(256).fill(0)
                };

                // Calculate histogram
                for (let i = 0; i < data.length; i += 4) {
                    histogram.red[data[i]]++;
                    histogram.green[data[i + 1]]++;
                    histogram.blue[data[i + 2]]++;

                    const luminance = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
                    histogram.luminance[luminance]++;
                }

                this.drawHistogram(histogram);
            }

            drawHistogram(histogram) {
                const ctx = this.histogramCtx;
                const width = this.histogramCanvas.width;
                const height = this.histogramCanvas.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                const maxValue = Math.max(
                    Math.max(...histogram.red),
                    Math.max(...histogram.green),
                    Math.max(...histogram.blue),
                    Math.max(...histogram.luminance)
                );

                if (maxValue === 0) return;

                const barWidth = width / 256;

                // Draw histogram based on current mode
                if (this.histogramMode === 'rgb') {
                    this.drawHistogramChannel(ctx, histogram.red, '#ff4444', maxValue, barWidth, height, 0.7);
                    this.drawHistogramChannel(ctx, histogram.green, '#44ff44', maxValue, barWidth, height, 0.7);
                    this.drawHistogramChannel(ctx, histogram.blue, '#4444ff', maxValue, barWidth, height, 0.7);
                } else if (this.histogramMode === 'luminance') {
                    this.drawHistogramChannel(ctx, histogram.luminance, '#ffffff', maxValue, barWidth, height, 1.0);
                } else {
                    const channelData = histogram[this.histogramMode];
                    const colors = { red: '#ff4444', green: '#44ff44', blue: '#4444ff' };
                    this.drawHistogramChannel(ctx, channelData, colors[this.histogramMode], maxValue, barWidth, height, 1.0);
                }
            }

            drawHistogramChannel(ctx, data, color, maxValue, barWidth, height, alpha) {
                ctx.fillStyle = color;
                ctx.globalAlpha = alpha;

                for (let i = 0; i < 256; i++) {
                    let barHeight = (data[i] / maxValue) * height;

                    if (this.settings.logScale && data[i] > 0) {
                        barHeight = (Math.log(data[i] + 1) / Math.log(maxValue + 1)) * height;
                    }

                    ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
                }

                ctx.globalAlpha = 1.0;
            }
        }

            updateWaveform() {
                if (!this.currentImageData) return;

                const ctx = this.waveformCtx;
                const width = this.waveformCanvas.width;
                const height = this.waveformCanvas.height;
                const data = this.currentImageData.data;
                const imageWidth = this.currentImageData.width;
                const imageHeight = this.currentImageData.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                const gain = this.settings.waveformGain / 5;

                if (this.waveformMode === 'luma') {
                    this.drawLumaWaveform(ctx, data, imageWidth, imageHeight, width, height, gain);
                } else if (this.waveformMode === 'rgb') {
                    this.drawRGBWaveform(ctx, data, imageWidth, imageHeight, width, height, gain);
                } else if (this.waveformMode === 'parade') {
                    this.drawParadeWaveform(ctx, data, imageWidth, imageHeight, width, height, gain);
                }
            }

            drawLumaWaveform(ctx, data, imageWidth, imageHeight, width, height, gain) {
                ctx.fillStyle = '#44ff44';

                for (let x = 0; x < imageWidth; x += Math.max(1, Math.floor(imageWidth / width))) {
                    for (let y = 0; y < imageHeight; y++) {
                        const index = (y * imageWidth + x) * 4;
                        const r = data[index];
                        const g = data[index + 1];
                        const b = data[index + 2];

                        const luma = 0.299 * r + 0.587 * g + 0.114 * b;
                        const plotX = (x / imageWidth) * width;
                        const plotY = height - (luma / 255) * height;

                        ctx.globalAlpha = 0.1 * gain;
                        ctx.fillRect(plotX, plotY, 1, 1);
                    }
                }
                ctx.globalAlpha = 1.0;
            }

            drawRGBWaveform(ctx, data, imageWidth, imageHeight, width, height, gain) {
                const channels = [
                    { color: '#ff4444', index: 0 }, // Red
                    { color: '#44ff44', index: 1 }, // Green
                    { color: '#4444ff', index: 2 }  // Blue
                ];

                channels.forEach(channel => {
                    ctx.fillStyle = channel.color;

                    for (let x = 0; x < imageWidth; x += Math.max(1, Math.floor(imageWidth / width))) {
                        for (let y = 0; y < imageHeight; y++) {
                            const index = (y * imageWidth + x) * 4 + channel.index;
                            const value = data[index];

                            const plotX = (x / imageWidth) * width;
                            const plotY = height - (value / 255) * height;

                            ctx.globalAlpha = 0.05 * gain;
                            ctx.fillRect(plotX, plotY, 1, 1);
                        }
                    }
                });
                ctx.globalAlpha = 1.0;
            }

            drawParadeWaveform(ctx, data, imageWidth, imageHeight, width, height, gain) {
                const channelWidth = width / 3;
                const channels = [
                    { color: '#ff4444', index: 0, offset: 0 }, // Red
                    { color: '#44ff44', index: 1, offset: channelWidth }, // Green
                    { color: '#4444ff', index: 2, offset: channelWidth * 2 }  // Blue
                ];

                channels.forEach(channel => {
                    ctx.fillStyle = channel.color;

                    for (let x = 0; x < imageWidth; x += Math.max(1, Math.floor(imageWidth / channelWidth))) {
                        for (let y = 0; y < imageHeight; y++) {
                            const index = (y * imageWidth + x) * 4 + channel.index;
                            const value = data[index];

                            const plotX = channel.offset + (x / imageWidth) * channelWidth;
                            const plotY = height - (value / 255) * height;

                            ctx.globalAlpha = 0.1 * gain;
                            ctx.fillRect(plotX, plotY, 1, 1);
                        }
                    }
                });
                ctx.globalAlpha = 1.0;
            }

            updateVectorscope() {
                if (!this.currentImageData) return;

                const ctx = this.vectorscopeCtx;
                const width = this.vectorscopeCanvas.width;
                const height = this.vectorscopeCanvas.height;
                const centerX = width / 2;
                const centerY = height / 2;
                const data = this.currentImageData.data;
                const magnitude = this.settings.vectorMag;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                ctx.fillStyle = '#44ff44';

                for (let i = 0; i < data.length; i += 16) { // Sample every 4th pixel
                    const r = data[i] / 255;
                    const g = data[i + 1] / 255;
                    const b = data[i + 2] / 255;

                    // Convert RGB to U and V components
                    const u = -0.147 * r - 0.289 * g + 0.436 * b;
                    const v = 0.615 * r - 0.515 * g - 0.100 * b;

                    const plotX = centerX + u * centerX * magnitude;
                    const plotY = centerY - v * centerY * magnitude;

                    if (plotX >= 0 && plotX < width && plotY >= 0 && plotY < height) {
                        ctx.globalAlpha = 0.1;
                        ctx.fillRect(plotX, plotY, 1, 1);
                    }
                }
                ctx.globalAlpha = 1.0;
            }

            updateParade() {
                if (!this.currentImageData) return;

                const ctx = this.paradeCtx;
                const width = this.paradeCanvas.width;
                const height = this.paradeCanvas.height;
                const data = this.currentImageData.data;
                const imageWidth = this.currentImageData.width;
                const imageHeight = this.currentImageData.height;
                const scale = this.settings.paradeScale / 5;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                if (this.paradeMode === 'rgb') {
                    this.drawRGBParade(ctx, data, imageWidth, imageHeight, width, height, scale);
                } else if (this.paradeMode === 'yuv') {
                    this.drawYUVParade(ctx, data, imageWidth, imageHeight, width, height, scale);
                }
            }

            drawRGBParade(ctx, data, imageWidth, imageHeight, width, height, scale) {
                const channelWidth = width / 3;
                const channels = [
                    { color: '#ff4444', index: 0, offset: 0 }, // Red
                    { color: '#44ff44', index: 1, offset: channelWidth }, // Green
                    { color: '#4444ff', index: 2, offset: channelWidth * 2 }  // Blue
                ];

                channels.forEach(channel => {
                    ctx.fillStyle = channel.color;

                    for (let x = 0; x < imageWidth; x += Math.max(1, Math.floor(imageWidth / channelWidth))) {
                        for (let y = 0; y < imageHeight; y++) {
                            const index = (y * imageWidth + x) * 4 + channel.index;
                            const value = data[index];

                            const plotX = channel.offset + (x / imageWidth) * channelWidth;
                            const plotY = height - (value / 255) * height;

                            ctx.globalAlpha = 0.1 * scale;
                            ctx.fillRect(plotX, plotY, 1, 1);
                        }
                    }
                });
                ctx.globalAlpha = 1.0;
            }

            drawYUVParade(ctx, data, imageWidth, imageHeight, width, height, scale) {
                const channelWidth = width / 3;

                for (let x = 0; x < imageWidth; x += Math.max(1, Math.floor(imageWidth / channelWidth))) {
                    for (let y = 0; y < imageHeight; y++) {
                        const index = (y * imageWidth + x) * 4;
                        const r = data[index] / 255;
                        const g = data[index + 1] / 255;
                        const b = data[index + 2] / 255;

                        // Convert to YUV
                        const yVal = 0.299 * r + 0.587 * g + 0.114 * b;
                        const uVal = -0.147 * r - 0.289 * g + 0.436 * b + 0.5;
                        const vVal = 0.615 * r - 0.515 * g - 0.100 * b + 0.5;

                        const plotX = (x / imageWidth) * channelWidth;

                        // Y channel
                        ctx.fillStyle = '#ffffff';
                        ctx.globalAlpha = 0.1 * scale;
                        ctx.fillRect(plotX, height - yVal * height, 1, 1);

                        // U channel
                        ctx.fillStyle = '#4444ff';
                        ctx.fillRect(channelWidth + plotX, height - uVal * height, 1, 1);

                        // V channel
                        ctx.fillStyle = '#ff4444';
                        ctx.fillRect(channelWidth * 2 + plotX, height - vVal * height, 1, 1);
                    }
                }
                ctx.globalAlpha = 1.0;
            }

            updateScopes() {
                this.updateWaveform();
                this.updateVectorscope();
                this.updateParade();
            }
        }

        // Global functions
        function setHistogramMode(mode) {
            if (!histogramScopes) return;

            // Update button states
            ['rgb', 'red', 'green', 'blue', 'luminance'].forEach(m => {
                const btn = document.getElementById('hist' + (m === 'rgb' ? 'RGB' :
                    m === 'luminance' ? 'Lum' : m.charAt(0).toUpperCase() + m.slice(1)));
                if (btn) {
                    btn.classList.toggle('active', m === mode);
                }
            });

            histogramScopes.histogramMode = mode;
            histogramScopes.updateHistogram();
        }

        function setWaveformMode(mode) {
            if (!histogramScopes) return;

            // Update button states
            ['luma', 'rgb', 'parade'].forEach(m => {
                const btn = document.getElementById('wave' + m.charAt(0).toUpperCase() + m.slice(1));
                if (btn) {
                    btn.classList.toggle('active', m === mode);
                }
            });

            histogramScopes.waveformMode = mode;
            histogramScopes.updateWaveform();
        }

        function setVectorscopeMode(mode) {
            if (!histogramScopes) return;

            // Update button states
            ['75', '100'].forEach(m => {
                const btn = document.getElementById('vector' + m);
                if (btn) {
                    btn.classList.toggle('active', m === mode);
                }
            });

            histogramScopes.vectorscopeMode = mode;
            histogramScopes.updateVectorscope();
        }

        function setParadeMode(mode) {
            if (!histogramScopes) return;

            // Update button states
            ['rgb', 'yuv'].forEach(m => {
                const btn = document.getElementById('parade' + m.toUpperCase());
                if (btn) {
                    btn.classList.toggle('active', m === mode);
                }
            });

            histogramScopes.paradeMode = mode;
            histogramScopes.updateParade();
        }

        function updateHistogram() {
            if (histogramScopes) {
                histogramScopes.updateHistogram();
            }
        }

        // Initialize the histogram scopes
        let histogramScopes;
        document.addEventListener('DOMContentLoaded', () => {
            histogramScopes = new HistogramScopes();
        });
