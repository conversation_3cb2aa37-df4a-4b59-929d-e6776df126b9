<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Grading Panel</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }
        
        .color-panel {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #00d4ff;
            border-bottom: 1px solid #404040;
            padding-bottom: 8px;
        }
        
        .hsl-controls {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .color-channel {
            background: #252525;
            border-radius: 6px;
            padding: 12px;
            border: 1px solid #3a3a3a;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .color-channel:hover {
            border-color: #00d4ff;
        }
        
        .color-channel.active {
            border-color: #00d4ff;
            background: #2a3a4a;
        }
        
        .color-name {
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .color-swatch {
            width: 100%;
            height: 20px;
            border-radius: 3px;
            margin-bottom: 8px;
        }
        
        .hsl-sliders {
            display: none;
        }
        
        .hsl-sliders.active {
            display: block;
        }
        
        .slider-group {
            margin-bottom: 12px;
        }
        
        .slider-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
        }
        
        .slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .curves-container {
            background: #1a1a1a;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .curve-canvas {
            width: 100%;
            height: 200px;
            background: #0f0f0f;
            border-radius: 4px;
            border: 1px solid #404040;
            cursor: crosshair;
        }
        
        .curve-controls {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        
        .curve-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .curve-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .curve-button.active {
            background: #00d4ff;
            border-color: #00d4ff;
            color: #000;
        }
        
        .color-wheels {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .color-wheel {
            text-align: center;
        }
        
        .wheel-title {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 8px;
            text-transform: uppercase;
        }
        
        .wheel-canvas {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 2px solid #404040;
            cursor: pointer;
            margin: 0 auto;
        }
        
        .lut-section {
            margin-top: 20px;
        }
        
        .lut-presets {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .lut-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .lut-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .before-after {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .comparison-canvas {
            flex: 1;
            height: 150px;
            background: #1a1a1a;
            border-radius: 4px;
            border: 1px solid #404040;
        }
        
        .canvas-label {
            font-size: 11px;
            color: #888;
            text-align: center;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="color-panel">
        <div class="panel-title">🎨 HSL / Barevné kanály</div>
        
        <div class="hsl-controls">
            <div class="color-channel" data-color="red" onclick="selectHSLChannel('red')">
                <div class="color-name">Červená</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #ff0000, #ff6666);"></div>
            </div>
            <div class="color-channel" data-color="orange" onclick="selectHSLChannel('orange')">
                <div class="color-name">Oranžová</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #ff8000, #ffb366);"></div>
            </div>
            <div class="color-channel" data-color="yellow" onclick="selectHSLChannel('yellow')">
                <div class="color-name">Žlutá</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #ffff00, #ffff66);"></div>
            </div>
            <div class="color-channel" data-color="green" onclick="selectHSLChannel('green')">
                <div class="color-name">Zelená</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #00ff00, #66ff66);"></div>
            </div>
            <div class="color-channel" data-color="aqua" onclick="selectHSLChannel('aqua')">
                <div class="color-name">Aqua</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #00ffff, #66ffff);"></div>
            </div>
            <div class="color-channel" data-color="blue" onclick="selectHSLChannel('blue')">
                <div class="color-name">Modrá</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #0000ff, #6666ff);"></div>
            </div>
            <div class="color-channel" data-color="purple" onclick="selectHSLChannel('purple')">
                <div class="color-name">Fialová</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #8000ff, #b366ff);"></div>
            </div>
            <div class="color-channel" data-color="magenta" onclick="selectHSLChannel('magenta')">
                <div class="color-name">Magenta</div>
                <div class="color-swatch" style="background: linear-gradient(90deg, #ff00ff, #ff66ff);"></div>
            </div>
        </div>
        
        <div class="hsl-sliders" id="hslSliders">
            <div class="slider-group">
                <div class="slider-label">
                    <span>Odstín</span>
                    <span id="hueValue">0</span>
                </div>
                <input type="range" class="slider" id="hueSlider" min="-180" max="180" value="0">
            </div>
            <div class="slider-group">
                <div class="slider-label">
                    <span>Sytost</span>
                    <span id="satValue">0</span>
                </div>
                <input type="range" class="slider" id="satSlider" min="-100" max="100" value="0">
            </div>
            <div class="slider-group">
                <div class="slider-label">
                    <span>Světlost</span>
                    <span id="lightValue">0</span>
                </div>
                <input type="range" class="slider" id="lightSlider" min="-100" max="100" value="0">
            </div>
        </div>
    </div>
    
    <div class="color-panel">
        <div class="panel-title">📈 Barevné křivky</div>
        
        <div class="curves-container">
            <canvas class="curve-canvas" id="curvesCanvas" width="300" height="200"></canvas>
            <div class="curve-controls">
                <button class="curve-button active" onclick="selectCurveChannel('rgb')">RGB</button>
                <button class="curve-button" onclick="selectCurveChannel('red')">Červená</button>
                <button class="curve-button" onclick="selectCurveChannel('green')">Zelená</button>
                <button class="curve-button" onclick="selectCurveChannel('blue')">Modrá</button>
                <button class="curve-button" onclick="resetCurve()">Reset</button>
            </div>
        </div>
    </div>
    
    <div class="color-panel">
        <div class="panel-title">🎭 Color Grading</div>
        
        <div class="color-wheels">
            <div class="color-wheel">
                <div class="wheel-title">Stíny</div>
                <canvas class="wheel-canvas" id="shadowsWheel" width="80" height="80"></canvas>
            </div>
            <div class="color-wheel">
                <div class="wheel-title">Střední tóny</div>
                <canvas class="wheel-canvas" id="midtonesWheel" width="80" height="80"></canvas>
            </div>
            <div class="color-wheel">
                <div class="wheel-title">Světla</div>
                <canvas class="wheel-canvas" id="highlightsWheel" width="80" height="80"></canvas>
            </div>
        </div>
        
        <div class="lut-section">
            <div class="panel-title" style="font-size: 14px; margin-bottom: 10px;">LUT Presety</div>
            <div class="lut-presets">
                <button class="lut-button" onclick="applyColorGrade('cinematic')">🎬 Cinematic</button>
                <button class="lut-button" onclick="applyColorGrade('warm')">🔥 Teplý</button>
                <button class="lut-button" onclick="applyColorGrade('cool')">❄️ Chladný</button>
                <button class="lut-button" onclick="applyColorGrade('vintage')">📷 Vintage</button>
                <button class="lut-button" onclick="applyColorGrade('bleach')">💡 Bleach Bypass</button>
                <button class="lut-button" onclick="applyColorGrade('teal')">🌊 Teal & Orange</button>
            </div>
        </div>
    </div>
    
    <div class="color-panel">
        <div class="panel-title">👁️ Před / Po</div>
        <div class="before-after">
            <div>
                <canvas class="comparison-canvas" id="beforeCanvas"></canvas>
                <div class="canvas-label">Před</div>
            </div>
            <div>
                <canvas class="comparison-canvas" id="afterCanvas"></canvas>
                <div class="canvas-label">Po</div>
            </div>
        </div>
    </div>

    <script src="advanced-color-tools.js"></script>
    <script>
        class ColorGradingPanel {
            constructor() {
                this.colorTools = new ColorTools();
                this.currentHSLChannel = null;
                this.currentCurveChannel = 'rgb';
                this.curvePoints = {
                    rgb: [{ x: 0, y: 0 }, { x: 255, y: 255 }],
                    red: [{ x: 0, y: 0 }, { x: 255, y: 255 }],
                    green: [{ x: 0, y: 0 }, { x: 255, y: 255 }],
                    blue: [{ x: 0, y: 0 }, { x: 255, y: 255 }]
                };

                this.initializeCurvesCanvas();
                this.initializeColorWheels();
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // HSL sliders
                ['hueSlider', 'satSlider', 'lightSlider'].forEach(sliderId => {
                    const slider = document.getElementById(sliderId);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateHSLValue(sliderId, e.target.value);
                        });
                    }
                });
            }

            initializeCurvesCanvas() {
                this.curvesCanvas = document.getElementById('curvesCanvas');
                this.curvesCtx = this.curvesCanvas.getContext('2d');

                this.curvesCanvas.addEventListener('mousedown', (e) => {
                    this.handleCurveMouseDown(e);
                });

                this.curvesCanvas.addEventListener('mousemove', (e) => {
                    this.handleCurveMouseMove(e);
                });

                this.curvesCanvas.addEventListener('mouseup', (e) => {
                    this.handleCurveMouseUp(e);
                });

                this.drawCurves();
            }

            initializeColorWheels() {
                ['shadowsWheel', 'midtonesWheel', 'highlightsWheel'].forEach(wheelId => {
                    const canvas = document.getElementById(wheelId);
                    if (canvas) {
                        this.drawColorWheel(canvas);

                        canvas.addEventListener('click', (e) => {
                            this.handleColorWheelClick(e, wheelId);
                        });
                    }
                });
            }

            drawColorWheel(canvas) {
                const ctx = canvas.getContext('2d');
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 2;

                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw color wheel
                for (let angle = 0; angle < 360; angle += 1) {
                    const startAngle = (angle - 1) * Math.PI / 180;
                    const endAngle = angle * Math.PI / 180;

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.lineWidth = radius / 2;
                    ctx.strokeStyle = `hsl(${angle}, 100%, 50%)`;
                    ctx.stroke();
                }

                // Draw center circle
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius / 3, 0, 2 * Math.PI);
                ctx.fillStyle = '#2d2d2d';
                ctx.fill();
                ctx.strokeStyle = '#555';
                ctx.lineWidth = 1;
                ctx.stroke();
            }

            drawCurves() {
                const ctx = this.curvesCtx;
                const width = this.curvesCanvas.width;
                const height = this.curvesCanvas.height;

                // Clear canvas
                ctx.clearRect(0, 0, width, height);

                // Draw grid
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;

                // Vertical lines
                for (let x = 0; x <= width; x += width / 4) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();
                }

                // Horizontal lines
                for (let y = 0; y <= height; y += height / 4) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }

                // Draw diagonal reference line
                ctx.strokeStyle = '#555';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(0, height);
                ctx.lineTo(width, 0);
                ctx.stroke();

                // Draw current curve
                const points = this.curvePoints[this.currentCurveChannel];
                if (points.length > 1) {
                    ctx.strokeStyle = this.getCurveColor(this.currentCurveChannel);
                    ctx.lineWidth = 2;
                    ctx.beginPath();

                    const firstPoint = points[0];
                    ctx.moveTo(
                        (firstPoint.x / 255) * width,
                        height - (firstPoint.y / 255) * height
                    );

                    for (let i = 1; i < points.length; i++) {
                        const point = points[i];
                        ctx.lineTo(
                            (point.x / 255) * width,
                            height - (point.y / 255) * height
                        );
                    }

                    ctx.stroke();

                    // Draw control points
                    ctx.fillStyle = this.getCurveColor(this.currentCurveChannel);
                    points.forEach(point => {
                        ctx.beginPath();
                        ctx.arc(
                            (point.x / 255) * width,
                            height - (point.y / 255) * height,
                            4,
                            0,
                            2 * Math.PI
                        );
                        ctx.fill();
                    });
                }
            }

            getCurveColor(channel) {
                const colors = {
                    rgb: '#ffffff',
                    red: '#ff4444',
                    green: '#44ff44',
                    blue: '#4444ff'
                };
                return colors[channel] || '#ffffff';
            }

            handleCurveMouseDown(e) {
                const rect = this.curvesCanvas.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 255;
                const y = 255 - ((e.clientY - rect.top) / rect.height) * 255;

                // Find closest point or create new one
                const points = this.curvePoints[this.currentCurveChannel];
                let closestIndex = -1;
                let closestDistance = Infinity;

                points.forEach((point, index) => {
                    const distance = Math.sqrt(Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2));
                    if (distance < closestDistance && distance < 20) {
                        closestDistance = distance;
                        closestIndex = index;
                    }
                });

                if (closestIndex === -1) {
                    // Create new point
                    points.push({ x: Math.max(0, Math.min(255, x)), y: Math.max(0, Math.min(255, y)) });
                    points.sort((a, b) => a.x - b.x);
                }

                this.isDragging = true;
                this.dragPointIndex = closestIndex === -1 ? points.length - 1 : closestIndex;
                this.drawCurves();
            }

            handleCurveMouseMove(e) {
                if (!this.isDragging) return;

                const rect = this.curvesCanvas.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 255;
                const y = 255 - ((e.clientY - rect.top) / rect.height) * 255;

                const points = this.curvePoints[this.currentCurveChannel];
                if (this.dragPointIndex >= 0 && this.dragPointIndex < points.length) {
                    points[this.dragPointIndex].x = Math.max(0, Math.min(255, x));
                    points[this.dragPointIndex].y = Math.max(0, Math.min(255, y));

                    // Keep points sorted by x
                    points.sort((a, b) => a.x - b.x);

                    this.drawCurves();
                    this.applyCurveAdjustments();
                }
            }

            handleCurveMouseUp(e) {
                this.isDragging = false;
                this.dragPointIndex = -1;
            }

            applyCurveAdjustments() {
                // Update the curve in colorTools
                Object.keys(this.curvePoints).forEach(channel => {
                    this.colorTools.curves[channel] = this.colorTools.createCurveFromPoints(this.curvePoints[channel]);
                });

                // Apply to image if available
                if (window.editor && window.editor.currentImageData) {
                    const imageData = new ImageData(
                        new Uint8ClampedArray(window.editor.originalImageData.data),
                        window.editor.originalImageData.width,
                        window.editor.originalImageData.height
                    );

                    this.colorTools.applyCurves(imageData);
                    window.editor.ctx.putImageData(imageData, 0, 0);
                    window.editor.currentImageData = imageData;
                }
            }

            updateHSLValue(sliderId, value) {
                const valueId = sliderId.replace('Slider', 'Value');
                document.getElementById(valueId).textContent = value;

                if (this.currentHSLChannel) {
                    const property = sliderId.replace('Slider', '').replace('hue', 'hue').replace('sat', 'saturation').replace('light', 'lightness');
                    this.colorTools.hslChannels[this.currentHSLChannel][property] = parseFloat(value);

                    this.applyHSLAdjustments();
                }
            }

            applyHSLAdjustments() {
                if (window.editor && window.editor.currentImageData) {
                    const imageData = new ImageData(
                        new Uint8ClampedArray(window.editor.originalImageData.data),
                        window.editor.originalImageData.width,
                        window.editor.originalImageData.height
                    );

                    this.colorTools.applyHSLAdjustments(imageData);
                    window.editor.ctx.putImageData(imageData, 0, 0);
                    window.editor.currentImageData = imageData;
                }
            }

            handleColorWheelClick(e, wheelId) {
                const canvas = e.target;
                const rect = canvas.getBoundingClientRect();
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const x = e.clientX - rect.left - centerX;
                const y = e.clientY - rect.top - centerY;

                const angle = Math.atan2(y, x) * 180 / Math.PI;
                const distance = Math.sqrt(x * x + y * y);
                const maxDistance = Math.min(centerX, centerY) - 2;

                if (distance <= maxDistance) {
                    const hue = (angle + 360) % 360;
                    const saturation = (distance / maxDistance) * 100;

                    console.log(`${wheelId}: Hue ${hue.toFixed(0)}°, Saturation ${saturation.toFixed(0)}%`);

                    // Apply color grading based on wheel
                    this.applyColorWheelAdjustment(wheelId, hue, saturation);
                }
            }

            applyColorWheelAdjustment(wheelId, hue, saturation) {
                // Implementation for color wheel adjustments
                console.log(`Applying ${wheelId} adjustment: ${hue}°, ${saturation}%`);
            }
        }

        // Global functions
        function selectHSLChannel(color) {
            // Remove active class from all channels
            document.querySelectorAll('.color-channel').forEach(channel => {
                channel.classList.remove('active');
            });

            // Add active class to selected channel
            document.querySelector(`[data-color="${color}"]`).classList.add('active');

            // Show sliders
            document.getElementById('hslSliders').classList.add('active');

            // Update current channel
            if (window.colorGradingPanel) {
                window.colorGradingPanel.currentHSLChannel = color;

                // Load current values
                const channel = window.colorGradingPanel.colorTools.hslChannels[color];
                document.getElementById('hueSlider').value = channel.hue;
                document.getElementById('satSlider').value = channel.saturation;
                document.getElementById('lightSlider').value = channel.lightness;

                document.getElementById('hueValue').textContent = channel.hue;
                document.getElementById('satValue').textContent = channel.saturation;
                document.getElementById('lightValue').textContent = channel.lightness;
            }
        }

        function selectCurveChannel(channel) {
            // Update button states
            document.querySelectorAll('.curve-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update current channel
            if (window.colorGradingPanel) {
                window.colorGradingPanel.currentCurveChannel = channel;
                window.colorGradingPanel.drawCurves();
            }
        }

        function resetCurve() {
            if (window.colorGradingPanel) {
                const channel = window.colorGradingPanel.currentCurveChannel;
                window.colorGradingPanel.curvePoints[channel] = [{ x: 0, y: 0 }, { x: 255, y: 255 }];
                window.colorGradingPanel.drawCurves();
                window.colorGradingPanel.applyCurveAdjustments();
            }
        }

        function applyColorGrade(gradeName) {
            if (window.colorGradingPanel && window.editor && window.editor.currentImageData) {
                const imageData = new ImageData(
                    new Uint8ClampedArray(window.editor.originalImageData.data),
                    window.editor.originalImageData.width,
                    window.editor.originalImageData.height
                );

                window.colorGradingPanel.colorTools.applyColorGrade(imageData, gradeName);
                window.editor.ctx.putImageData(imageData, 0, 0);
                window.editor.currentImageData = imageData;
            }
        }

        // Initialize the color grading panel
        document.addEventListener('DOMContentLoaded', () => {
            window.colorGradingPanel = new ColorGradingPanel();
        });
    </script>
