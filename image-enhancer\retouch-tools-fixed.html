<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Po<PERSON>roč<PERSON>vy</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .retouch-container {
            display: grid;
            grid-template-columns: 300px 1fr 280px;
            grid-template-rows: 1fr;
            grid-template-areas: "tools canvas properties";
            height: 100vh;
            gap: 0;
        }
        
        .tools-panel {
            grid-area: tools;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-right: 1px solid #404040;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .properties-panel {
            grid-area: properties;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-left: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
            border: 1px solid #404040;
            cursor: crosshair;
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px dashed #00d4ff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .tool-group {
            margin-bottom: 20px;
            background: #252525;
            padding: 15px;
            border-radius: 6px;
        }
        
        .tool-button {
            width: 100%;
            background: #404040;
            color: #e0e0e0;
            border: 1px solid #555;
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px 0;
            font-size: 12px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tool-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .tool-button.active {
            background: #00d4ff;
            color: #000;
            border-color: #00d4ff;
        }
        
        .auto-button {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .auto-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }
        
        .control-group {
            margin-bottom: 15px;
            background: #252525;
            padding: 12px;
            border-radius: 6px;
        }
        
        .control-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #ccc;
        }
        
        .control-value {
            color: #00d4ff;
            font-weight: bold;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin: 15px 0 10px 0;
            border-bottom: 1px solid #404040;
            padding-bottom: 5px;
        }
        
        .brush-preview {
            width: 60px;
            height: 60px;
            border: 2px solid #404040;
            border-radius: 50%;
            margin: 10px auto;
            background: radial-gradient(circle, rgba(0,212,255,0.3) 0%, transparent 70%);
            position: relative;
        }
        
        .brush-preview::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: #00d4ff;
            border-radius: 50%;
        }
        
        /* Help System */
        .help-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
            transition: all 0.3s;
        }
        
        .help-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 212, 255, 0.4);
        }
        
        .help-toggle.active {
            background: #ff6b6b;
            color: #fff;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.4;
            max-width: 280px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            border: 1px solid #00d4ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
        }
        
        .tooltip.show {
            opacity: 1;
            visibility: visible;
        }
        
        .tooltip::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #00d4ff;
        }
        
        .tooltip-title {
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 4px;
        }
        
        .tool-group.help-active {
            position: relative;
            border: 2px solid #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Help Toggle Button -->
    <button class="help-toggle" id="helpToggle" onclick="toggleHelp()" title="Zapnout/vypnout nápovědu">?</button>
    
    <div class="retouch-container">
        <!-- Tools Panel -->
        <div class="tools-panel">
            <div class="panel-title">🛠️ Retuš nástroje</div>
            
            <button class="auto-button" onclick="document.getElementById('fileInput').click()" style="background: linear-gradient(135deg, #28a745, #20c997); margin-bottom: 10px;" data-help="Import|Nahrajte vlastní obrázek z počítače pro retušování.">📁 Importovat obrázek</button>
            
            <button class="auto-button" id="autoButton" onclick="autoRetouch()" data-help="Auto retuš|Automatické vyhlazení pleti a odstranění nedokonalostí. 3 různé styly intenzity.">✨ Auto retuš</button>
            
            <div class="section-title">Základní nástroje</div>
            
            <div class="tool-group" data-help="Healing Brush|Nástroj pro odstranění skvrn, jizev a nedokonalostí. Vzorkuje okolní texturu.">
                <button class="tool-button active" onclick="selectTool('healing')" data-tool="healing">
                    <span>🩹</span> Healing Brush
                </button>
            </div>
            
            <div class="tool-group" data-help="Clone Stamp|Klonuje části obrázku z jednoho místa na druhé. Ideální pro duplikování textur.">
                <button class="tool-button" onclick="selectTool('clone')" data-tool="clone">
                    <span>📋</span> Clone Stamp
                </button>
            </div>
            
            <div class="tool-group" data-help="Spot Healing|Rychlé odstranění malých skvrn a nedokonalostí jedním kliknutím.">
                <button class="tool-button" onclick="selectTool('spot')" data-tool="spot">
                    <span>🎯</span> Spot Healing
                </button>
            </div>
            
            <div class="section-title">Vyhlazování</div>
            
            <div class="tool-group" data-help="Blur Tool|Rozostřuje oblasti pro vyhlazení pleti nebo zmírnění detailů.">
                <button class="tool-button" onclick="selectTool('blur')" data-tool="blur">
                    <span>🌫️</span> Blur Tool
                </button>
            </div>
            
            <div class="tool-group" data-help="Sharpen Tool|Zaostřuje detaily a zvýrazňuje textury. Opak blur nástroje.">
                <button class="tool-button" onclick="selectTool('sharpen')" data-tool="sharpen">
                    <span>🔍</span> Sharpen Tool
                </button>
            </div>
            
            <div class="section-title">Dodging & Burning</div>
            
            <div class="tool-group" data-help="Dodge Tool|Zesvětluje oblasti obrázku. Používá se pro zvýraznění světel.">
                <button class="tool-button" onclick="selectTool('dodge')" data-tool="dodge">
                    <span>☀️</span> Dodge Tool
                </button>
            </div>
            
            <div class="tool-group" data-help="Burn Tool|Ztmavuje oblasti obrázku. Používá se pro prohloubení stínů.">
                <button class="tool-button" onclick="selectTool('burn')" data-tool="burn">
                    <span>🔥</span> Burn Tool
                </button>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="tool-button" onclick="loadTestImage()" style="width: 100%; margin-top: 10px;" data-help="Test obrázek|Načte ukázkový portrét pro vyzkoušení retuš nástrojů.">🎨 Test obrázek</button>
                <button class="tool-button" onclick="resetAll()" style="width: 100%; margin-top: 5px;" data-help="Reset|Vrátí obrázek do původního stavu před všemi úpravami.">🔄 Reset vše</button>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas" width="800" height="600"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">🖼️</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <div class="panel-title">⚙️ Vlastnosti</div>
            
            <div class="section-title">Štětec</div>
            
            <div class="brush-preview" id="brushPreview"></div>
            
            <div class="control-group" data-help="Velikost štětce|Průměr štětce v pixelech. Větší štětec = širší oblast působení.">
                <div class="control-label">
                    <span>Velikost</span>
                    <span class="control-value" id="brushSizeValue">20</span>
                </div>
                <input type="range" class="control-slider" id="brushSize" min="1" max="100" value="20">
            </div>
            
            <div class="control-group" data-help="Tvrdost štětce|Ostrost okrajů štětce. 100% = ostré okraje, 0% = měkké okraje.">
                <div class="control-label">
                    <span>Tvrdost</span>
                    <span class="control-value" id="brushHardnessValue">50</span>
                </div>
                <input type="range" class="control-slider" id="brushHardness" min="0" max="100" value="50">
            </div>
            
            <div class="control-group" data-help="Síla nástroje|Intenzita působení nástroje. Vyšší hodnoty = silnější efekt.">
                <div class="control-label">
                    <span>Síla</span>
                    <span class="control-value" id="brushStrengthValue">50</span>
                </div>
                <input type="range" class="control-slider" id="brushStrength" min="1" max="100" value="50">
            </div>
            
            <div class="control-group" data-help="Průtok|Rychlost aplikace efektu. Nižší hodnoty = postupné budování efektu.">
                <div class="control-label">
                    <span>Průtok</span>
                    <span class="control-value" id="brushFlowValue">100</span>
                </div>
                <input type="range" class="control-slider" id="brushFlow" min="1" max="100" value="100">
            </div>
            
            <div class="section-title">Pokročilé</div>
            
            <div class="control-group" data-help="Vyhlazení pleti|Globální vyhlazení pleti na celém obrázku. Zachovává důležité detaily.">
                <div class="control-label">
                    <span>Vyhlazení pleti</span>
                    <span class="control-value" id="skinSmoothValue">0</span>
                </div>
                <input type="range" class="control-slider" id="skinSmooth" min="0" max="100" value="0">
            </div>
            
            <div class="control-group" data-help="Zesvětlení očí|Automatické zesvětlení a zvýraznění očí na portrétech.">
                <div class="control-label">
                    <span>Zesvětlení očí</span>
                    <span class="control-value" id="eyeBrightenValue">0</span>
                </div>
                <input type="range" class="control-slider" id="eyeBrighten" min="0" max="100" value="0">
            </div>
            
            <div style="margin-top: 20px;">
                <button class="tool-button" onclick="downloadImage()" style="width: 100%;" data-help="Stáhnout obrázek|Uloží retušovaný obrázek ve formátu PNG s vysokou kvalitou.">📥 Stáhnout</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let canvas, ctx;
        let originalImageData, currentImageData;
        let helpMode = false;
        let currentTooltip = null;
        let currentTool = 'healing';
        let isDrawing = false;
        let autoMode = 0; // 0, 1, 2 for different auto retouch styles
        
        const brushSettings = {
            size: 20,
            hardness: 50,
            strength: 50,
            flow: 100
        };
        
        const adjustments = {
            skinSmooth: 0,
            eyeBrighten: 0
        };

        function initializeApp() {
            console.log('🚀 Initializing Retouch Tools...');

            // Get canvas elements
            canvas = document.getElementById('mainCanvas');
            if (!canvas) {
                console.error('❌ Main canvas not found!');
                return false;
            }

            ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ Canvas context not found!');
                return false;
            }

            console.log('✅ Canvas elements initialized');

            // Setup event listeners
            setupEventListeners();

            // Load test image
            loadTestImage();

            // Initialize auto button text
            updateAutoButtonText();

            console.log('✅ Retouch Tools initialized successfully');
            return true;
        }

        function setupEventListeners() {
            // File input
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            // Upload zone drag & drop
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('drop', handleDrop);
                uploadZone.addEventListener('dragleave', (e) => {
                    e.currentTarget.style.background = '';
                });
            }

            // Canvas drawing events
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseleave', stopDrawing);

            // Brush settings sliders
            ['brushSize', 'brushHardness', 'brushStrength', 'brushFlow', 'skinSmooth', 'eyeBrighten'].forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.addEventListener('input', (e) => {
                        updateSetting(key, parseFloat(e.target.value));
                    });
                }
            });
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('📁 Loading file:', file.name);
                loadImageFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.background = 'rgba(0, 212, 255, 0.15)';
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.background = '';
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                console.log('📁 Loading dropped file:', files[0].name);
                loadImageFile(files[0]);
            }
        }

        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    console.log('🖼️ Image loaded:', img.width, 'x', img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    console.error('❌ Error loading image');
                    alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('❌ Error reading file');
                alert('Chyba při čtení souboru.');
            };
            reader.readAsDataURL(file);
        }

        function drawImageToCanvas(img) {
            // Calculate size to fit canvas
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;

            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;

            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;

            // Clear with black background
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, maxWidth, maxHeight);

            // Draw image centered
            ctx.drawImage(img, x, y, width, height);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);
            currentImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);

            // Hide upload zone
            hideUploadZone();

            console.log('✅ Image drawn to canvas');
        }

        function loadTestImage() {
            console.log('🎨 Loading test image...');

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Create a portrait-like test pattern for retouching
            const gradient = ctx.createRadialGradient(400, 200, 50, 400, 200, 200);
            gradient.addColorStop(0, '#ffdbcc');
            gradient.addColorStop(0.7, '#e6b8a2');
            gradient.addColorStop(1, '#d4a574');

            ctx.fillStyle = gradient;
            ctx.fillRect(200, 100, 400, 300);

            // Add some "imperfections" to retouch
            ctx.fillStyle = '#cc9966';
            ctx.beginPath();
            ctx.arc(350, 180, 3, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(450, 220, 2, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(380, 280, 4, 0, 2 * Math.PI);
            ctx.fill();

            // Add "eyes" area
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(360, 200, 15, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(440, 200, 15, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#333333';
            ctx.beginPath();
            ctx.arc(360, 200, 8, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(440, 200, 8, 0, 2 * Math.PI);
            ctx.fill();

            // Add text
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('RETOUCH TEST PORTRAIT', 220, 450);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // Hide upload zone
            hideUploadZone();

            console.log('✅ Test image loaded');
        }

        function selectTool(toolName) {
            currentTool = toolName;

            // Update button states
            document.querySelectorAll('.tool-button').forEach(btn => {
                btn.classList.remove('active');
            });

            document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

            console.log('🛠️ Tool selected:', toolName);
        }

        function updateSetting(setting, value) {
            if (setting.startsWith('brush')) {
                const key = setting.replace('brush', '').toLowerCase();
                brushSettings[key] = value;
            } else {
                adjustments[setting] = value;
            }

            // Update value display
            const valueDisplay = document.getElementById(setting + 'Value');
            if (valueDisplay) {
                valueDisplay.textContent = value;
            }

            // Update brush preview
            updateBrushPreview();

            // Apply adjustments if needed
            if (setting === 'skinSmooth' || setting === 'eyeBrighten') {
                applyGlobalAdjustments();
            }
        }

        function updateBrushPreview() {
            const preview = document.getElementById('brushPreview');
            if (!preview) return;

            const size = Math.min(brushSettings.size, 30);
            const hardness = brushSettings.hardness;

            preview.style.width = size + 'px';
            preview.style.height = size + 'px';

            const softness = 100 - hardness;
            preview.style.background = `radial-gradient(circle, rgba(0,212,255,0.5) 0%, rgba(0,212,255,${softness/200}) 70%, transparent 100%)`;
        }

        function startDrawing(event) {
            if (!currentImageData) return;

            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            applyTool(x, y);
        }

        function draw(event) {
            if (!isDrawing || !currentImageData) return;

            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            applyTool(x, y);
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function applyTool(x, y) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const brushSize = brushSettings.size;
            const strength = brushSettings.strength / 100;

            for (let dy = -brushSize; dy <= brushSize; dy++) {
                for (let dx = -brushSize; dx <= brushSize; dx++) {
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    if (distance > brushSize) continue;

                    const pixelX = Math.round(x + dx);
                    const pixelY = Math.round(y + dy);

                    if (pixelX < 0 || pixelX >= canvas.width || pixelY < 0 || pixelY >= canvas.height) continue;

                    const index = (pixelY * canvas.width + pixelX) * 4;
                    const brushStrength = (1 - distance / brushSize) * strength;

                    switch (currentTool) {
                        case 'healing':
                            applyHealing(data, index, brushStrength);
                            break;
                        case 'blur':
                            applyBlur(data, index, brushStrength, pixelX, pixelY);
                            break;
                        case 'sharpen':
                            applySharpen(data, index, brushStrength);
                            break;
                        case 'dodge':
                            applyDodge(data, index, brushStrength);
                            break;
                        case 'burn':
                            applyBurn(data, index, brushStrength);
                            break;
                        case 'spot':
                            applySpotHealing(data, index, brushStrength);
                            break;
                    }
                }
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        function applyHealing(data, index, strength) {
            // Simple healing - blend with surrounding pixels
            const r = data[index] * (1 - strength) + 180 * strength;
            const g = data[index + 1] * (1 - strength) + 150 * strength;
            const b = data[index + 2] * (1 - strength) + 120 * strength;

            data[index] = Math.min(255, r);
            data[index + 1] = Math.min(255, g);
            data[index + 2] = Math.min(255, b);
        }

        function applyBlur(data, index, strength, x, y) {
            // Simple blur effect
            let r = 0, g = 0, b = 0, count = 0;

            for (let dy = -2; dy <= 2; dy++) {
                for (let dx = -2; dx <= 2; dx++) {
                    const nx = x + dx;
                    const ny = y + dy;
                    if (nx >= 0 && nx < canvas.width && ny >= 0 && ny < canvas.height) {
                        const nIndex = (ny * canvas.width + nx) * 4;
                        r += data[nIndex];
                        g += data[nIndex + 1];
                        b += data[nIndex + 2];
                        count++;
                    }
                }
            }

            if (count > 0) {
                const avgR = r / count;
                const avgG = g / count;
                const avgB = b / count;

                data[index] = data[index] * (1 - strength) + avgR * strength;
                data[index + 1] = data[index + 1] * (1 - strength) + avgG * strength;
                data[index + 2] = data[index + 2] * (1 - strength) + avgB * strength;
            }
        }

        function applySharpen(data, index, strength) {
            // Simple sharpen effect
            const factor = 1 + strength;
            data[index] = Math.min(255, data[index] * factor);
            data[index + 1] = Math.min(255, data[index + 1] * factor);
            data[index + 2] = Math.min(255, data[index + 2] * factor);
        }

        function applyDodge(data, index, strength) {
            // Lighten effect
            const factor = 1 + strength * 0.3;
            data[index] = Math.min(255, data[index] * factor);
            data[index + 1] = Math.min(255, data[index + 1] * factor);
            data[index + 2] = Math.min(255, data[index + 2] * factor);
        }

        function applyBurn(data, index, strength) {
            // Darken effect
            const factor = 1 - strength * 0.3;
            data[index] = Math.max(0, data[index] * factor);
            data[index + 1] = Math.max(0, data[index + 1] * factor);
            data[index + 2] = Math.max(0, data[index + 2] * factor);
        }

        function applySpotHealing(data, index, strength) {
            // Similar to healing but more aggressive
            applyHealing(data, index, strength * 1.5);
        }

        // Auto retouch with multiple modes
        function autoRetouch() {
            console.log('✨ Auto retouching...');

            if (!originalImageData) {
                console.log('❌ No image to retouch');
                return;
            }

            // Cycle through 3 different auto modes
            autoMode = (autoMode + 1) % 3;

            switch(autoMode) {
                case 0:
                    applySubtleRetouch();
                    console.log('✅ Subtle retouch applied');
                    break;
                case 1:
                    applyMediumRetouch();
                    console.log('✅ Medium retouch applied');
                    break;
                case 2:
                    applyStrongRetouch();
                    console.log('✅ Strong retouch applied');
                    break;
            }

            updateAutoButtonText();
        }

        function applySubtleRetouch() {
            // Subtle skin smoothing and eye brightening
            updateSetting('skinSmooth', 20);
            updateSetting('eyeBrighten', 15);

            // Apply subtle healing to common problem areas
            applyAutoHealing(0.3);
        }

        function applyMediumRetouch() {
            // Medium skin smoothing and eye brightening
            updateSetting('skinSmooth', 40);
            updateSetting('eyeBrighten', 30);

            // Apply medium healing
            applyAutoHealing(0.5);
        }

        function applyStrongRetouch() {
            // Strong skin smoothing and eye brightening
            updateSetting('skinSmooth', 60);
            updateSetting('eyeBrighten', 50);

            // Apply strong healing
            applyAutoHealing(0.7);
        }

        function applyAutoHealing(intensity) {
            // Automatically detect and heal common skin imperfections
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Simple auto-healing algorithm
            for (let y = 0; y < canvas.height; y += 5) {
                for (let x = 0; x < canvas.width; x += 5) {
                    const index = (y * canvas.width + x) * 4;
                    const r = data[index];
                    const g = data[index + 1];
                    const b = data[index + 2];

                    // Detect skin-like colors and imperfections
                    if (r > 120 && g > 80 && b > 60 && r > g && g > b) {
                        // Apply healing to skin areas
                        applyHealing(data, index, intensity * 0.5);
                    }
                }
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        function applyGlobalAdjustments() {
            if (!originalImageData) return;

            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;

            for (let i = 0; i < data.length; i += 4) {
                let r = originalData[i];
                let g = originalData[i + 1];
                let b = originalData[i + 2];

                // Apply skin smoothing
                if (adjustments.skinSmooth > 0) {
                    const smoothFactor = adjustments.skinSmooth / 100;
                    const avg = (r + g + b) / 3;
                    r = r * (1 - smoothFactor * 0.3) + avg * (smoothFactor * 0.3);
                    g = g * (1 - smoothFactor * 0.3) + avg * (smoothFactor * 0.3);
                    b = b * (1 - smoothFactor * 0.3) + avg * (smoothFactor * 0.3);
                }

                // Apply eye brightening (simplified - affects lighter areas)
                if (adjustments.eyeBrighten > 0) {
                    const brightness = (r + g + b) / 3;
                    if (brightness > 150) { // Likely eye area
                        const brightenFactor = 1 + (adjustments.eyeBrighten / 100) * 0.2;
                        r = Math.min(255, r * brightenFactor);
                        g = Math.min(255, g * brightenFactor);
                        b = Math.min(255, b * brightenFactor);
                    }
                }

                data[i] = r;
                data[i + 1] = g;
                data[i + 2] = b;
                data[i + 3] = originalData[i + 3];
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        function updateAutoButtonText() {
            const button = document.getElementById('autoButton');
            if (!button) return;

            const modes = [
                { text: '✨ Auto: Subtle', color: 'linear-gradient(135deg, #28a745, #20c997)' },
                { text: '💫 Auto: Medium', color: 'linear-gradient(135deg, #007bff, #0056b3)' },
                { text: '🌟 Auto: Strong', color: 'linear-gradient(135deg, #dc3545, #c82333)' }
            ];

            const currentMode = modes[autoMode];
            button.textContent = currentMode.text;
            button.style.background = currentMode.color;
        }

        function resetAll() {
            console.log('🔄 Resetting all adjustments...');

            if (originalImageData) {
                ctx.putImageData(originalImageData, 0, 0);
                currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            }

            // Reset all sliders
            Object.keys(adjustments).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = 0;
                    updateSetting(key, 0);
                }
            });

            console.log('✅ All adjustments reset');
        }

        function hideUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }
        }

        function downloadImage() {
            if (!canvas) {
                alert('Žádný obrázek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `retouched_image_${Date.now()}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
            console.log('📥 Image downloaded');
        }

        // Help System Functions (same as other tools)
        function toggleHelp() {
            helpMode = !helpMode;
            const helpToggle = document.getElementById('helpToggle');

            if (helpMode) {
                helpToggle.classList.add('active');
                helpToggle.textContent = '✕';
                helpToggle.title = 'Vypnout nápovědu';
                setupHelpListeners();
                showWelcomeTooltip();
            } else {
                helpToggle.classList.remove('active');
                helpToggle.textContent = '?';
                helpToggle.title = 'Zapnout nápovědu';
                removeHelpListeners();
                hideTooltip();
            }

            console.log('🔧 Help mode:', helpMode ? 'ON' : 'OFF');
        }

        function setupHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.addEventListener('mouseenter', showTooltip);
                element.addEventListener('mouseleave', hideTooltip);
            });
        }

        function removeHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.removeEventListener('mouseenter', showTooltip);
                element.removeEventListener('mouseleave', hideTooltip);
                element.classList.remove('help-active');
            });
        }

        function showTooltip(event) {
            if (!helpMode) return;

            const element = event.currentTarget;
            const helpText = element.getAttribute('data-help');
            if (!helpText) return;

            const [title, description] = helpText.split('|');

            hideTooltip();

            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">${title}</div>
                <div>${description}</div>
            `;

            const rect = element.getBoundingClientRect();

            // Smart positioning
            const tooltipWidth = 280;
            const tooltipHeight = 80;

            let left = rect.left;
            let top = rect.bottom + 10;

            if (left + tooltipWidth > window.innerWidth) {
                left = window.innerWidth - tooltipWidth - 20;
            }

            if (top + tooltipHeight > window.innerHeight) {
                top = rect.top - tooltipHeight - 10;
            }

            if (left < 10) {
                left = 10;
            }

            if (top < 10) {
                top = rect.bottom + 10;
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            document.body.appendChild(tooltip);
            currentTooltip = tooltip;

            element.classList.add('help-active');
        }

        function hideTooltip() {
            if (currentTooltip) {
                currentTooltip.remove();
                currentTooltip = null;
            }

            const activeElements = document.querySelectorAll('.help-active');
            activeElements.forEach(el => el.classList.remove('help-active'));
        }

        function showWelcomeTooltip() {
            const helpToggle = document.getElementById('helpToggle');
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">Retuš nápověda! 🖌️</div>
                <div>Najeďte myší na jakýkoliv nástroj pro zobrazení nápovědy o retušovacích technikách.</div>
            `;

            const rect = helpToggle.getBoundingClientRect();
            tooltip.style.left = (rect.left - 200) + 'px';
            tooltip.style.top = (rect.bottom + 10) + 'px';

            document.body.appendChild(tooltip);

            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 3000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded');
            if (initializeApp()) {
                console.log('✅ Retouch Tools app initialized successfully');
            } else {
                console.log('❌ Retouch Tools app initialization failed');
            }
        });
    </script>
</body>
</html>
