<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Retouch Tools</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }
        
        .retouch-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: 100vh;
        }
        
        .tools-panel {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #404040;
            overflow-y: auto;
        }
        
        .canvas-area {
            background: #1a1a1a;
            border-radius: 8px;
            border: 1px solid #404040;
            position: relative;
            overflow: hidden;
        }
        
        .tool-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 15px;
            border-bottom: 1px solid #404040;
            padding-bottom: 8px;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .tool-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 12px 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .tool-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .tool-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }
        
        .tool-icon {
            font-size: 18px;
        }
        
        .tool-settings {
            background: #252525;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
            border: 1px solid #3a3a3a;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .setting-row:last-child {
            margin-bottom: 0;
        }
        
        .setting-label {
            font-size: 11px;
            color: #ccc;
            width: 80px;
            flex-shrink: 0;
        }
        
        .setting-slider {
            flex: 1;
            margin: 0 8px;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .setting-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .setting-value {
            font-size: 11px;
            color: #00d4ff;
            width: 35px;
            text-align: right;
            font-weight: 500;
        }
        
        .main-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }
        
        .brush-preview {
            position: absolute;
            border: 2px solid #00d4ff;
            border-radius: 50%;
            pointer-events: none;
            display: none;
            transform: translate(-50%, -50%);
            opacity: 0.7;
        }
        
        .tool-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 11px;
            color: #ccc;
        }
        
        .before-after-slider {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            padding: 10px 20px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .comparison-slider {
            width: 200px;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .comparison-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .zoom-button {
            background: rgba(0,0,0,0.8);
            border: 1px solid #555;
            color: #e0e0e0;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .zoom-button:hover {
            background: rgba(0,212,255,0.2);
            border-color: #00d4ff;
        }
        
        .progress-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
        }
        
        .progress-spinner {
            border: 3px solid #404040;
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .progress-text {
            color: #e0e0e0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="retouch-container">
        <div class="tools-panel">
            <div class="tool-section">
                <div class="section-title">🖌️ Retuš nástroje</div>
                <div class="tool-grid">
                    <div class="tool-button active" onclick="selectTool('clone')" data-tool="clone">
                        <div class="tool-icon">📋</div>
                        <div>Clone Stamp</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('healing')" data-tool="healing">
                        <div class="tool-icon">🩹</div>
                        <div>Healing Brush</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('spot')" data-tool="spot">
                        <div class="tool-icon">🎯</div>
                        <div>Spot Removal</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('redeye')" data-tool="redeye">
                        <div class="tool-icon">👁️</div>
                        <div>Red Eye</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('smooth')" data-tool="smooth">
                        <div class="tool-icon">✨</div>
                        <div>Skin Smooth</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('sharpen')" data-tool="sharpen">
                        <div class="tool-icon">🔍</div>
                        <div>Sharpen</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('blur')" data-tool="blur">
                        <div class="tool-icon">💫</div>
                        <div>Blur</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('dodge')" data-tool="dodge">
                        <div class="tool-icon">☀️</div>
                        <div>Dodge</div>
                    </div>
                </div>
                
                <div class="tool-settings">
                    <div class="setting-row">
                        <span class="setting-label">Velikost</span>
                        <input type="range" class="setting-slider" id="brushSize" min="1" max="200" value="20">
                        <span class="setting-value" id="brushSizeValue">20</span>
                    </div>
                    <div class="setting-row">
                        <span class="setting-label">Tvrdost</span>
                        <input type="range" class="setting-slider" id="brushHardness" min="0" max="100" value="80">
                        <span class="setting-value" id="brushHardnessValue">80</span>
                    </div>
                    <div class="setting-row">
                        <span class="setting-label">Opacity</span>
                        <input type="range" class="setting-slider" id="brushOpacity" min="1" max="100" value="100">
                        <span class="setting-value" id="brushOpacityValue">100</span>
                    </div>
                    <div class="setting-row">
                        <span class="setting-label">Flow</span>
                        <input type="range" class="setting-slider" id="brushFlow" min="1" max="100" value="100">
                        <span class="setting-value" id="brushFlowValue">100</span>
                    </div>
                </div>
            </div>
            
            <div class="tool-section">
                <div class="section-title">🎨 Automatické nástroje</div>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <button class="tool-button" onclick="autoFaceSmooth()" style="width: 100%;">
                        <div class="tool-icon">😊</div>
                        <div>Auto Face Smooth</div>
                    </button>
                    <button class="tool-button" onclick="autoSpotRemoval()" style="width: 100%;">
                        <div class="tool-icon">🔍</div>
                        <div>Auto Spot Detection</div>
                    </button>
                    <button class="tool-button" onclick="autoRedEyeRemoval()" style="width: 100%;">
                        <div class="tool-icon">👁️‍🗨️</div>
                        <div>Auto Red Eye Fix</div>
                    </button>
                    <button class="tool-button" onclick="autoSkinTone()" style="width: 100%;">
                        <div class="tool-icon">🎭</div>
                        <div>Auto Skin Tone</div>
                    </button>
                </div>
            </div>
            
            <div class="tool-section">
                <div class="section-title">⚙️ Nastavení</div>
                <div class="tool-settings">
                    <div class="setting-row">
                        <span class="setting-label">Zoom</span>
                        <input type="range" class="setting-slider" id="zoomLevel" min="25" max="800" value="100">
                        <span class="setting-value" id="zoomLevelValue">100%</span>
                    </div>
                    <div class="setting-row">
                        <label style="font-size: 11px; color: #ccc;">
                            <input type="checkbox" id="showBrushPreview" checked> Náhled štětce
                        </label>
                    </div>
                    <div class="setting-row">
                        <label style="font-size: 11px; color: #ccc;">
                            <input type="checkbox" id="pressureSensitive"> Citlivost na tlak
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="canvas-area">
            <canvas class="main-canvas" id="retouchCanvas"></canvas>
            <div class="brush-preview" id="brushPreview"></div>
            
            <div class="tool-info" id="toolInfo">
                Clone Stamp Tool - Alt+Click pro nastavení zdroje
            </div>
            
            <div class="zoom-controls">
                <button class="zoom-button" onclick="zoomIn()">+</button>
                <button class="zoom-button" onclick="zoomOut()">-</button>
                <button class="zoom-button" onclick="zoomFit()">⌂</button>
            </div>
            
            <div class="before-after-slider">
                <span style="font-size: 11px;">Před</span>
                <input type="range" class="comparison-slider" id="beforeAfter" min="0" max="100" value="100">
                <span style="font-size: 11px;">Po</span>
            </div>
            
            <div class="progress-overlay" id="progressOverlay">
                <div class="progress-spinner"></div>
                <div class="progress-text" id="progressText">Zpracování...</div>
            </div>
        </div>
    </div>

    <script>
        class RetouchTools {
            constructor() {
                this.canvas = document.getElementById('retouchCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.originalImageData = null;
                this.workingImageData = null;
                this.currentTool = 'clone';
                this.isDrawing = false;
                this.lastX = 0;
                this.lastY = 0;
                this.cloneSourceX = 0;
                this.cloneSourceY = 0;
                this.hasCloneSource = false;
                this.zoomLevel = 1;
                this.panX = 0;
                this.panY = 0;

                this.brushSettings = {
                    size: 20,
                    hardness: 80,
                    opacity: 100,
                    flow: 100
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Set up canvas for high DPI displays
                const dpr = window.devicePixelRatio || 1;
                const rect = this.canvas.getBoundingClientRect();
                this.canvas.width = rect.width * dpr;
                this.canvas.height = rect.height * dpr;
                this.ctx.scale(dpr, dpr);
                this.canvas.style.width = rect.width + 'px';
                this.canvas.style.height = rect.height + 'px';
            }

            initializeEventListeners() {
                // Canvas events
                this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));

                // Brush settings
                ['brushSize', 'brushHardness', 'brushOpacity', 'brushFlow', 'zoomLevel'].forEach(id => {
                    const slider = document.getElementById(id);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateSetting(id, e.target.value);
                        });
                    }
                });

                // Before/After slider
                const beforeAfterSlider = document.getElementById('beforeAfter');
                if (beforeAfterSlider) {
                    beforeAfterSlider.addEventListener('input', (e) => {
                        this.showBeforeAfter(e.target.value / 100);
                    });
                }

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.altKey && this.currentTool === 'clone') {
                        this.canvas.style.cursor = 'crosshair';
                    }

                    // Tool shortcuts
                    switch(e.key) {
                        case 'c': if (e.ctrlKey) break; this.selectTool('clone'); break;
                        case 'h': this.selectTool('healing'); break;
                        case 's': if (e.ctrlKey) break; this.selectTool('spot'); break;
                        case 'b': this.selectTool('blur'); break;
                        case 'd': this.selectTool('dodge'); break;
                        case '[': this.adjustBrushSize(-5); break;
                        case ']': this.adjustBrushSize(5); break;
                    }
                });

                document.addEventListener('keyup', (e) => {
                    if (!e.altKey) {
                        this.updateCursor();
                    }
                });
            }

            loadTestImage() {
                // Create a test pattern
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Draw gradient background
                const gradient = this.ctx.createLinearGradient(0, 0, 800, 600);
                gradient.addColorStop(0, '#4a5568');
                gradient.addColorStop(1, '#2d3748');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, 800, 600);

                // Add some test elements
                this.ctx.fillStyle = '#e53e3e';
                this.ctx.beginPath();
                this.ctx.arc(200, 150, 30, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#38a169';
                this.ctx.fillRect(400, 200, 100, 80);

                this.ctx.fillStyle = '#3182ce';
                this.ctx.beginPath();
                this.ctx.arc(600, 400, 50, 0, 2 * Math.PI);
                this.ctx.fill();

                // Add text
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = '24px Arial';
                this.ctx.fillText('Test Image for Retouching', 250, 500);

                // Store original
                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.workingImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );
            }

            handleMouseDown(e) {
                const rect = this.canvas.getBoundingClientRect();
                const x = (e.clientX - rect.left) / this.zoomLevel - this.panX;
                const y = (e.clientY - rect.top) / this.zoomLevel - this.panY;

                if (e.altKey && this.currentTool === 'clone') {
                    // Set clone source
                    this.cloneSourceX = x;
                    this.cloneSourceY = y;
                    this.hasCloneSource = true;
                    this.showCloneSource(x, y);
                    return;
                }

                this.isDrawing = true;
                this.lastX = x;
                this.lastY = y;

                this.applyTool(x, y, false);
            }

            handleMouseMove(e) {
                const rect = this.canvas.getBoundingClientRect();
                const x = (e.clientX - rect.left) / this.zoomLevel - this.panX;
                const y = (e.clientY - rect.top) / this.zoomLevel - this.panY;

                // Update brush preview
                this.updateBrushPreview(e.clientX, e.clientY);

                if (this.isDrawing) {
                    this.applyTool(x, y, true);
                    this.lastX = x;
                    this.lastY = y;
                }
            }

            handleMouseUp(e) {
                this.isDrawing = false;
            }

            handleWheel(e) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                this.zoomLevel = Math.max(0.1, Math.min(5, this.zoomLevel * delta));
                this.updateZoomDisplay();
                this.redraw();
            }

            applyTool(x, y, continuous) {
                const size = this.brushSettings.size;
                const opacity = this.brushSettings.opacity / 100;
                const flow = this.brushSettings.flow / 100;

                switch(this.currentTool) {
                    case 'clone':
                        this.applyCloneStamp(x, y, size, opacity);
                        break;
                    case 'healing':
                        this.applyHealingBrush(x, y, size, opacity);
                        break;
                    case 'spot':
                        this.applySpotRemoval(x, y, size);
                        break;
                    case 'smooth':
                        this.applySkinSmoothing(x, y, size, opacity);
                        break;
                    case 'sharpen':
                        this.applySharpen(x, y, size, opacity);
                        break;
                    case 'blur':
                        this.applyBlur(x, y, size, opacity);
                        break;
                    case 'dodge':
                        this.applyDodge(x, y, size, opacity);
                        break;
                }

                this.redraw();
            }

            applyCloneStamp(x, y, size, opacity) {
                if (!this.hasCloneSource) return;

                const sourceData = this.getCircularArea(
                    this.originalImageData,
                    this.cloneSourceX,
                    this.cloneSourceY,
                    size
                );

                this.blendCircularArea(
                    this.workingImageData,
                    x, y, size,
                    sourceData,
                    opacity
                );
            }

            applyHealingBrush(x, y, size, opacity) {
                // Healing brush samples surrounding area and blends
                const surroundingData = this.getSurroundingAverage(x, y, size * 2);
                const targetData = this.getCircularArea(this.workingImageData, x, y, size);

                // Blend with surrounding colors
                this.healingBlend(this.workingImageData, x, y, size, surroundingData, opacity);
            }

            applySpotRemoval(x, y, size) {
                // Automatic spot removal using content-aware fill
                this.contentAwareFill(this.workingImageData, x, y, size);
            }

            applySkinSmoothing(x, y, size, opacity) {
                // Gaussian blur with edge preservation
                const blurredData = this.gaussianBlur(
                    this.getCircularArea(this.workingImageData, x, y, size),
                    2
                );

                this.blendCircularArea(
                    this.workingImageData,
                    x, y, size,
                    blurredData,
                    opacity * 0.5 // Subtle effect
                );
            }

            applySharpen(x, y, size, opacity) {
                const kernel = [
                    0, -1, 0,
                    -1, 5, -1,
                    0, -1, 0
                ];

                this.applyConvolutionFilter(x, y, size, kernel, opacity);
            }

            applyBlur(x, y, size, opacity) {
                const kernel = [
                    1/9, 1/9, 1/9,
                    1/9, 1/9, 1/9,
                    1/9, 1/9, 1/9
                ];

                this.applyConvolutionFilter(x, y, size, kernel, opacity);
            }

            applyDodge(x, y, size, opacity) {
                // Lighten the area
                const imageData = this.workingImageData;
                const data = imageData.data;
                const width = imageData.width;

                for (let dy = -size; dy <= size; dy++) {
                    for (let dx = -size; dx <= size; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= size) {
                            const px = Math.round(x + dx);
                            const py = Math.round(y + dy);

                            if (px >= 0 && px < width && py >= 0 && py < imageData.height) {
                                const index = (py * width + px) * 4;
                                const factor = 1 + (opacity * 0.1 * (1 - distance / size));

                                data[index] = Math.min(255, data[index] * factor);
                                data[index + 1] = Math.min(255, data[index + 1] * factor);
                                data[index + 2] = Math.min(255, data[index + 2] * factor);
                            }
                        }
                    }
                }
            }
        }

            getCircularArea(imageData, centerX, centerY, radius) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                const result = [];

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= radius) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 0 && x < width && y >= 0 && y < height) {
                                const index = (y * width + x) * 4;
                                result.push({
                                    x: dx,
                                    y: dy,
                                    r: data[index],
                                    g: data[index + 1],
                                    b: data[index + 2],
                                    a: data[index + 3]
                                });
                            }
                        }
                    }
                }

                return result;
            }

            blendCircularArea(imageData, centerX, centerY, radius, sourceData, opacity) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;

                sourceData.forEach(pixel => {
                    const x = Math.round(centerX + pixel.x);
                    const y = Math.round(centerY + pixel.y);

                    if (x >= 0 && x < width && y >= 0 && y < height) {
                        const index = (y * width + x) * 4;
                        const distance = Math.sqrt(pixel.x * pixel.x + pixel.y * pixel.y);
                        const falloff = Math.max(0, 1 - distance / radius);
                        const blendOpacity = opacity * falloff;

                        data[index] = data[index] * (1 - blendOpacity) + pixel.r * blendOpacity;
                        data[index + 1] = data[index + 1] * (1 - blendOpacity) + pixel.g * blendOpacity;
                        data[index + 2] = data[index + 2] * (1 - blendOpacity) + pixel.b * blendOpacity;
                    }
                });
            }

            getSurroundingAverage(centerX, centerY, radius) {
                const data = this.workingImageData.data;
                const width = this.workingImageData.width;
                const height = this.workingImageData.height;

                let totalR = 0, totalG = 0, totalB = 0, count = 0;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance > radius * 0.5 && distance <= radius) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 0 && x < width && y >= 0 && y < height) {
                                const index = (y * width + x) * 4;
                                totalR += data[index];
                                totalG += data[index + 1];
                                totalB += data[index + 2];
                                count++;
                            }
                        }
                    }
                }

                return count > 0 ? {
                    r: totalR / count,
                    g: totalG / count,
                    b: totalB / count
                } : { r: 128, g: 128, b: 128 };
            }

            healingBlend(imageData, centerX, centerY, radius, surroundingColor, opacity) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= radius) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 0 && x < width && y >= 0 && y < height) {
                                const index = (y * width + x) * 4;
                                const falloff = Math.max(0, 1 - distance / radius);
                                const blendOpacity = opacity * falloff;

                                data[index] = data[index] * (1 - blendOpacity) + surroundingColor.r * blendOpacity;
                                data[index + 1] = data[index + 1] * (1 - blendOpacity) + surroundingColor.g * blendOpacity;
                                data[index + 2] = data[index + 2] * (1 - blendOpacity) + surroundingColor.b * blendOpacity;
                            }
                        }
                    }
                }
            }

            contentAwareFill(imageData, centerX, centerY, radius) {
                // Simple content-aware fill using surrounding pixels
                const surroundingPixels = [];
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;

                // Collect surrounding pixels
                for (let dy = -radius * 2; dy <= radius * 2; dy++) {
                    for (let dx = -radius * 2; dx <= radius * 2; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance > radius && distance <= radius * 2) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 0 && x < width && y >= 0 && y < height) {
                                const index = (y * width + x) * 4;
                                surroundingPixels.push({
                                    r: data[index],
                                    g: data[index + 1],
                                    b: data[index + 2]
                                });
                            }
                        }
                    }
                }

                // Fill the area with random surrounding pixels
                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= radius) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 0 && x < width && y >= 0 && y < height && surroundingPixels.length > 0) {
                                const randomPixel = surroundingPixels[Math.floor(Math.random() * surroundingPixels.length)];
                                const index = (y * width + x) * 4;

                                data[index] = randomPixel.r;
                                data[index + 1] = randomPixel.g;
                                data[index + 2] = randomPixel.b;
                            }
                        }
                    }
                }
            }

            gaussianBlur(pixelData, radius) {
                // Simple Gaussian blur implementation
                const blurred = [...pixelData];

                for (let i = 0; i < blurred.length; i++) {
                    let totalR = 0, totalG = 0, totalB = 0, totalWeight = 0;

                    for (let j = 0; j < pixelData.length; j++) {
                        const dx = pixelData[j].x - pixelData[i].x;
                        const dy = pixelData[j].y - pixelData[i].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance <= radius) {
                            const weight = Math.exp(-(distance * distance) / (2 * radius * radius));
                            totalR += pixelData[j].r * weight;
                            totalG += pixelData[j].g * weight;
                            totalB += pixelData[j].b * weight;
                            totalWeight += weight;
                        }
                    }

                    if (totalWeight > 0) {
                        blurred[i].r = totalR / totalWeight;
                        blurred[i].g = totalG / totalWeight;
                        blurred[i].b = totalB / totalWeight;
                    }
                }

                return blurred;
            }

            applyConvolutionFilter(centerX, centerY, radius, kernel, opacity) {
                const data = this.workingImageData.data;
                const width = this.workingImageData.width;
                const height = this.workingImageData.height;
                const output = new Uint8ClampedArray(data);

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= radius) {
                            const x = Math.round(centerX + dx);
                            const y = Math.round(centerY + dy);

                            if (x >= 1 && x < width - 1 && y >= 1 && y < height - 1) {
                                for (let channel = 0; channel < 3; channel++) {
                                    let sum = 0;
                                    for (let ky = -1; ky <= 1; ky++) {
                                        for (let kx = -1; kx <= 1; kx++) {
                                            const index = ((y + ky) * width + (x + kx)) * 4 + channel;
                                            const weight = kernel[(ky + 1) * 3 + (kx + 1)];
                                            sum += data[index] * weight;
                                        }
                                    }

                                    const index = (y * width + x) * 4 + channel;
                                    const falloff = Math.max(0, 1 - distance / radius);
                                    const blendOpacity = opacity * falloff;

                                    output[index] = data[index] * (1 - blendOpacity) +
                                                   Math.max(0, Math.min(255, sum)) * blendOpacity;
                                }
                            }
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            updateSetting(settingId, value) {
                const numValue = parseFloat(value);

                switch(settingId) {
                    case 'brushSize':
                        this.brushSettings.size = numValue;
                        document.getElementById('brushSizeValue').textContent = numValue;
                        break;
                    case 'brushHardness':
                        this.brushSettings.hardness = numValue;
                        document.getElementById('brushHardnessValue').textContent = numValue;
                        break;
                    case 'brushOpacity':
                        this.brushSettings.opacity = numValue;
                        document.getElementById('brushOpacityValue').textContent = numValue;
                        break;
                    case 'brushFlow':
                        this.brushSettings.flow = numValue;
                        document.getElementById('brushFlowValue').textContent = numValue;
                        break;
                    case 'zoomLevel':
                        this.zoomLevel = numValue / 100;
                        document.getElementById('zoomLevelValue').textContent = numValue + '%';
                        this.redraw();
                        break;
                }

                this.updateBrushPreview();
            }

            updateBrushPreview(clientX, clientY) {
                const preview = document.getElementById('brushPreview');
                const showPreview = document.getElementById('showBrushPreview').checked;

                if (showPreview && clientX !== undefined && clientY !== undefined) {
                    preview.style.display = 'block';
                    preview.style.left = clientX + 'px';
                    preview.style.top = clientY + 'px';
                    preview.style.width = (this.brushSettings.size * 2 * this.zoomLevel) + 'px';
                    preview.style.height = (this.brushSettings.size * 2 * this.zoomLevel) + 'px';
                } else {
                    preview.style.display = 'none';
                }
            }

            updateCursor() {
                switch(this.currentTool) {
                    case 'clone':
                        this.canvas.style.cursor = this.hasCloneSource ? 'copy' : 'crosshair';
                        break;
                    case 'healing':
                        this.canvas.style.cursor = 'crosshair';
                        break;
                    case 'spot':
                        this.canvas.style.cursor = 'pointer';
                        break;
                    default:
                        this.canvas.style.cursor = 'crosshair';
                }
            }

            showCloneSource(x, y) {
                // Visual indicator for clone source
                this.ctx.save();
                this.ctx.strokeStyle = '#00d4ff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.arc(x, y, 10, 0, 2 * Math.PI);
                this.ctx.stroke();
                this.ctx.restore();
            }

            redraw() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.save();
                this.ctx.scale(this.zoomLevel, this.zoomLevel);
                this.ctx.translate(this.panX, this.panY);
                this.ctx.putImageData(this.workingImageData, 0, 0);
                this.ctx.restore();
            }

            showBeforeAfter(ratio) {
                // Show before/after comparison
                const beforeData = this.originalImageData;
                const afterData = this.workingImageData;
                const splitX = this.canvas.width * ratio;

                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw before (left side)
                this.ctx.putImageData(beforeData, 0, 0, 0, 0, splitX, this.canvas.height);

                // Draw after (right side)
                this.ctx.putImageData(afterData, splitX, 0, splitX, 0, this.canvas.width - splitX, this.canvas.height);

                // Draw split line
                this.ctx.strokeStyle = '#00d4ff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(splitX, 0);
                this.ctx.lineTo(splitX, this.canvas.height);
                this.ctx.stroke();
            }

            adjustBrushSize(delta) {
                const newSize = Math.max(1, Math.min(200, this.brushSettings.size + delta));
                document.getElementById('brushSize').value = newSize;
                this.updateSetting('brushSize', newSize);
            }

            updateZoomDisplay() {
                document.getElementById('zoomLevel').value = this.zoomLevel * 100;
                document.getElementById('zoomLevelValue').textContent = Math.round(this.zoomLevel * 100) + '%';
            }
        }

        // Global functions
        function selectTool(toolName) {
            // Update button states
            document.querySelectorAll('.tool-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

            // Update tool
            if (retouchTools) {
                retouchTools.currentTool = toolName;
                retouchTools.updateCursor();

                // Update tool info
                const toolInfo = document.getElementById('toolInfo');
                const toolMessages = {
                    clone: 'Clone Stamp Tool - Alt+Click pro nastavení zdroje',
                    healing: 'Healing Brush - Automaticky míchá s okolními barvami',
                    spot: 'Spot Removal - Klikněte na skvrnu pro odstranění',
                    redeye: 'Red Eye Removal - Klikněte na červené oko',
                    smooth: 'Skin Smoothing - Jemné vyhlazení pleti',
                    sharpen: 'Sharpen Tool - Zaostření detailů',
                    blur: 'Blur Tool - Rozostření oblasti',
                    dodge: 'Dodge Tool - Zesvětlení oblasti'
                };
                toolInfo.textContent = toolMessages[toolName] || 'Nástroj vybrán';
            }
        }

        function autoFaceSmooth() {
            showProgress('Detekce obličeje a vyhlazení pleti...');
            setTimeout(() => {
                hideProgress();
                alert('Auto Face Smooth bude implementován s AI detekcí obličeje.');
            }, 2000);
        }

        function autoSpotRemoval() {
            showProgress('Detekce skvrn a automatické odstranění...');
            setTimeout(() => {
                hideProgress();
                alert('Auto Spot Detection bude implementován s AI detekcí defektů.');
            }, 2000);
        }

        function autoRedEyeRemoval() {
            showProgress('Detekce červených očí...');
            setTimeout(() => {
                hideProgress();
                alert('Auto Red Eye Fix bude implementován s detekcí očí.');
            }, 1500);
        }

        function autoSkinTone() {
            showProgress('Analýza a korekce tónu pleti...');
            setTimeout(() => {
                hideProgress();
                alert('Auto Skin Tone bude implementován s AI analýzou pleti.');
            }, 2000);
        }

        function zoomIn() {
            if (retouchTools) {
                retouchTools.zoomLevel = Math.min(5, retouchTools.zoomLevel * 1.2);
                retouchTools.updateZoomDisplay();
                retouchTools.redraw();
            }
        }

        function zoomOut() {
            if (retouchTools) {
                retouchTools.zoomLevel = Math.max(0.1, retouchTools.zoomLevel / 1.2);
                retouchTools.updateZoomDisplay();
                retouchTools.redraw();
            }
        }

        function zoomFit() {
            if (retouchTools) {
                retouchTools.zoomLevel = 1;
                retouchTools.panX = 0;
                retouchTools.panY = 0;
                retouchTools.updateZoomDisplay();
                retouchTools.redraw();
            }
        }

        function showProgress(message) {
            document.getElementById('progressText').textContent = message;
            document.getElementById('progressOverlay').style.display = 'flex';
        }

        function hideProgress() {
            document.getElementById('progressOverlay').style.display = 'none';
        }

        // Initialize the retouch tools
        let retouchTools;
        document.addEventListener('DOMContentLoaded', () => {
            retouchTools = new RetouchTools();
        });
