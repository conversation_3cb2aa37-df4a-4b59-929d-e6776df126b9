<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .adjustments-container {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 1fr;
            grid-template-areas: "controls canvas histogram";
            height: 100vh;
            gap: 0;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-right: 1px solid #404040;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .histogram-panel {
            grid-area: histogram;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-left: 1px solid #404040;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
            border: 1px solid #404040;
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px dashed #00d4ff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .control-group {
            margin-bottom: 20px;
            background: #252525;
            padding: 15px;
            border-radius: 6px;
        }
        
        .control-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #ccc;
        }
        
        .control-value {
            color: #00d4ff;
            font-weight: bold;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .auto-button {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        
        .auto-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }
        
        .preset-button {
            background: #404040;
            color: #e0e0e0;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 11px;
            transition: all 0.2s;
        }
        
        .preset-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .histogram-canvas {
            width: 100%;
            height: 150px;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="adjustments-container">
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="panel-title">⚡ Základní úpravy</div>
            
            <button class="auto-button" onclick="autoAdjust()">🤖 Automatické úpravy</button>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Exposure</span>
                    <span class="control-value" id="exposureValue">0</span>
                </div>
                <input type="range" class="control-slider" id="exposure" min="-2" max="2" value="0" step="0.1">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Kontrast</span>
                    <span class="control-value" id="contrastValue">0</span>
                </div>
                <input type="range" class="control-slider" id="contrast" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Jas</span>
                    <span class="control-value" id="brightnessValue">0</span>
                </div>
                <input type="range" class="control-slider" id="brightness" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Sytost</span>
                    <span class="control-value" id="saturationValue">0</span>
                </div>
                <input type="range" class="control-slider" id="saturation" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Vibrance</span>
                    <span class="control-value" id="vibranceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="vibrance" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Světla</span>
                    <span class="control-value" id="highlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="highlights" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Stíny</span>
                    <span class="control-value" id="shadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="shadows" min="-100" max="100" value="0">
            </div>
            
            <div style="margin-top: 20px;">
                <button class="preset-button" onclick="applyPreset('natural')">Natural</button>
                <button class="preset-button" onclick="applyPreset('vivid')">Vivid</button>
                <button class="preset-button" onclick="applyPreset('dramatic')">Dramatic</button>
                <button class="preset-button" onclick="resetAll()" style="width: 100%; margin-top: 10px;">🔄 Reset vše</button>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas" width="800" height="600"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Histogram Panel -->
        <div class="histogram-panel">
            <div class="panel-title">📊 Histogram</div>
            <canvas class="histogram-canvas" id="histogramCanvas" width="270" height="150"></canvas>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="toggleChannel('rgb')" id="rgbBtn">RGB</button>
                <button class="preset-button" onclick="toggleChannel('red')" id="redBtn">R</button>
                <button class="preset-button" onclick="toggleChannel('green')" id="greenBtn">G</button>
                <button class="preset-button" onclick="toggleChannel('blue')" id="blueBtn">B</button>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="downloadImage()" style="width: 100%;">📥 Stáhnout</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let canvas, ctx, histogramCanvas, histogramCtx;
        let originalImageData, currentImageData;
        let histogramMode = 'rgb';
        
        const adjustments = {
            exposure: 0,
            contrast: 0,
            brightness: 0,
            saturation: 0,
            vibrance: 0,
            highlights: 0,
            shadows: 0
        };
        
        function initializeApp() {
            console.log('🚀 Initializing Basic Adjustments...');
            
            // Get canvas elements
            canvas = document.getElementById('mainCanvas');
            if (!canvas) {
                console.error('❌ Main canvas not found!');
                return false;
            }
            
            ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ Canvas context not found!');
                return false;
            }
            
            histogramCanvas = document.getElementById('histogramCanvas');
            histogramCtx = histogramCanvas ? histogramCanvas.getContext('2d') : null;
            
            console.log('✅ Canvas elements initialized');
            
            // Setup event listeners
            setupEventListeners();
            
            // Load test image
            loadTestImage();
            
            console.log('✅ Basic Adjustments initialized successfully');
            return true;
        }
        
        function setupEventListeners() {
            // File input
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }
            
            // Upload zone drag & drop
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('drop', handleDrop);
                uploadZone.addEventListener('dragleave', (e) => {
                    e.currentTarget.style.background = '';
                });
            }
            
            // Adjustment sliders
            Object.keys(adjustments).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.addEventListener('input', (e) => {
                        updateAdjustment(key, parseFloat(e.target.value));
                    });
                }
            });
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('📁 Loading file:', file.name);
                loadImageFile(file);
            }
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.background = 'rgba(0, 212, 255, 0.15)';
        }
        
        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.background = '';
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                console.log('📁 Loading dropped file:', files[0].name);
                loadImageFile(files[0]);
            }
        }

        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    console.log('🖼️ Image loaded:', img.width, 'x', img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    console.error('❌ Error loading image');
                    alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('❌ Error reading file');
                alert('Chyba při čtení souboru.');
            };
            reader.readAsDataURL(file);
        }

        function drawImageToCanvas(img) {
            // Calculate size to fit canvas
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;

            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;

            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;

            // Clear with black background
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, maxWidth, maxHeight);

            // Draw image centered
            ctx.drawImage(img, x, y, width, height);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);
            currentImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);

            // Hide upload zone
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }

            updateHistogram();
            console.log('✅ Image drawn to canvas');
        }

        function loadTestImage() {
            console.log('🎨 Loading test image...');

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add shapes
            ctx.fillStyle = '#fff';
            ctx.fillRect(100, 100, 200, 150);

            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.arc(600, 200, 80, 0, 2 * Math.PI);
            ctx.fill();

            // Add text
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('BASIC ADJUSTMENTS', 200, 400);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // Hide upload zone
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }

            updateHistogram();
            console.log('✅ Test image loaded');
        }

        function updateAdjustment(type, value) {
            adjustments[type] = value;
            document.getElementById(type + 'Value').textContent = value;
            applyAdjustments();
            updateHistogram();
        }

        function applyAdjustments() {
            if (!originalImageData) return;

            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;

            for (let i = 0; i < data.length; i += 4) {
                let r = originalData[i];
                let g = originalData[i + 1];
                let b = originalData[i + 2];

                // Apply exposure first
                const exposure = Math.pow(2, adjustments.exposure);
                r *= exposure;
                g *= exposure;
                b *= exposure;

                // Apply brightness
                r += adjustments.brightness;
                g += adjustments.brightness;
                b += adjustments.brightness;

                // Apply contrast
                const contrast = adjustments.contrast;
                const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
                r = factor * (r - 128) + 128;
                g = factor * (g - 128) + 128;
                b = factor * (b - 128) + 128;

                // Apply saturation
                if (adjustments.saturation !== 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const satFactor = 1 + (adjustments.saturation / 100);
                    r = gray + satFactor * (r - gray);
                    g = gray + satFactor * (g - gray);
                    b = gray + satFactor * (b - gray);
                }

                // Apply vibrance (selective saturation)
                if (adjustments.vibrance !== 0) {
                    const max = Math.max(r, g, b);
                    const avg = (r + g + b) / 3;
                    const amt = ((Math.abs(max - avg) * 2 / 255) * (adjustments.vibrance / 100));

                    if (r !== max) r += (max - r) * amt;
                    if (g !== max) g += (max - g) * amt;
                    if (b !== max) b += (max - b) * amt;
                }

                // Apply highlights (reduce bright areas)
                if (adjustments.highlights !== 0) {
                    const brightness = (r + g + b) / 3;
                    if (brightness > 128) {
                        const factor = 1 - (adjustments.highlights / 100) * ((brightness - 128) / 127);
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }
                }

                // Apply shadows (brighten dark areas)
                if (adjustments.shadows !== 0) {
                    const brightness = (r + g + b) / 3;
                    if (brightness < 128) {
                        const factor = 1 + (adjustments.shadows / 100) * ((128 - brightness) / 128);
                        r *= factor;
                        g *= factor;
                        b *= factor;
                    }
                }

                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
                data[i + 3] = originalData[i + 3]; // Alpha
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        function updateHistogram() {
            if (!histogramCtx || !currentImageData) return;

            // Clear histogram
            histogramCtx.fillStyle = '#1a1a1a';
            histogramCtx.fillRect(0, 0, histogramCanvas.width, histogramCanvas.height);

            // Calculate histogram data
            const histogram = new Array(256).fill(0);
            const data = currentImageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const brightness = Math.round((data[i] + data[i + 1] + data[i + 2]) / 3);
                histogram[brightness]++;
            }

            // Find max value for scaling
            const maxValue = Math.max(...histogram);

            // Draw histogram
            histogramCtx.strokeStyle = '#00d4ff';
            histogramCtx.lineWidth = 1;
            histogramCtx.beginPath();

            for (let i = 0; i < 256; i++) {
                const x = (i / 255) * histogramCanvas.width;
                const y = histogramCanvas.height - (histogram[i] / maxValue) * histogramCanvas.height;

                if (i === 0) {
                    histogramCtx.moveTo(x, y);
                } else {
                    histogramCtx.lineTo(x, y);
                }
            }

            histogramCtx.stroke();
        }

        // Global functions for buttons
        function autoAdjust() {
            console.log('🤖 Auto adjusting...');

            // Intelligent auto adjustments
            updateAdjustment('exposure', 0.1);
            updateAdjustment('contrast', 15);
            updateAdjustment('brightness', 8);
            updateAdjustment('saturation', 12);
            updateAdjustment('vibrance', 15);
            updateAdjustment('highlights', -15);
            updateAdjustment('shadows', 20);

            console.log('✅ Auto adjustments applied');
        }

        function applyPreset(presetName) {
            console.log('🎨 Applying preset:', presetName);
            const presets = {
                natural: {
                    exposure: 0.1,
                    contrast: 10,
                    brightness: 5,
                    saturation: 8,
                    vibrance: 12,
                    highlights: -10,
                    shadows: 15
                },
                vivid: {
                    exposure: 0.2,
                    contrast: 25,
                    brightness: 10,
                    saturation: 35,
                    vibrance: 40,
                    highlights: -20,
                    shadows: 20
                },
                dramatic: {
                    exposure: -0.1,
                    contrast: 45,
                    brightness: -5,
                    saturation: 25,
                    vibrance: 30,
                    highlights: -35,
                    shadows: 40
                }
            };

            const preset = presets[presetName];
            if (preset) {
                Object.keys(preset).forEach(key => {
                    if (adjustments.hasOwnProperty(key)) {
                        const slider = document.getElementById(key);
                        if (slider) {
                            slider.value = preset[key];
                            updateAdjustment(key, preset[key]);
                        }
                    }
                });
            }
        }

        function resetAll() {
            console.log('🔄 Resetting all adjustments...');
            Object.keys(adjustments).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = 0;
                    updateAdjustment(key, 0);
                }
            });
        }

        function toggleChannel(channel) {
            histogramMode = channel;
            updateHistogram();

            // Update button states
            ['rgb', 'red', 'green', 'blue'].forEach(ch => {
                const btn = document.getElementById(ch + 'Btn');
                if (btn) {
                    btn.style.background = ch === channel ? '#00d4ff' : '#404040';
                    btn.style.color = ch === channel ? '#000' : '#e0e0e0';
                }
            });
        }

        function downloadImage() {
            if (!canvas) {
                alert('Žádný obrázek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `enhanced_image_${Date.now()}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
            console.log('📥 Image downloaded');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded');
            if (initializeApp()) {
                console.log('✅ App initialized successfully');
            } else {
                console.log('❌ App initialization failed');
            }
        });
    </script>
</body>
</html>
