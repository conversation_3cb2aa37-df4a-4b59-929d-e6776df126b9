<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Grading - Professional Image Enhancer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .grading-container {
            display: grid;
            grid-template-areas:
                "wheels canvas hsl"
                "lut canvas creative";
            grid-template-columns: 280px 1fr 280px;
            grid-template-rows: 1fr 1fr;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .wheels-panel {
            grid-area: wheels;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .hsl-panel {
            grid-area: hsl;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            overflow-y: auto;
        }
        
        .lut-panel {
            grid-area: lut;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .creative-panel {
            grid-area: creative;
            background: #252525;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .color-wheel {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 10px auto;
            position: relative;
            cursor: pointer;
            border: 2px solid #404040;
        }
        
        .wheel-center {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #fff;
            border: 2px solid #000;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        
        .wheel-label {
            text-align: center;
            font-size: 11px;
            color: #ccc;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 12px;
        }
        
        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-value {
            font-size: 11px;
            color: #00d4ff;
            font-weight: 500;
        }
        
        .control-slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 6px;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 3px dashed #00d4ff;
            border-radius: 12px;
            padding: 60px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
            z-index: 100;
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.05);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }
        
        .upload-text {
            font-size: 14px;
            color: #888;
        }
        
        .lut-preview {
            width: 100%;
            height: 60px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .lut-preview:hover {
            border-color: #00d4ff;
        }
        
        .lut-preview.active {
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
        
        .preset-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            margin: 4px;
            display: inline-block;
            width: calc(50% - 8px);
        }
        
        .preset-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .preset-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }
        
        .hsl-color-bar {
            width: 100%;
            height: 20px;
            border-radius: 4px;
            margin: 8px 0;
            border: 1px solid #404040;
        }
        
        .before-after {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .comparison-canvas {
            flex: 1;
            height: 100px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .canvas-label {
            font-size: 10px;
            color: #888;
            text-align: center;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="grading-container">
        <!-- Color Wheels Panel -->
        <div class="wheels-panel">
            <div class="panel-title">🎨 3-Way Color Correction</div>
            
            <div class="wheel-label">Shadows</div>
            <canvas class="color-wheel" id="shadowsWheel" width="120" height="120"></canvas>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Lift</span>
                    <span class="control-value" id="shadowsLiftValue">0</span>
                </div>
                <input type="range" class="control-slider" id="shadowsLift" min="-100" max="100" value="0">
            </div>
            
            <div class="wheel-label">Midtones</div>
            <canvas class="color-wheel" id="midtonesWheel" width="120" height="120"></canvas>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Gamma</span>
                    <span class="control-value" id="midtonesGammaValue">0</span>
                </div>
                <input type="range" class="control-slider" id="midtonesGamma" min="-100" max="100" value="0">
            </div>
            
            <div class="wheel-label">Highlights</div>
            <canvas class="color-wheel" id="highlightsWheel" width="120" height="120"></canvas>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Gain</span>
                    <span class="control-value" id="highlightsGainValue">0</span>
                </div>
                <input type="range" class="control-slider" id="highlightsGain" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- HSL Panel -->
        <div class="hsl-panel">
            <div class="panel-title">🌈 HSL Adjustments</div>
            
            <div class="control-group">
                <div class="control-label">Red</div>
                <div class="hsl-color-bar" style="background: linear-gradient(90deg, #ff0000, #ff8080);"></div>
                <div class="control-label">
                    <span>Hue</span>
                    <span class="control-value" id="redHueValue">0</span>
                </div>
                <input type="range" class="control-slider" id="redHue" min="-180" max="180" value="0">
                <div class="control-label">
                    <span>Saturation</span>
                    <span class="control-value" id="redSatValue">0</span>
                </div>
                <input type="range" class="control-slider" id="redSat" min="-100" max="100" value="0">
                <div class="control-label">
                    <span>Luminance</span>
                    <span class="control-value" id="redLumValue">0</span>
                </div>
                <input type="range" class="control-slider" id="redLum" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">Green</div>
                <div class="hsl-color-bar" style="background: linear-gradient(90deg, #00ff00, #80ff80);"></div>
                <div class="control-label">
                    <span>Hue</span>
                    <span class="control-value" id="greenHueValue">0</span>
                </div>
                <input type="range" class="control-slider" id="greenHue" min="-180" max="180" value="0">
                <div class="control-label">
                    <span>Saturation</span>
                    <span class="control-value" id="greenSatValue">0</span>
                </div>
                <input type="range" class="control-slider" id="greenSat" min="-100" max="100" value="0">
                <div class="control-label">
                    <span>Luminance</span>
                    <span class="control-value" id="greenLumValue">0</span>
                </div>
                <input type="range" class="control-slider" id="greenLum" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">Blue</div>
                <div class="hsl-color-bar" style="background: linear-gradient(90deg, #0000ff, #8080ff);"></div>
                <div class="control-label">
                    <span>Hue</span>
                    <span class="control-value" id="blueHueValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blueHue" min="-180" max="180" value="0">
                <div class="control-label">
                    <span>Saturation</span>
                    <span class="control-value" id="blueSatValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blueSat" min="-100" max="100" value="0">
                <div class="control-label">
                    <span>Luminance</span>
                    <span class="control-value" id="blueLumValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blueLum" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- LUT Panel -->
        <div class="lut-panel">
            <div class="panel-title">📊 LUT & Presets</div>
            
            <canvas class="lut-preview active" id="lutNone" width="250" height="60" onclick="applyLUT('none')"></canvas>
            <div class="canvas-label">None</div>
            
            <canvas class="lut-preview" id="lutCinematic" width="250" height="60" onclick="applyLUT('cinematic')"></canvas>
            <div class="canvas-label">Cinematic</div>
            
            <canvas class="lut-preview" id="lutVintage" width="250" height="60" onclick="applyLUT('vintage')"></canvas>
            <div class="canvas-label">Vintage</div>
            
            <canvas class="lut-preview" id="lutDramatic" width="250" height="60" onclick="applyLUT('dramatic')"></canvas>
            <div class="canvas-label">Dramatic</div>
        </div>

        <!-- Creative Panel -->
        <div class="creative-panel">
            <div class="panel-title">✨ Creative Looks</div>
            
            <div class="before-after">
                <div>
                    <canvas class="comparison-canvas" id="beforeCanvas" width="120" height="100"></canvas>
                    <div class="canvas-label">Before</div>
                </div>
                <div>
                    <canvas class="comparison-canvas" id="afterCanvas" width="120" height="100"></canvas>
                    <div class="canvas-label">After</div>
                </div>
            </div>
            
            <button class="preset-button" onclick="applyCreativeLook('natural')">Natural</button>
            <button class="preset-button" onclick="applyCreativeLook('warm')">Warm</button>
            <button class="preset-button" onclick="applyCreativeLook('cool')">Cool</button>
            <button class="preset-button" onclick="applyCreativeLook('moody')">Moody</button>
            <button class="preset-button" onclick="applyCreativeLook('bright')">Bright</button>
            <button class="preset-button" onclick="applyCreativeLook('faded')">Faded</button>
            
            <div style="margin-top: 15px;">
                <button class="preset-button" onclick="resetAll()" style="width: 100%;">🔄 Reset vše</button>
                <button class="preset-button" onclick="downloadImage()" style="width: 100%; margin-top: 8px;">📥 Stáhnout</button>
            </div>
        </div>
    </div>

    <script>
        class ColorGrading {
            constructor() {
                this.canvas = document.getElementById('mainCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.beforeCanvas = document.getElementById('beforeCanvas');
                this.beforeCtx = this.beforeCanvas.getContext('2d');
                this.afterCanvas = document.getElementById('afterCanvas');
                this.afterCtx = this.afterCanvas.getContext('2d');

                this.originalImageData = null;
                this.currentImageData = null;
                this.currentLUT = 'none';

                this.colorWheels = {
                    shadows: { x: 0, y: 0 },
                    midtones: { x: 0, y: 0 },
                    highlights: { x: 0, y: 0 }
                };

                this.adjustments = {
                    shadowsLift: 0,
                    midtonesGamma: 0,
                    highlightsGain: 0,
                    redHue: 0, redSat: 0, redLum: 0,
                    greenHue: 0, greenSat: 0, greenLum: 0,
                    blueHue: 0, blueSat: 0, blueLum: 0
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
                this.drawColorWheels();
                this.generateLUTPreviews();
            }

            initializeCanvas() {
                // Set initial canvas size
                this.canvas.width = 800;
                this.canvas.height = 600;
                this.canvas.style.maxWidth = '100%';
                this.canvas.style.maxHeight = '100%';
                this.canvas.style.objectFit = 'contain';
            }

            initializeEventListeners() {
                // File upload
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.1)';
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.style.background = '';
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = '';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                // Adjustment sliders
                Object.keys(this.adjustments).forEach(adjustment => {
                    const slider = document.getElementById(adjustment);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateAdjustment(adjustment, parseFloat(e.target.value));
                        });
                    }
                });

                // Color wheel interactions
                ['shadows', 'midtones', 'highlights'].forEach(wheel => {
                    const canvas = document.getElementById(wheel + 'Wheel');
                    if (canvas) {
                        canvas.addEventListener('click', (e) => {
                            this.handleColorWheelClick(e, wheel);
                        });
                    }
                });
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Get canvas container size
                        const canvasContainer = this.canvas.parentElement;
                        const containerWidth = canvasContainer.clientWidth;
                        const containerHeight = canvasContainer.clientHeight;

                        // Calculate optimal size maintaining aspect ratio
                        let { width, height } = img;
                        const imgRatio = width / height;
                        const containerRatio = containerWidth / containerHeight;

                        if (imgRatio > containerRatio) {
                            // Image is wider than container
                            width = containerWidth * 0.95;
                            height = width / imgRatio;
                        } else {
                            // Image is taller than container
                            height = containerHeight * 0.95;
                            width = height * imgRatio;
                        }

                        // Set canvas size
                        this.canvas.width = width;
                        this.canvas.height = height;
                        this.canvas.style.width = width + 'px';
                        this.canvas.style.height = height + 'px';

                        // Clear and draw image
                        this.ctx.clearRect(0, 0, width, height);
                        this.ctx.drawImage(img, 0, 0, width, height);

                        this.originalImageData = this.ctx.getImageData(0, 0, width, height);
                        this.currentImageData = new ImageData(
                            new Uint8ClampedArray(this.originalImageData.data),
                            width,
                            height
                        );

                        document.getElementById('uploadZone').style.display = 'none';
                        this.updatePreviews();

                        console.log('Image loaded successfully:', file.name);
                    };
                    img.onerror = () => {
                        alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    alert('Chyba při čtení souboru.');
                };
                reader.readAsDataURL(file);
            }

            loadTestImage() {
                // Create test image with various colors
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Create colorful test pattern
                const gradient1 = this.ctx.createLinearGradient(0, 0, 800, 0);
                gradient1.addColorStop(0, '#ff6b6b');
                gradient1.addColorStop(0.2, '#ffa500');
                gradient1.addColorStop(0.4, '#ffff00');
                gradient1.addColorStop(0.6, '#32cd32');
                gradient1.addColorStop(0.8, '#1e90ff');
                gradient1.addColorStop(1, '#9370db');

                this.ctx.fillStyle = gradient1;
                this.ctx.fillRect(0, 0, 800, 200);

                // Skin tone area
                this.ctx.fillStyle = '#d4a574';
                this.ctx.fillRect(0, 200, 200, 200);

                // Sky area
                this.ctx.fillStyle = '#87ceeb';
                this.ctx.fillRect(200, 200, 200, 200);

                // Grass area
                this.ctx.fillStyle = '#228b22';
                this.ctx.fillRect(400, 200, 200, 200);

                // Neutral gray
                this.ctx.fillStyle = '#808080';
                this.ctx.fillRect(600, 200, 200, 200);

                // Dark area
                this.ctx.fillStyle = '#2f2f2f';
                this.ctx.fillRect(0, 400, 400, 200);

                // Bright area
                this.ctx.fillStyle = '#f0f0f0';
                this.ctx.fillRect(400, 400, 400, 200);

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );

                document.getElementById('uploadZone').style.display = 'none';
                this.updatePreviews();
            }

            updateAdjustment(type, value) {
                this.adjustments[type] = value;
                document.getElementById(type + 'Value').textContent = value;
                this.applyColorGrading();
                this.updatePreviews();
            }

            applyColorGrading() {
                // Reset to original
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                this.apply3WayColorCorrection();
                this.applyHSLAdjustments();
                this.applyLUTEffect();

                this.ctx.putImageData(this.currentImageData, 0, 0);
            }
        }

            apply3WayColorCorrection() {
                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    let r = data[i] / 255;
                    let g = data[i + 1] / 255;
                    let b = data[i + 2] / 255;

                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

                    // Apply shadows (lift)
                    if (luminance < 0.3) {
                        const factor = (0.3 - luminance) / 0.3;
                        const lift = this.adjustments.shadowsLift / 100;
                        r += lift * factor * 0.1;
                        g += lift * factor * 0.1;
                        b += lift * factor * 0.1;
                    }

                    // Apply midtones (gamma)
                    if (luminance >= 0.3 && luminance <= 0.7) {
                        const gamma = 1 + (this.adjustments.midtonesGamma / 100);
                        r = Math.pow(r, 1 / gamma);
                        g = Math.pow(g, 1 / gamma);
                        b = Math.pow(b, 1 / gamma);
                    }

                    // Apply highlights (gain)
                    if (luminance > 0.7) {
                        const factor = (luminance - 0.7) / 0.3;
                        const gain = 1 + (this.adjustments.highlightsGain / 100);
                        r *= gain * factor + (1 - factor);
                        g *= gain * factor + (1 - factor);
                        b *= gain * factor + (1 - factor);
                    }

                    data[i] = Math.max(0, Math.min(255, r * 255));
                    data[i + 1] = Math.max(0, Math.min(255, g * 255));
                    data[i + 2] = Math.max(0, Math.min(255, b * 255));
                }
            }

            applyHSLAdjustments() {
                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    let r = data[i] / 255;
                    let g = data[i + 1] / 255;
                    let b = data[i + 2] / 255;

                    const hsl = this.rgbToHsl(r, g, b);
                    const hue = hsl.h * 360;

                    // Determine which color range this pixel belongs to
                    let colorRange = '';
                    if ((hue >= 0 && hue < 60) || (hue >= 300 && hue <= 360)) {
                        colorRange = 'red';
                    } else if (hue >= 60 && hue < 180) {
                        colorRange = 'green';
                    } else if (hue >= 180 && hue < 300) {
                        colorRange = 'blue';
                    }

                    if (colorRange) {
                        // Apply HSL adjustments for this color range
                        hsl.h += this.adjustments[colorRange + 'Hue'] / 360;
                        hsl.s *= (1 + this.adjustments[colorRange + 'Sat'] / 100);
                        hsl.l *= (1 + this.adjustments[colorRange + 'Lum'] / 100);

                        // Clamp values
                        hsl.h = (hsl.h + 1) % 1;
                        hsl.s = Math.max(0, Math.min(1, hsl.s));
                        hsl.l = Math.max(0, Math.min(1, hsl.l));

                        const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
                        data[i] = rgb.r * 255;
                        data[i + 1] = rgb.g * 255;
                        data[i + 2] = rgb.b * 255;
                    }
                }
            }

            applyLUTEffect() {
                if (this.currentLUT === 'none') return;

                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    let r = data[i] / 255;
                    let g = data[i + 1] / 255;
                    let b = data[i + 2] / 255;

                    switch (this.currentLUT) {
                        case 'cinematic':
                            r = Math.pow(r, 0.9) * 1.1;
                            g = Math.pow(g, 0.95) * 1.05;
                            b = Math.pow(b, 1.1) * 0.9;
                            break;
                        case 'vintage':
                            r = r * 1.2 + 0.1;
                            g = g * 1.1 + 0.05;
                            b = b * 0.8;
                            break;
                        case 'dramatic':
                            r = Math.pow(r, 1.3);
                            g = Math.pow(g, 1.2);
                            b = Math.pow(b, 1.1);
                            break;
                    }

                    data[i] = Math.max(0, Math.min(255, r * 255));
                    data[i + 1] = Math.max(0, Math.min(255, g * 255));
                    data[i + 2] = Math.max(0, Math.min(255, b * 255));
                }
            }

            drawColorWheels() {
                ['shadows', 'midtones', 'highlights'].forEach(wheel => {
                    this.drawColorWheel(wheel + 'Wheel');
                });
            }

            drawColorWheel(canvasId) {
                const canvas = document.getElementById(canvasId);
                const ctx = canvas.getContext('2d');
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 5;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw color wheel
                for (let angle = 0; angle < 360; angle += 1) {
                    const startAngle = (angle - 1) * Math.PI / 180;
                    const endAngle = angle * Math.PI / 180;

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.lineWidth = radius / 3;
                    ctx.strokeStyle = `hsl(${angle}, 100%, 50%)`;
                    ctx.stroke();
                }

                // Draw center circle
                ctx.fillStyle = '#333';
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius / 3, 0, 2 * Math.PI);
                ctx.fill();
            }

            generateLUTPreviews() {
                const luts = ['none', 'cinematic', 'vintage', 'dramatic'];

                luts.forEach(lut => {
                    const canvas = document.getElementById('lut' + lut.charAt(0).toUpperCase() + lut.slice(1));
                    const ctx = canvas.getContext('2d');

                    // Create gradient preview
                    const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);

                    switch (lut) {
                        case 'none':
                            gradient.addColorStop(0, '#000');
                            gradient.addColorStop(0.5, '#808080');
                            gradient.addColorStop(1, '#fff');
                            break;
                        case 'cinematic':
                            gradient.addColorStop(0, '#1a1a2e');
                            gradient.addColorStop(0.5, '#16213e');
                            gradient.addColorStop(1, '#0f3460');
                            break;
                        case 'vintage':
                            gradient.addColorStop(0, '#8b4513');
                            gradient.addColorStop(0.5, '#daa520');
                            gradient.addColorStop(1, '#f4a460');
                            break;
                        case 'dramatic':
                            gradient.addColorStop(0, '#000');
                            gradient.addColorStop(0.5, '#4a4a4a');
                            gradient.addColorStop(1, '#ffffff');
                            break;
                    }

                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                });
            }

            updatePreviews() {
                // Update before preview
                this.beforeCtx.clearRect(0, 0, 120, 100);
                this.beforeCtx.drawImage(this.canvas, 0, 0, 120, 100);

                // Update after preview (will be updated after processing)
                this.afterCtx.clearRect(0, 0, 120, 100);
                this.afterCtx.drawImage(this.canvas, 0, 0, 120, 100);
            }

            rgbToHsl(r, g, b) {
                const max = Math.max(r, g, b);
                const min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;

                if (max === min) {
                    h = s = 0;
                } else {
                    const d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }

                return { h, s, l };
            }

            hslToRgb(h, s, l) {
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1/6) return p + (q - p) * 6 * t;
                    if (t < 1/2) return q;
                    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                    return p;
                };

                let r, g, b;
                if (s === 0) {
                    r = g = b = l;
                } else {
                    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    const p = 2 * l - q;
                    r = hue2rgb(p, q, h + 1/3);
                    g = hue2rgb(p, q, h);
                    b = hue2rgb(p, q, h - 1/3);
                }

                return { r, g, b };
            }
        }

        // Global functions
        function applyLUT(lutName) {
            if (!colorGrading) return;

            // Update LUT preview states
            document.querySelectorAll('.lut-preview').forEach(preview => {
                preview.classList.remove('active');
            });
            document.getElementById('lut' + lutName.charAt(0).toUpperCase() + lutName.slice(1)).classList.add('active');

            colorGrading.currentLUT = lutName;
            colorGrading.applyColorGrading();
            colorGrading.updatePreviews();
        }

        function applyCreativeLook(lookName) {
            if (!colorGrading) return;

            const looks = {
                natural: { shadowsLift: 5, midtonesGamma: 0, highlightsGain: 0 },
                warm: { redSat: 15, redLum: 10, blueSat: -10 },
                cool: { blueSat: 15, blueLum: 5, redSat: -10 },
                moody: { shadowsLift: -20, highlightsGain: -15, midtonesGamma: 10 },
                bright: { highlightsGain: 20, shadowsLift: 15, midtonesGamma: -5 },
                faded: { shadowsLift: 30, highlightsGain: -20, midtonesGamma: -10 }
            };

            const look = looks[lookName];
            if (look) {
                Object.keys(look).forEach(key => {
                    const slider = document.getElementById(key);
                    if (slider) {
                        slider.value = look[key];
                        colorGrading.updateAdjustment(key, look[key]);
                    }
                });
            }
        }

        function resetAll() {
            if (!colorGrading) return;

            Object.keys(colorGrading.adjustments).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.value = 0;
                    colorGrading.updateAdjustment(key, 0);
                }
            });

            applyLUT('none');
        }

        function downloadImage() {
            if (!colorGrading || !colorGrading.currentImageData) {
                alert('Žádný obrázek k stažení!');
                return;
            }

            const link = document.createElement('a');
            link.download = `color_graded_image_${Date.now()}.png`;
            link.href = colorGrading.canvas.toDataURL('image/png');
            link.click();
        }

        // Initialize
        let colorGrading;
        document.addEventListener('DOMContentLoaded', () => {
            colorGrading = new ColorGrading();
        });
