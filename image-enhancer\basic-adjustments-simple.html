<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON>pravy - Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .upload-area {
            border: 2px dashed #00d4ff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-area:hover {
            background: rgba(0, 212, 255, 0.2);
        }
        
        .canvas-container {
            text-align: center;
            margin: 20px 0;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        
        #mainCanvas {
            max-width: 100%;
            max-height: 500px;
            border: 1px solid #555;
            background: #000;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .control-group {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
        }
        
        .control-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .test-button {
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0099cc;
        }
        
        .debug {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Základní úpravy - Test verze</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📁 Klikněte pro nahrání obrázku</h3>
            <p>Nebo přetáhněte obrázek sem</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="canvas-container">
            <canvas id="mainCanvas" width="800" height="600"></canvas>
        </div>
        
        <div>
            <button class="test-button" onclick="loadTestImage()">🎨 Načíst testovací obrázek</button>
            <button class="test-button" onclick="clearCanvas()">🗑️ Vymazat</button>
            <button class="test-button" onclick="runTest()">🧪 Test funkcí</button>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <div class="control-label">
                    <span>Jas</span>
                    <span id="brightnessValue">0</span>
                </div>
                <input type="range" class="control-slider" id="brightness" min="-100" max="100" value="0" oninput="updateBrightness(this.value)">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Kontrast</span>
                    <span id="contrastValue">0</span>
                </div>
                <input type="range" class="control-slider" id="contrast" min="-100" max="100" value="0" oninput="updateContrast(this.value)">
            </div>
        </div>
        
        <div class="debug" id="debugLog">
            <strong>Debug log:</strong><br>
            Stránka načtena...<br>
        </div>
    </div>

    <script>
        let canvas, ctx, originalImageData, currentImageData;
        
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function initializeApp() {
            log('🚀 Inicializace aplikace...');
            
            canvas = document.getElementById('mainCanvas');
            if (!canvas) {
                log('❌ Canvas nenalezen!');
                return;
            }
            
            ctx = canvas.getContext('2d');
            if (!ctx) {
                log('❌ Canvas context nenalezen!');
                return;
            }
            
            log('✅ Canvas nalezen: ' + canvas.width + 'x' + canvas.height);
            
            // Setup file input
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFileSelect);
            
            // Setup drag & drop
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            
            log('✅ Event listenery nastaveny');
            
            // Load test image
            loadTestImage();
        }
        
        function handleFileSelect(event) {
            log('📁 Soubor vybrán');
            const file = event.target.files[0];
            if (file) {
                log('📁 Načítám: ' + file.name + ' (' + file.type + ')');
                loadImageFile(file);
            }
        }
        
        function handleDragOver(event) {
            event.preventDefault();
        }
        
        function handleDrop(event) {
            event.preventDefault();
            log('🎯 Soubor přetažen');
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                log('📁 Načítám přetažený soubor: ' + files[0].name);
                loadImageFile(files[0]);
            }
        }
        
        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                log('📖 Soubor přečten, vytváření obrázku...');
                const img = new Image();
                img.onload = function() {
                    log('🖼️ Obrázek načten: ' + img.width + 'x' + img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    log('❌ Chyba při načítání obrázku');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                log('❌ Chyba při čtení souboru');
            };
            reader.readAsDataURL(file);
        }
        
        function drawImageToCanvas(img) {
            // Calculate size to fit canvas
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;
            
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
            
            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;
            
            // Clear and draw
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, x, y, width, height);
            
            // Store original
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            log('✅ Obrázek vykreslen na canvas');
        }
        
        function loadTestImage() {
            log('🎨 Načítám testovací obrázek...');
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add shapes
            ctx.fillStyle = '#fff';
            ctx.fillRect(100, 100, 200, 150);
            
            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.arc(600, 200, 80, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add text
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('TEST OBRÁZEK', 250, 400);
            
            // Store as original
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            log('✅ Testovací obrázek načten');
        }
        
        function clearCanvas() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            log('🗑️ Canvas vymazán');
        }
        
        function updateBrightness(value) {
            document.getElementById('brightnessValue').textContent = value;
            applyAdjustments();
            log('💡 Jas změněn na: ' + value);
        }
        
        function updateContrast(value) {
            document.getElementById('contrastValue').textContent = value;
            applyAdjustments();
            log('🔆 Kontrast změněn na: ' + value);
        }
        
        function applyAdjustments() {
            if (!originalImageData) return;
            
            const brightness = parseInt(document.getElementById('brightness').value);
            const contrast = parseInt(document.getElementById('contrast').value);
            
            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;
            
            for (let i = 0; i < data.length; i += 4) {
                // Apply brightness
                let r = originalData[i] + brightness;
                let g = originalData[i + 1] + brightness;
                let b = originalData[i + 2] + brightness;
                
                // Apply contrast
                const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
                r = factor * (r - 128) + 128;
                g = factor * (g - 128) + 128;
                b = factor * (b - 128) + 128;
                
                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
                data[i + 3] = originalData[i + 3]; // Alpha
            }
            
            ctx.putImageData(imageData, 0, 0);
        }
        
        function runTest() {
            log('🧪 Spouštím test...');
            log('Canvas: ' + (canvas ? 'OK' : 'CHYBA'));
            log('Context: ' + (ctx ? 'OK' : 'CHYBA'));
            log('Original data: ' + (originalImageData ? 'OK' : 'CHYBA'));
            log('Canvas size: ' + canvas.width + 'x' + canvas.height);
            log('Canvas visible: ' + (canvas.offsetWidth > 0 ? 'ANO' : 'NE'));
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
