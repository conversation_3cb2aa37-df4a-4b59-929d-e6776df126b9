<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Image Enhancer - Kompletní řešení pro úpravu obrázků</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e1e, #2d2d2d);
            color: #e0e0e0;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
            padding: 20px 0;
            border-bottom: 2px solid #00d4ff;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            font-size: 36px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #00d4ff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo-subtitle {
            font-size: 12px;
            color: #888;
            margin-top: 2px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
        }
        
        .nav-item {
            color: #ccc;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            position: relative;
        }
        
        .nav-item:hover {
            color: #00d4ff;
            transform: translateY(-2px);
        }
        
        .nav-item::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            transition: width 0.3s;
        }
        
        .nav-item:hover::after {
            width: 100%;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .hero-section {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00d4ff, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradient 3s ease infinite;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .hero-subtitle {
            font-size: 20px;
            color: #ccc;
            margin-bottom: 30px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
            border: 1px solid #404040;
            border-radius: 12px;
            padding: 30px;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .feature-card:hover::before {
            left: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #00d4ff;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .feature-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffffff;
        }
        
        .feature-description {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-highlights {
            list-style: none;
            margin-bottom: 25px;
        }
        
        .feature-highlights li {
            color: #00d4ff;
            font-size: 14px;
            margin-bottom: 8px;
            position: relative;
            padding-left: 20px;
        }
        
        .feature-highlights li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #00d4ff;
            font-weight: bold;
        }
        
        .launch-button {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .launch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }
        
        .stats-section {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 60px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #ccc;
            font-size: 14px;
        }
        
        .footer {
            background: #1a1a1a;
            padding: 40px 0;
            border-top: 1px solid #404040;
            text-align: center;
        }
        
        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .footer-text {
            color: #888;
            margin-bottom: 20px;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
        }
        
        .footer-link {
            color: #ccc;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
        }
        
        .footer-link:hover {
            color: #00d4ff;
        }
        
        .copyright {
            color: #666;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .hero-title {
                font-size: 36px;
            }
            
            .hero-subtitle {
                font-size: 18px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🎨</div>
                <div>
                    <div class="logo-text">Image Enhancer Pro</div>
                    <div class="logo-subtitle">Professional Image Enhancement Suite</div>
                </div>
            </div>
            <nav class="nav-menu">
                <a href="#features" class="nav-item">Funkce</a>
                <a href="#tools" class="nav-item">Nástroje</a>
                <a href="#about" class="nav-item">O aplikaci</a>
                <a href="#contact" class="nav-item">Kontakt</a>
            </nav>
        </div>
    </header>

    <main class="main-container">
        <section class="hero-section">
            <h1 class="hero-title">Profesionální úprava obrázků</h1>
            <p class="hero-subtitle">
                Kompletní sada nástrojů pro pokročilou úpravu fotografií s AI technologiemi, 
                profesionálními filtry a pokročilými funkcemi pro dokonalé výsledky.
            </p>
        </section>

        <section id="features" class="features-grid">
            <div class="feature-card" onclick="window.open('basic-adjustments-fixed.html', '_blank')">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Základní úpravy</h3>
                <p class="feature-description">
                    Profesionální nástroje pro základní úpravy fotografií s real-time náhledem a pokročilými algoritmy.
                </p>
                <ul class="feature-highlights">
                    <li>Exposure, kontrast, jas, sytost</li>
                    <li>Křivky a úrovně s histogram analýzou</li>
                    <li>Barevná korekce a white balance</li>
                    <li>Gamma korekce a tone mapping</li>
                </ul>
                <a href="basic-adjustments-fixed.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('color-grading-fixed.html', '_blank')">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">Color Grading</h3>
                <p class="feature-description">
                    Pokročilé nástroje pro barevné gradování s profesionálními color wheels a LUT podporou.
                </p>
                <ul class="feature-highlights">
                    <li>3-way color correction (shadows/midtones/highlights)</li>
                    <li>HSL adjustments pro jednotlivé barvy</li>
                    <li>LUT import/export a custom LUT creation</li>
                    <li>Split toning a creative looks</li>
                </ul>
                <a href="color-grading-fixed.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('retouch-tools-fixed.html', '_blank')">
                <div class="feature-icon">🖌️</div>
                <h3 class="feature-title">Retuš nástroje</h3>
                <p class="feature-description">
                    Profesionální retuš nástroje pro odstranění nedokonalostí a vylepšení portrétů.
                </p>
                <ul class="feature-highlights">
                    <li>Clone stamp a healing brush</li>
                    <li>Spot removal s content-aware fill</li>
                    <li>Skin smoothing a blemish removal</li>
                    <li>Red eye removal a teeth whitening</li>
                </ul>
                <a href="retouch-tools-fixed.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('selection-tools-fixed.html', '_blank')">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">Pokročilé selekce</h3>
                <p class="feature-description">
                    Sofistikované nástroje pro přesné výběry a masky s edge detection a AI asistencí.
                </p>
                <ul class="feature-highlights">
                    <li>Magic wand s tolerance nastavením</li>
                    <li>Color range selection</li>
                    <li>Polygon a lasso nástroje</li>
                    <li>Edge detection a mask refinement</li>
                </ul>
                <a href="selection-tools-fixed.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('histogram-scopes-pro.html', '_blank')">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">Histogram & Scopes</h3>
                <p class="feature-description">
                    Profesionální analýza obrazu s histogram, waveform, vectorscope a RGB parade.
                </p>
                <ul class="feature-highlights">
                    <li>RGB histogram s logaritmickým škálováním</li>
                    <li>Waveform monitor pro exposure analýzu</li>
                    <li>Vectorscope pro color balance</li>
                    <li>RGB parade a real-time color info</li>
                </ul>
                <a href="histogram-scopes-pro.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('lens-correction.html', '_blank')">
                <div class="feature-icon">🔧</div>
                <h3 class="feature-title">Lens Correction</h3>
                <p class="feature-description">
                    Pokročilé korekce objektivu s automatickou detekcí a lens profily.
                </p>
                <ul class="feature-highlights">
                    <li>Barrel/pincushion distortion correction</li>
                    <li>Chromatic aberration removal</li>
                    <li>Perspective correction a rotation</li>
                    <li>Vignetting correction a lens presets</li>
                </ul>
                <a href="lens-correction.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('raw-processor.html', '_blank')">
                <div class="feature-icon">📸</div>
                <h3 class="feature-title">RAW Processing</h3>
                <p class="feature-description">
                    Kompletní RAW workflow simulace s pokročilými nástroji pro profesionální zpracování.
                </p>
                <ul class="feature-highlights">
                    <li>White balance s 8 presets</li>
                    <li>Exposure, highlights, shadows, whites, blacks</li>
                    <li>Tone curve s interaktivní křivkou</li>
                    <li>Split toning a creative looks</li>
                </ul>
                <a href="raw-processor.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>

            <div class="feature-card" onclick="window.open('ai-tools.html', '_blank')">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">AI Nástroje</h3>
                <p class="feature-description">
                    Nejmodernější AI technologie pro automatické vylepšení a pokročilé úpravy.
                </p>
                <ul class="feature-highlights">
                    <li>Background removal s alpha transparency</li>
                    <li>Super resolution a AI upscaling</li>
                    <li>Face enhancement a skin smoothing</li>
                    <li>Style transfer a AI colorization</li>
                </ul>
                <a href="ai-tools.html" class="launch-button" target="_blank">🚀 Spustit nástroj</a>
            </div>
        </section>

        <section class="stats-section">
            <h2 style="font-size: 32px; margin-bottom: 40px; color: #ffffff;">Proč zvolit Image Enhancer Pro?</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">Profesionálních nástrojů</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Pokročilých funkcí</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">AI modelů</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Zdarma k použití</div>
                </div>
            </div>
        </section>

        <section id="about" style="background: linear-gradient(135deg, #1a1a1a, #2d2d2d); border-radius: 12px; padding: 40px; margin-bottom: 60px;">
            <h2 style="font-size: 32px; margin-bottom: 30px; color: #ffffff; text-align: center;">O aplikaci</h2>
            <div style="max-width: 800px; margin: 0 auto; text-align: center; color: #ccc; line-height: 1.8; font-size: 16px;">
                <p style="margin-bottom: 20px;">
                    Image Enhancer Pro je kompletní sada profesionálních nástrojů pro úpravu fotografií,
                    vyvinutá s důrazem na kvalitu, výkon a uživatelskou přívětivost. Kombinuje tradiční
                    fotografické techniky s nejmodernějšími AI technologiemi.
                </p>
                <p style="margin-bottom: 20px;">
                    Každý nástroj byl navržen s ohledem na potřeby profesionálních fotografů, grafických
                    designérů a nadšenců do fotografie. Aplikace běží přímo ve vašem prohlížeči bez
                    nutnosti instalace nebo registrace.
                </p>
                <p>
                    Všechny nástroje jsou optimalizovány pro rychlost a přesnost, s real-time náhledem
                    a pokročilými algoritmy pro dosažení nejlepších možných výsledků.
                </p>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p class="footer-text">
                Professional Image Enhancement Suite - Vytvořeno s ❤️ pro fotografy a designéry
            </p>
            <div class="footer-links">
                <a href="#" class="footer-link">Dokumentace</a>
                <a href="#" class="footer-link">Tutoriály</a>
                <a href="#" class="footer-link">Podpora</a>
                <a href="#" class="footer-link">GitHub</a>
            </div>
            <p class="copyright">
                © 2024 Image Enhancer Pro. Všechna práva vyhrazena.
            </p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation for feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', function() {
                const button = this.querySelector('.launch-button');
                const originalText = button.textContent;
                button.textContent = '⏳ Načítání...';
                button.style.opacity = '0.7';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.opacity = '1';
                }, 1000);
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero-section');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards for animation
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
