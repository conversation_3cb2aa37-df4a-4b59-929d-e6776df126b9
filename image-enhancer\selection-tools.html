<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Selection & Masking Tools</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .selection-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #1a1a1a;
            overflow: hidden;
        }
        
        /* Canvas zabírá celou obrazovku */
        .canvas-area {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Rozbalovací panely */
        .collapsible-panel {
            position: fixed;
            background: rgba(45, 45, 45, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid #404040;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-height: 80vh;
            overflow-y: auto;
            cursor: move;
        }

        .collapsible-panel.collapsed {
            transform: translateX(-100%);
        }

        .collapsible-panel.panel-right.collapsed {
            transform: translateX(100%);
        }

        .collapsible-panel.panel-bottom.collapsed {
            transform: translateY(100%);
        }

        .collapsible-panel.panel-top.collapsed {
            transform: translateY(-100%);
        }

        /* Hover efekt pro rozbalování z levé strany */
        .panel-edge-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 10px;
            height: 100vh;
            z-index: 999;
            background: transparent;
            transition: all 0.3s;
        }

        .panel-edge-trigger:hover {
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.3), transparent);
            width: 20px;
        }

        .panel-edge-trigger:hover ~ .collapsible-panel.panel-left.collapsed,
        .collapsible-panel.panel-left.collapsed:hover {
            transform: translateX(0);
        }

        /* Vizuální indikátor pro skryté panely */
        .panel-indicator {
            position: fixed;
            width: 3px;
            height: 60px;
            background: rgba(0, 212, 255, 0.6);
            border-radius: 0 3px 3px 0;
            z-index: 998;
            transition: all 0.3s;
        }

        .panel-indicator.left {
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .panel-indicator:hover {
            width: 6px;
            background: rgba(0, 212, 255, 0.9);
        }

        /* Pozice panelů */
        .panel-left {
            left: 20px;
            top: 20px;
            width: 320px;
        }

        .panel-right {
            right: 20px;
            top: 20px;
            width: 320px;
        }

        .panel-bottom {
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 200px;
        }

        .panel-top {
            top: 20px;
            left: 20px;
            right: 20px;
            height: 200px;
        }

        /* Panel header s ovládáním */
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #404040;
            background: rgba(0, 212, 255, 0.1);
            cursor: move;
            user-select: none;
            position: relative;
        }

        .panel-header:hover {
            background: rgba(0, 212, 255, 0.2);
        }

        .panel-header::before {
            content: '⋮⋮';
            position: absolute;
            left: 5px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 12px;
            letter-spacing: -2px;
        }

        .panel-title {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
        }

        .panel-controls {
            display: flex;
            gap: 8px;
        }

        .panel-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #555;
            color: #ccc;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s;
        }

        .panel-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
        }

        .panel-content {
            padding: 15px;
            max-height: calc(80vh - 60px);
            overflow-y: auto;
        }

        /* Toggle buttons pro panely */
        .panel-toggle {
            position: fixed;
            background: rgba(0, 212, 255, 0.9);
            color: #000;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            z-index: 1001;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        .panel-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.5);
        }

        .toggle-tools {
            top: 20px;
            left: 20px;
        }

        .toggle-properties {
            top: 20px;
            right: 20px;
        }

        .toggle-layers {
            bottom: 20px;
            left: 20px;
        }

        .toggle-masks {
            bottom: 20px;
            right: 20px;
        }
        
        .tool-section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
            margin-bottom: 12px;
        }
        
        .tool-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 10px 6px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
        }
        
        .tool-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .tool-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }
        
        .tool-icon {
            font-size: 16px;
        }
        
        .main-canvas {
            max-width: 95%;
            max-height: 95%;
            cursor: crosshair;
            object-fit: contain;
            background: #0f0f0f;
            border: 1px solid #333;
        }
        
        .selection-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 100%;
            max-height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .marching-ants {
            stroke: #000;
            stroke-width: 1;
            stroke-dasharray: 4 4;
            fill: none;
            animation: march 0.5s linear infinite;
        }
        
        @keyframes march {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: 8; }
        }
        
        .selection-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 11px;
            color: #ccc;
        }
        
        .magic-wand-preview {
            position: absolute;
            border: 2px dashed #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            pointer-events: none;
            display: none;
        }
        
        .color-range-preview {
            position: absolute;
            top: 50px;
            left: 10px;
            background: rgba(0,0,0,0.9);
            padding: 10px;
            border-radius: 6px;
            display: none;
        }
        
        .color-sample {
            width: 30px;
            height: 30px;
            border: 2px solid #555;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .tolerance-slider {
            width: 150px;
            margin: 8px 0;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .setting-label {
            font-size: 11px;
            color: #ccc;
            width: 80px;
            flex-shrink: 0;
        }
        
        .setting-slider {
            flex: 1;
            margin: 0 8px;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .setting-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .setting-value {
            font-size: 11px;
            color: #00d4ff;
            width: 35px;
            text-align: right;
        }
        
        .mask-thumbnail {
            width: 60px;
            height: 60px;
            background: #1a1a1a;
            border: 1px solid #555;
            border-radius: 4px;
            margin: 4px;
            cursor: pointer;
            position: relative;
        }
        
        .mask-thumbnail.active {
            border-color: #00d4ff;
        }
        
        .mask-name {
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            background: rgba(0,0,0,0.8);
            color: #fff;
            font-size: 9px;
            text-align: center;
            padding: 1px;
        }

        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 3px dashed #00d4ff;
            border-radius: 12px;
            padding: 60px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .selection-stats {
            background: #252525;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 11px;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .edge-detection-preview {
            width: 100%;
            height: 120px;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="selection-container">
        <!-- Canvas zabírá celou obrazovku -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="selectionCanvas"></canvas>
            <svg class="selection-overlay" id="selectionOverlay">
                <!-- Selection paths will be drawn here -->
            </svg>
            <div class="magic-wand-preview" id="magicPreview"></div>

            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Hover trigger pro levý okraj -->
        <div class="panel-edge-trigger"></div>
        <div class="panel-indicator left" title="Najeďte myší pro rozbalení panelu"></div>

        <!-- Toggle buttons -->
        <button class="panel-toggle toggle-tools" onclick="togglePanel('tools')" title="Nástroje">🛠️</button>
        <button class="panel-toggle toggle-properties" onclick="togglePanel('properties')" title="Vlastnosti">⚙️</button>
        <button class="panel-toggle toggle-layers" onclick="togglePanel('layers')" title="Vrstvy">📄</button>
        <button class="panel-toggle toggle-masks" onclick="togglePanel('masks')" title="Masky">🎭</button>

        <!-- Tools Panel -->
        <div class="collapsible-panel panel-left collapsed" id="toolsPanel">
            <div class="panel-header">
                <div class="panel-title">🛠️ Nástroje</div>
                <div class="panel-controls">
                    <button class="panel-btn" onclick="movePanel('tools', 'left')" title="Vlevo">←</button>
                    <button class="panel-btn" onclick="movePanel('tools', 'right')" title="Vpravo">→</button>
                    <button class="panel-btn" onclick="movePanel('tools', 'top')" title="Nahoře">↑</button>
                    <button class="panel-btn" onclick="movePanel('tools', 'bottom')" title="Dole">↓</button>
                    <button class="panel-btn" onclick="togglePanel('tools')" title="Zavřít">✕</button>
                </div>
            </div>
            <div class="panel-content">
            <div class="tool-section">
                <div class="section-title">Výběrové nástroje</div>
                <div class="tool-grid">
                    <div class="tool-button active" onclick="selectTool('rectangle')" data-tool="rectangle">
                        <div class="tool-icon">⬜</div>
                        <div>Obdélník</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('ellipse')" data-tool="ellipse">
                        <div class="tool-icon">⭕</div>
                        <div>Elipsa</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('lasso')" data-tool="lasso">
                        <div class="tool-icon">🪢</div>
                        <div>Lasso</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('polygon')" data-tool="polygon">
                        <div class="tool-icon">🔷</div>
                        <div>Polygon</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('magic-wand')" data-tool="magic-wand">
                        <div class="tool-icon">🪄</div>
                        <div>Magic Wand</div>
                    </div>
                    <div class="tool-button" onclick="selectTool('color-range')" data-tool="color-range">
                        <div class="tool-icon">🎨</div>
                        <div>Color Range</div>
                    </div>
                </div>
            </div>

            <div class="tool-section">
                <div class="section-title">Akce</div>
                <button class="tool-button" onclick="selectAll()" style="width: 100%; margin-bottom: 8px;">📋 Vybrat vše</button>
                <button class="tool-button" onclick="deselectAll()" style="width: 100%; margin-bottom: 8px;">❌ Zrušit výběr</button>
                <button class="tool-button" onclick="invertSelection()" style="width: 100%; margin-bottom: 8px;">🔄 Invertovat</button>
                <button class="tool-button" onclick="detectEdges()" style="width: 100%; margin-bottom: 8px;">🔍 Detekovat hrany</button>
            </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="collapsible-panel panel-right collapsed" id="propertiesPanel">
            <div class="panel-header">
                <div class="panel-title">⚙️ Vlastnosti</div>
                <div class="panel-controls">
                    <button class="panel-btn" onclick="movePanel('properties', 'left')" title="Vlevo">←</button>
                    <button class="panel-btn" onclick="movePanel('properties', 'right')" title="Vpravo">→</button>
                    <button class="panel-btn" onclick="movePanel('properties', 'top')" title="Nahoře">↑</button>
                    <button class="panel-btn" onclick="movePanel('properties', 'bottom')" title="Dole">↓</button>
                    <button class="panel-btn" onclick="togglePanel('properties')" title="Zavřít">✕</button>
                </div>
            </div>
            <div class="panel-content">
                <div class="tool-section">
                    <div class="section-title">Nastavení nástroje</div>
                    <div class="setting-row">
                        <span class="setting-label">Feather</span>
                        <input type="range" class="setting-slider" id="featherRadius" min="0" max="50" value="0">
                        <span class="setting-value" id="featherRadiusValue">0</span>
                    </div>
                    <div class="setting-row">
                        <span class="setting-label">Tolerance</span>
                        <input type="range" class="setting-slider" id="tolerance" min="0" max="100" value="32">
                        <span class="setting-value" id="toleranceValue">32</span>
                    </div>
                    <div class="setting-row">
                        <label style="font-size: 11px; color: #ccc;">
                            <input type="checkbox" id="antiAlias" checked> Anti-alias
                        </label>
                    </div>
                    <div class="setting-row">
                        <label style="font-size: 11px; color: #ccc;">
                            <input type="checkbox" id="contiguous" checked> Souvislé
                        </label>
                    </div>
                </div>

                <div class="tool-section">
                    <div class="section-title">Statistiky výběru</div>
                    <div class="selection-stats">
                        <div class="stats-row">
                            <span>Pixely:</span>
                            <span id="selectedPixels">0</span>
                        </div>
                        <div class="stats-row">
                            <span>Procenta:</span>
                            <span id="selectedPercent">0%</span>
                        </div>
                        <div class="stats-row">
                            <span>Rozměry:</span>
                            <span id="selectionDimensions">0 × 0</span>
                        </div>
                        <div class="stats-row">
                            <span>Střed:</span>
                            <span id="selectionCenter">0, 0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Layers Panel -->
        <div class="collapsible-panel panel-bottom collapsed" id="layersPanel">
            <div class="panel-header">
                <div class="panel-title">📄 Vrstvy</div>
                <div class="panel-controls">
                    <button class="panel-btn" onclick="movePanel('layers', 'left')" title="Vlevo">←</button>
                    <button class="panel-btn" onclick="movePanel('layers', 'right')" title="Vpravo">→</button>
                    <button class="panel-btn" onclick="movePanel('layers', 'top')" title="Nahoře">↑</button>
                    <button class="panel-btn" onclick="movePanel('layers', 'bottom')" title="Dole">↓</button>
                    <button class="panel-btn" onclick="togglePanel('layers')" title="Zavřít">✕</button>
                </div>
            </div>
            <div class="panel-content">
                <div class="section-title">Vrstvy</div>
                <div id="layersList" style="max-height: 120px; overflow-y: auto;">
                    <!-- Layers will be populated here -->
                </div>
                <div style="display: flex; gap: 4px; margin-top: 8px;">
                    <button class="tool-button" onclick="addLayer()" style="flex: 1; font-size: 10px;">➕</button>
                    <button class="tool-button" onclick="deleteLayer()" style="flex: 1; font-size: 10px;">🗑️</button>
                    <button class="tool-button" onclick="duplicateLayer()" style="flex: 1; font-size: 10px;">📋</button>
                </div>
            </div>
        </div>

        <!-- Masks Panel -->
        <div class="collapsible-panel panel-bottom collapsed" id="masksPanel">
            <div class="panel-header">
                <div class="panel-title">🎭 Masky</div>
                <div class="panel-controls">
                    <button class="panel-btn" onclick="movePanel('masks', 'left')" title="Vlevo">←</button>
                    <button class="panel-btn" onclick="movePanel('masks', 'right')" title="Vpravo">→</button>
                    <button class="panel-btn" onclick="movePanel('masks', 'top')" title="Nahoře">↑</button>
                    <button class="panel-btn" onclick="movePanel('masks', 'bottom')" title="Dole">↓</button>
                    <button class="panel-btn" onclick="togglePanel('masks')" title="Zavřít">✕</button>
                </div>
            </div>
            <div class="panel-content">
                <div class="section-title">Masky</div>
                <div id="masksList" style="display: flex; flex-wrap: wrap; max-height: 120px; overflow-y: auto;">
                    <!-- Masks will be populated here -->
                </div>
                <div style="display: flex; gap: 4px; margin-top: 8px;">
                    <button class="tool-button" onclick="createMask()" style="flex: 1; font-size: 9px;">➕ Maska</button>
                    <button class="tool-button" onclick="deleteMask()" style="flex: 1; font-size: 9px;">🗑️ Smazat</button>
                </div>
                <div style="margin-top: 8px;">
                    <button class="tool-button" onclick="applyMask()" style="width: 100%; font-size: 10px;">✅ Aplikovat masku</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SelectionTools {
            constructor() {
                this.canvas = document.getElementById('selectionCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.overlay = document.getElementById('selectionOverlay');
                this.currentTool = 'rectangle';
                this.isSelecting = false;
                this.startX = 0;
                this.startY = 0;
                this.currentSelection = null;
                this.selectionMask = null;
                this.layers = [];
                this.masks = [];
                this.currentLayer = 0;
                this.currentMask = -1;

                this.settings = {
                    featherRadius: 0,
                    tolerance: 32,
                    antiAlias: true,
                    contiguous: true,
                    edgeThreshold: 30
                };

                this.polygonPoints = [];
                this.lassoPath = [];

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Set up overlay SVG
                this.overlay.setAttribute('width', '800');
                this.overlay.setAttribute('height', '600');
                this.overlay.setAttribute('viewBox', '0 0 800 600');
            }

            initializeEventListeners() {
                // File upload
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.15)';
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.05)';
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.05)';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                // Canvas events
                this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                this.canvas.addEventListener('click', (e) => this.handleClick(e));

                // Settings sliders
                ['featherRadius', 'tolerance', 'edgeThreshold'].forEach(id => {
                    const slider = document.getElementById(id);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateSetting(id, e.target.value);
                        });
                    }
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'Escape':
                            this.deselectAll();
                            break;
                        case 'a':
                            if (e.ctrlKey) {
                                e.preventDefault();
                                this.selectAll();
                            }
                            break;
                        case 'd':
                            if (e.ctrlKey) {
                                e.preventDefault();
                                this.deselectAll();
                            }
                            break;
                        case 'i':
                            if (e.ctrlKey && e.shiftKey) {
                                e.preventDefault();
                                this.invertSelection();
                            }
                            break;
                    }
                });
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Get canvas container size
                        const canvasContainer = this.canvas.parentElement;
                        const containerWidth = canvasContainer.clientWidth;
                        const containerHeight = canvasContainer.clientHeight;

                        // Calculate optimal size maintaining aspect ratio
                        let { width, height } = img;
                        const imgRatio = width / height;
                        const containerRatio = containerWidth / containerHeight;

                        if (imgRatio > containerRatio) {
                            width = containerWidth * 0.9;
                            height = width / imgRatio;
                        } else {
                            height = containerHeight * 0.9;
                            width = height * imgRatio;
                        }

                        // Set canvas size
                        this.canvas.width = width;
                        this.canvas.height = height;
                        this.canvas.style.width = width + 'px';
                        this.canvas.style.height = height + 'px';

                        // Update overlay SVG
                        this.overlay.setAttribute('width', width);
                        this.overlay.setAttribute('height', height);
                        this.overlay.setAttribute('viewBox', `0 0 ${width} ${height}`);

                        // Clear and draw image
                        this.ctx.clearRect(0, 0, width, height);
                        this.ctx.drawImage(img, 0, 0, width, height);

                        this.originalImageData = this.ctx.getImageData(0, 0, width, height);

                        document.getElementById('uploadZone').style.display = 'none';
                        console.log('Image loaded successfully:', file.name);
                    };
                    img.onerror = () => {
                        alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    alert('Chyba při čtení souboru.');
                };
                reader.readAsDataURL(file);
            }

            loadTestImage() {
                // Create a test image with various elements
                const canvasContainer = this.canvas.parentElement;
                const containerWidth = canvasContainer.clientWidth;
                const containerHeight = canvasContainer.clientHeight;

                // Use most of the available space
                this.canvas.width = containerWidth * 0.9;
                this.canvas.height = containerHeight * 0.9;
                this.canvas.style.width = (containerWidth * 0.9) + 'px';
                this.canvas.style.height = (containerHeight * 0.9) + 'px';

                const width = this.canvas.width;
                const height = this.canvas.height;

                // Update overlay SVG
                this.overlay.setAttribute('width', width);
                this.overlay.setAttribute('height', height);
                this.overlay.setAttribute('viewBox', `0 0 ${width} ${height}`);

                // Background gradient
                const gradient = this.ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, '#4a90e2');
                gradient.addColorStop(0.5, '#7b68ee');
                gradient.addColorStop(1, '#ff6b6b');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, width, height);

                // Add various shapes for testing selections (scaled to canvas size)
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(width * 0.125, height * 0.167, width * 0.1875, height * 0.167);

                this.ctx.fillStyle = '#ff4757';
                this.ctx.beginPath();
                this.ctx.arc(width * 0.5, height * 0.333, width * 0.1, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#2ed573';
                this.ctx.beginPath();
                this.ctx.moveTo(width * 0.75, height * 0.25);
                this.ctx.lineTo(width * 0.875, height * 0.25);
                this.ctx.lineTo(width * 0.8125, height * 0.417);
                this.ctx.closePath();
                this.ctx.fill();

                this.ctx.fillStyle = '#ffa502';
                this.ctx.fillRect(width * 0.25, height * 0.583, width * 0.25, height * 0.133);

                // Add some text
                this.ctx.fillStyle = '#2f3542';
                this.ctx.font = `bold ${Math.max(16, width * 0.03)}px Arial`;
                this.ctx.fillText('Selection Test Image', width * 0.3125, height * 0.833);

                // Initialize layers
                this.initializeLayers();

                document.getElementById('uploadZone').style.display = 'none';
            }

            handleMouseDown(e) {
                const rect = this.canvas.getBoundingClientRect();
                this.startX = e.clientX - rect.left;
                this.startY = e.clientY - rect.top;

                switch(this.currentTool) {
                    case 'rectangle':
                    case 'ellipse':
                        this.isSelecting = true;
                        this.clearSelectionOverlay();
                        break;
                    case 'lasso':
                        this.isSelecting = true;
                        this.lassoPath = [{ x: this.startX, y: this.startY }];
                        this.clearSelectionOverlay();
                        break;
                    case 'polygon':
                        this.addPolygonPoint(this.startX, this.startY);
                        break;
                    case 'magic':
                        this.magicWandSelect(this.startX, this.startY);
                        break;
                    case 'color':
                        this.colorRangeSelect(this.startX, this.startY);
                        break;
                }
            }

            handleMouseMove(e) {
                if (!this.isSelecting) return;

                const rect = this.canvas.getBoundingClientRect();
                const currentX = e.clientX - rect.left;
                const currentY = e.clientY - rect.top;

                switch(this.currentTool) {
                    case 'rectangle':
                        this.drawRectangleSelection(this.startX, this.startY, currentX, currentY);
                        break;
                    case 'ellipse':
                        this.drawEllipseSelection(this.startX, this.startY, currentX, currentY);
                        break;
                    case 'lasso':
                        this.lassoPath.push({ x: currentX, y: currentY });
                        this.drawLassoSelection();
                        break;
                }
            }

            handleMouseUp(e) {
                if (!this.isSelecting) return;

                const rect = this.canvas.getBoundingClientRect();
                const endX = e.clientX - rect.left;
                const endY = e.clientY - rect.top;

                switch(this.currentTool) {
                    case 'rectangle':
                        this.finalizeRectangleSelection(this.startX, this.startY, endX, endY);
                        break;
                    case 'ellipse':
                        this.finalizeEllipseSelection(this.startX, this.startY, endX, endY);
                        break;
                    case 'lasso':
                        this.finalizeLassoSelection();
                        break;
                }

                this.isSelecting = false;
            }

            handleClick(e) {
                if (this.currentTool === 'polygon') {
                    const rect = this.canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    if (e.detail === 2) { // Double click
                        this.finalizePolygonSelection();
                    }
                }
            }

            drawRectangleSelection(x1, y1, x2, y2) {
                this.clearSelectionOverlay();

                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const d = `M ${x1} ${y1} L ${x2} ${y1} L ${x2} ${y2} L ${x1} ${y2} Z`;
                path.setAttribute('d', d);
                path.setAttribute('class', 'marching-ants');
                this.overlay.appendChild(path);
            }

            drawEllipseSelection(x1, y1, x2, y2) {
                this.clearSelectionOverlay();

                const centerX = (x1 + x2) / 2;
                const centerY = (y1 + y2) / 2;
                const radiusX = Math.abs(x2 - x1) / 2;
                const radiusY = Math.abs(y2 - y1) / 2;

                const ellipse = document.createElementNS('http://www.w3.org/2000/svg', 'ellipse');
                ellipse.setAttribute('cx', centerX);
                ellipse.setAttribute('cy', centerY);
                ellipse.setAttribute('rx', radiusX);
                ellipse.setAttribute('ry', radiusY);
                ellipse.setAttribute('class', 'marching-ants');
                this.overlay.appendChild(ellipse);
            }

            drawLassoSelection() {
                this.clearSelectionOverlay();

                if (this.lassoPath.length < 2) return;

                let d = `M ${this.lassoPath[0].x} ${this.lassoPath[0].y}`;
                for (let i = 1; i < this.lassoPath.length; i++) {
                    d += ` L ${this.lassoPath[i].x} ${this.lassoPath[i].y}`;
                }

                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', d);
                path.setAttribute('class', 'marching-ants');
                path.setAttribute('fill', 'none');
                this.overlay.appendChild(path);
            }

            addPolygonPoint(x, y) {
                this.polygonPoints.push({ x, y });
                this.drawPolygonSelection();
            }

            drawPolygonSelection() {
                this.clearSelectionOverlay();

                if (this.polygonPoints.length < 2) return;

                let d = `M ${this.polygonPoints[0].x} ${this.polygonPoints[0].y}`;
                for (let i = 1; i < this.polygonPoints.length; i++) {
                    d += ` L ${this.polygonPoints[i].x} ${this.polygonPoints[i].y}`;
                }

                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', d);
                path.setAttribute('class', 'marching-ants');
                path.setAttribute('fill', 'none');
                this.overlay.appendChild(path);
            }

            magicWandSelect(x, y) {
                // Get the color at the clicked point
                const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const data = imageData.data;
                const width = this.canvas.width;
                const height = this.canvas.height;

                const index = (Math.floor(y) * width + Math.floor(x)) * 4;
                const targetR = data[index];
                const targetG = data[index + 1];
                const targetB = data[index + 2];

                // Flood fill algorithm
                const tolerance = this.settings.tolerance;
                const visited = new Array(width * height).fill(false);
                const stack = [{ x: Math.floor(x), y: Math.floor(y) }];
                const selectedPixels = [];

                while (stack.length > 0) {
                    const { x: px, y: py } = stack.pop();

                    if (px < 0 || px >= width || py < 0 || py >= height) continue;

                    const pixelIndex = py * width + px;
                    if (visited[pixelIndex]) continue;

                    const dataIndex = pixelIndex * 4;
                    const r = data[dataIndex];
                    const g = data[dataIndex + 1];
                    const b = data[dataIndex + 2];

                    // Check color similarity
                    const colorDiff = Math.sqrt(
                        Math.pow(r - targetR, 2) +
                        Math.pow(g - targetG, 2) +
                        Math.pow(b - targetB, 2)
                    );

                    if (colorDiff <= tolerance * 4.41) { // 4.41 ≈ sqrt(255²*3)/100
                        visited[pixelIndex] = true;
                        selectedPixels.push({ x: px, y: py });

                        if (this.settings.contiguous) {
                            stack.push({ x: px + 1, y: py });
                            stack.push({ x: px - 1, y: py });
                            stack.push({ x: px, y: py + 1 });
                            stack.push({ x: px, y: py - 1 });
                        }
                    }
                }

                this.createSelectionFromPixels(selectedPixels);
                this.updateSelectionStats(selectedPixels.length);
            }

            colorRangeSelect(x, y) {
                // Show color range preview
                const preview = document.getElementById('colorRangePreview');
                const sample = document.getElementById('colorSample');

                const imageData = this.ctx.getImageData(x, y, 1, 1);
                const data = imageData.data;
                const r = data[0];
                const g = data[1];
                const b = data[2];

                sample.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
                preview.style.display = 'block';

                // Perform color range selection
                this.performColorRangeSelection(r, g, b);
            }

            performColorRangeSelection(targetR, targetG, targetB) {
                const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const data = imageData.data;
                const tolerance = document.getElementById('toleranceSlider').value;
                const selectedPixels = [];

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    const colorDiff = Math.sqrt(
                        Math.pow(r - targetR, 2) +
                        Math.pow(g - targetG, 2) +
                        Math.pow(b - targetB, 2)
                    );

                    if (colorDiff <= tolerance * 4.41) {
                        const pixelIndex = i / 4;
                        const x = pixelIndex % this.canvas.width;
                        const y = Math.floor(pixelIndex / this.canvas.width);
                        selectedPixels.push({ x, y });
                    }
                }

                this.createSelectionFromPixels(selectedPixels);
                this.updateSelectionStats(selectedPixels.length);
            }
        }

            createSelectionFromPixels(pixels) {
                // Create SVG path from selected pixels
                if (pixels.length === 0) return;

                // Group adjacent pixels into regions
                const regions = this.groupPixelsIntoRegions(pixels);

                this.clearSelectionOverlay();

                regions.forEach(region => {
                    const path = this.createPathFromRegion(region);
                    if (path) {
                        const svgPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                        svgPath.setAttribute('d', path);
                        svgPath.setAttribute('class', 'marching-ants');
                        this.overlay.appendChild(svgPath);
                    }
                });

                this.currentSelection = pixels;
            }

            groupPixelsIntoRegions(pixels) {
                // Simple region grouping - can be optimized
                const regions = [];
                const processed = new Set();

                pixels.forEach(pixel => {
                    const key = `${pixel.x},${pixel.y}`;
                    if (!processed.has(key)) {
                        const region = this.floodFillRegion(pixels, pixel, processed);
                        if (region.length > 0) {
                            regions.push(region);
                        }
                    }
                });

                return regions;
            }

            floodFillRegion(allPixels, startPixel, processed) {
                const region = [];
                const stack = [startPixel];
                const pixelSet = new Set(allPixels.map(p => `${p.x},${p.y}`));

                while (stack.length > 0) {
                    const pixel = stack.pop();
                    const key = `${pixel.x},${pixel.y}`;

                    if (processed.has(key)) continue;

                    processed.add(key);
                    region.push(pixel);

                    // Check adjacent pixels
                    const adjacent = [
                        { x: pixel.x + 1, y: pixel.y },
                        { x: pixel.x - 1, y: pixel.y },
                        { x: pixel.x, y: pixel.y + 1 },
                        { x: pixel.x, y: pixel.y - 1 }
                    ];

                    adjacent.forEach(adj => {
                        const adjKey = `${adj.x},${adj.y}`;
                        if (pixelSet.has(adjKey) && !processed.has(adjKey)) {
                            stack.push(adj);
                        }
                    });
                }

                return region;
            }

            createPathFromRegion(region) {
                if (region.length === 0) return null;

                // Find boundary pixels
                const boundary = this.findBoundaryPixels(region);
                if (boundary.length < 3) return null;

                // Create path
                let path = `M ${boundary[0].x} ${boundary[0].y}`;
                for (let i = 1; i < boundary.length; i++) {
                    path += ` L ${boundary[i].x} ${boundary[i].y}`;
                }
                path += ' Z';

                return path;
            }

            findBoundaryPixels(region) {
                const pixelSet = new Set(region.map(p => `${p.x},${p.y}`));
                const boundary = [];

                region.forEach(pixel => {
                    const adjacent = [
                        { x: pixel.x + 1, y: pixel.y },
                        { x: pixel.x - 1, y: pixel.y },
                        { x: pixel.x, y: pixel.y + 1 },
                        { x: pixel.x, y: pixel.y - 1 }
                    ];

                    const isBoundary = adjacent.some(adj =>
                        !pixelSet.has(`${adj.x},${adj.y}`)
                    );

                    if (isBoundary) {
                        boundary.push(pixel);
                    }
                });

                return boundary;
            }

            finalizeRectangleSelection(x1, y1, x2, y2) {
                const minX = Math.min(x1, x2);
                const minY = Math.min(y1, y2);
                const maxX = Math.max(x1, x2);
                const maxY = Math.max(y1, y2);

                const pixels = [];
                for (let y = minY; y <= maxY; y++) {
                    for (let x = minX; x <= maxX; x++) {
                        pixels.push({ x, y });
                    }
                }

                this.currentSelection = pixels;
                this.updateSelectionStats(pixels.length);
            }

            finalizeEllipseSelection(x1, y1, x2, y2) {
                const centerX = (x1 + x2) / 2;
                const centerY = (y1 + y2) / 2;
                const radiusX = Math.abs(x2 - x1) / 2;
                const radiusY = Math.abs(y2 - y1) / 2;

                const pixels = [];
                const minX = Math.floor(centerX - radiusX);
                const maxX = Math.ceil(centerX + radiusX);
                const minY = Math.floor(centerY - radiusY);
                const maxY = Math.ceil(centerY + radiusY);

                for (let y = minY; y <= maxY; y++) {
                    for (let x = minX; x <= maxX; x++) {
                        const dx = (x - centerX) / radiusX;
                        const dy = (y - centerY) / radiusY;
                        if (dx * dx + dy * dy <= 1) {
                            pixels.push({ x, y });
                        }
                    }
                }

                this.currentSelection = pixels;
                this.updateSelectionStats(pixels.length);
            }

            finalizeLassoSelection() {
                if (this.lassoPath.length < 3) return;

                // Close the path
                this.lassoPath.push(this.lassoPath[0]);

                // Find pixels inside the lasso path
                const pixels = [];
                const bounds = this.getLassoPathBounds();

                for (let y = bounds.minY; y <= bounds.maxY; y++) {
                    for (let x = bounds.minX; x <= bounds.maxX; x++) {
                        if (this.isPointInLassoPath(x, y)) {
                            pixels.push({ x, y });
                        }
                    }
                }

                this.currentSelection = pixels;
                this.updateSelectionStats(pixels.length);
                this.lassoPath = [];
            }

            finalizePolygonSelection() {
                if (this.polygonPoints.length < 3) return;

                // Find pixels inside the polygon
                const pixels = [];
                const bounds = this.getPolygonBounds();

                for (let y = bounds.minY; y <= bounds.maxY; y++) {
                    for (let x = bounds.minX; x <= bounds.maxX; x++) {
                        if (this.isPointInPolygon(x, y, this.polygonPoints)) {
                            pixels.push({ x, y });
                        }
                    }
                }

                this.currentSelection = pixels;
                this.updateSelectionStats(pixels.length);
                this.polygonPoints = [];
                this.clearSelectionOverlay();
                this.createSelectionFromPixels(pixels);
            }

            getLassoPathBounds() {
                let minX = Infinity, minY = Infinity;
                let maxX = -Infinity, maxY = -Infinity;

                this.lassoPath.forEach(point => {
                    minX = Math.min(minX, point.x);
                    minY = Math.min(minY, point.y);
                    maxX = Math.max(maxX, point.x);
                    maxY = Math.max(maxY, point.y);
                });

                return { minX: Math.floor(minX), minY: Math.floor(minY),
                        maxX: Math.ceil(maxX), maxY: Math.ceil(maxY) };
            }

            getPolygonBounds() {
                let minX = Infinity, minY = Infinity;
                let maxX = -Infinity, maxY = -Infinity;

                this.polygonPoints.forEach(point => {
                    minX = Math.min(minX, point.x);
                    minY = Math.min(minY, point.y);
                    maxX = Math.max(maxX, point.x);
                    maxY = Math.max(maxY, point.y);
                });

                return { minX: Math.floor(minX), minY: Math.floor(minY),
                        maxX: Math.ceil(maxX), maxY: Math.ceil(maxY) };
            }

            isPointInLassoPath(x, y) {
                // Ray casting algorithm
                let inside = false;
                const path = this.lassoPath;

                for (let i = 0, j = path.length - 1; i < path.length; j = i++) {
                    if (((path[i].y > y) !== (path[j].y > y)) &&
                        (x < (path[j].x - path[i].x) * (y - path[i].y) / (path[j].y - path[i].y) + path[i].x)) {
                        inside = !inside;
                    }
                }

                return inside;
            }

            isPointInPolygon(x, y, polygon) {
                let inside = false;

                for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
                    if (((polygon[i].y > y) !== (polygon[j].y > y)) &&
                        (x < (polygon[j].x - polygon[i].x) * (y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
                        inside = !inside;
                    }
                }

                return inside;
            }

            updateSelectionStats(pixelCount) {
                const totalPixels = this.canvas.width * this.canvas.height;
                const percentage = ((pixelCount / totalPixels) * 100).toFixed(1);

                document.getElementById('selectedPixels').textContent = pixelCount.toLocaleString();
                document.getElementById('selectedPercent').textContent = percentage + '%';

                if (this.currentSelection && this.currentSelection.length > 0) {
                    const bounds = this.getSelectionBounds();
                    document.getElementById('selectionDimensions').textContent =
                        `${bounds.width} × ${bounds.height}`;
                    document.getElementById('selectionCenter').textContent =
                        `${bounds.centerX}, ${bounds.centerY}`;
                }

                document.getElementById('selectionInfo').textContent =
                    pixelCount > 0 ? `Vybráno: ${pixelCount.toLocaleString()} pixelů` : 'Žádný výběr';
            }

            getSelectionBounds() {
                if (!this.currentSelection || this.currentSelection.length === 0) {
                    return { width: 0, height: 0, centerX: 0, centerY: 0 };
                }

                let minX = Infinity, minY = Infinity;
                let maxX = -Infinity, maxY = -Infinity;

                this.currentSelection.forEach(pixel => {
                    minX = Math.min(minX, pixel.x);
                    minY = Math.min(minY, pixel.y);
                    maxX = Math.max(maxX, pixel.x);
                    maxY = Math.max(maxY, pixel.y);
                });

                return {
                    width: maxX - minX + 1,
                    height: maxY - minY + 1,
                    centerX: Math.round((minX + maxX) / 2),
                    centerY: Math.round((minY + maxY) / 2)
                };
            }

            clearSelectionOverlay() {
                while (this.overlay.firstChild) {
                    this.overlay.removeChild(this.overlay.firstChild);
                }
            }

            updateSetting(settingId, value) {
                this.settings[settingId] = parseFloat(value);
                document.getElementById(settingId + 'Value').textContent = value;

                if (settingId === 'edgeThreshold') {
                    this.updateEdgeDetectionPreview();
                }
            }

            updateEdgeDetectionPreview() {
                const preview = document.getElementById('edgePreview');
                const ctx = preview.getContext('2d');

                // Simple edge detection preview
                ctx.fillStyle = '#1a1a1a';
                ctx.fillRect(0, 0, preview.width, preview.height);

                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 1;

                // Draw some sample edges based on threshold
                const threshold = this.settings.edgeThreshold;
                const intensity = threshold / 100;

                for (let i = 0; i < 20; i++) {
                    if (Math.random() < intensity) {
                        ctx.beginPath();
                        ctx.moveTo(Math.random() * preview.width, Math.random() * preview.height);
                        ctx.lineTo(Math.random() * preview.width, Math.random() * preview.height);
                        ctx.stroke();
                    }
                }
            }

            initializeLayers() {
                this.layers = [{
                    id: 'background',
                    name: 'Background',
                    visible: true,
                    opacity: 100
                }];
                this.updateLayersList();
            }

            updateLayersList() {
                const layersList = document.getElementById('layersList');
                layersList.innerHTML = '';

                this.layers.forEach((layer, index) => {
                    const layerDiv = document.createElement('div');
                    layerDiv.style.cssText = `
                        background: ${index === this.currentLayer ? '#404040' : '#2a2a2a'};
                        border: 1px solid #555;
                        margin: 2px 0;
                        padding: 6px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 11px;
                    `;
                    layerDiv.textContent = layer.name;
                    layerDiv.addEventListener('click', () => {
                        this.currentLayer = index;
                        this.updateLayersList();
                    });
                    layersList.appendChild(layerDiv);
                });
            }

            // Selection methods
            selectAll() {
                const pixels = [];
                for (let y = 0; y < this.canvas.height; y++) {
                    for (let x = 0; x < this.canvas.width; x++) {
                        pixels.push({ x, y });
                    }
                }
                this.currentSelection = pixels;
                this.updateSelectionStats(pixels.length);
                this.createSelectionFromPixels(pixels);
            }

            deselectAll() {
                this.currentSelection = null;
                this.clearSelectionOverlay();
                this.updateSelectionStats(0);
            }

            invertSelection() {
                const allPixels = [];
                for (let y = 0; y < this.canvas.height; y++) {
                    for (let x = 0; x < this.canvas.width; x++) {
                        allPixels.push({ x, y });
                    }
                }

                if (!this.currentSelection) {
                    this.currentSelection = allPixels;
                } else {
                    const selectedSet = new Set(
                        this.currentSelection.map(p => `${p.x},${p.y}`)
                    );
                    this.currentSelection = allPixels.filter(
                        p => !selectedSet.has(`${p.x},${p.y}`)
                    );
                }

                this.createSelectionFromPixels(this.currentSelection);
                this.updateSelectionStats(this.currentSelection.length);
            }

            detectEdges() {
                alert('Edge detection bude implementována s Sobel/Canny algoritmy.');
            }

            addLayer() {
                const name = prompt('Název nové vrstvy:', `Vrstva ${this.layers.length + 1}`);
                if (name) {
                    this.layers.push({
                        id: Date.now().toString(),
                        name: name,
                        visible: true,
                        opacity: 100
                    });
                    this.updateLayersList();
                }
            }

            deleteLayer() {
                if (this.layers.length <= 1) {
                    alert('Nelze smazat poslední vrstvu!');
                    return;
                }

                if (confirm('Smazat vybranou vrstvu?')) {
                    this.layers.splice(this.currentLayer, 1);
                    this.currentLayer = Math.max(0, this.currentLayer - 1);
                    this.updateLayersList();
                }
            }

            duplicateLayer() {
                const currentLayer = this.layers[this.currentLayer];
                const duplicatedLayer = {
                    ...currentLayer,
                    id: Date.now().toString(),
                    name: currentLayer.name + ' kopie'
                };

                this.layers.splice(this.currentLayer + 1, 0, duplicatedLayer);
                this.currentLayer++;
                this.updateLayersList();
            }

            createMask() {
                if (!this.currentSelection) {
                    alert('Nejprve vytvořte výběr!');
                    return;
                }

                const mask = {
                    id: Date.now().toString(),
                    name: `Maska ${this.masks.length + 1}`,
                    selection: [...this.currentSelection]
                };

                this.masks.push(mask);
                alert('Maska vytvořena!');
            }

            deleteMask() {
                if (this.masks.length === 0) {
                    alert('Žádné masky k smazání!');
                    return;
                }

                this.masks.pop();
                alert('Maska smazána!');
            }

            applyMask() {
                if (this.masks.length === 0) {
                    alert('Žádné masky k aplikaci!');
                    return;
                }

                alert('Aplikace masky bude implementována!');
            }

            saveSelection() {
                if (!this.currentSelection) {
                    alert('Žádný výběr k uložení!');
                    return;
                }

                const selectionData = JSON.stringify(this.currentSelection);
                localStorage.setItem('savedSelection', selectionData);
                alert('Výběr uložen!');
            }

            loadSelection() {
                const selectionData = localStorage.getItem('savedSelection');
                if (!selectionData) {
                    alert('Žádný uložený výběr!');
                    return;
                }

                try {
                    this.currentSelection = JSON.parse(selectionData);
                    this.createSelectionFromPixels(this.currentSelection);
                    this.updateSelectionStats(this.currentSelection.length);
                    alert('Výběr načten!');
                } catch (error) {
                    alert('Chyba při načítání výběru!');
                }
            }

            modifySelection(operation) {
                if (!this.currentSelection) return;

                const amount = prompt(`Zadejte hodnotu pro ${operation}:`, '2');
                if (amount === null) return;

                const pixels = parseInt(amount);

                switch(operation) {
                    case 'expand':
                        alert('Rozšíření výběru bude implementováno!');
                        break;
                    case 'contract':
                        alert('Zmenšení výběru bude implementováno!');
                        break;
                    case 'feather':
                        alert('Rozostření okrajů bude implementováno!');
                        break;
                    case 'smooth':
                        alert('Vyhlazení výběru bude implementováno!');
                        break;
                }
            }

            selectionOperation(operation) {
                switch(operation) {
                    case 'add':
                        alert('Přidání k výběru bude implementováno!');
                        break;
                    case 'subtract':
                        alert('Odebrání z výběru bude implementováno!');
                        break;
                    case 'intersect':
                        alert('Průnik výběru bude implementován!');
                        break;
                    case 'invert':
                        this.invertSelection();
                        break;
                }
            }
        }

        // Global functions
        function selectTool(toolName) {
            document.querySelectorAll('.tool-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

            if (selectionTools) {
                selectionTools.currentTool = toolName;

                // Hide color range preview when switching tools
                if (toolName !== 'color') {
                    document.getElementById('colorRangePreview').style.display = 'none';
                }
            }
        }

        function selectionOperation(operation) {
            if (!selectionTools) return;

            switch(operation) {
                case 'add':
                    // Implementation for adding to selection
                    break;
                case 'subtract':
                    // Implementation for subtracting from selection
                    break;
                case 'intersect':
                    // Implementation for intersecting selection
                    break;
                case 'invert':
                    selectionTools.invertSelection();
                    break;
            }
        }

        function modifySelection(operation) {
            if (!selectionTools || !selectionTools.currentSelection) return;

            const amount = prompt(`Zadejte hodnotu pro ${operation}:`, '2');
            if (amount === null) return;

            const pixels = parseInt(amount);

            switch(operation) {
                case 'expand':
                    // Implementation for expanding selection
                    break;
                case 'contract':
                    // Implementation for contracting selection
                    break;
                case 'feather':
                    // Implementation for feathering selection
                    break;
                case 'smooth':
                    // Implementation for smoothing selection
                    break;
            }
        }

        function detectEdges() {
            if (!selectionTools) return;

            // Simple edge detection implementation
            alert('Edge detection bude implementována s Sobel/Canny algoritmy.');
        }

        function selectAll() {
            if (!selectionTools) return;

            const pixels = [];
            for (let y = 0; y < selectionTools.canvas.height; y++) {
                for (let x = 0; x < selectionTools.canvas.width; x++) {
                    pixels.push({ x, y });
                }
            }

            selectionTools.currentSelection = pixels;
            selectionTools.updateSelectionStats(pixels.length);

            // Draw selection border
            selectionTools.clearSelectionOverlay();
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const d = `M 0 0 L ${selectionTools.canvas.width} 0 L ${selectionTools.canvas.width} ${selectionTools.canvas.height} L 0 ${selectionTools.canvas.height} Z`;
            path.setAttribute('d', d);
            path.setAttribute('class', 'marching-ants');
            selectionTools.overlay.appendChild(path);
        }

        function deselectAll() {
            if (!selectionTools) return;

            selectionTools.currentSelection = null;
            selectionTools.clearSelectionOverlay();
            selectionTools.updateSelectionStats(0);
        }

        function invertSelection() {
            if (!selectionTools) return;

            const allPixels = [];
            for (let y = 0; y < selectionTools.canvas.height; y++) {
                for (let x = 0; x < selectionTools.canvas.width; x++) {
                    allPixels.push({ x, y });
                }
            }

            if (!selectionTools.currentSelection) {
                selectionTools.currentSelection = allPixels;
            } else {
                const selectedSet = new Set(
                    selectionTools.currentSelection.map(p => `${p.x},${p.y}`)
                );

                selectionTools.currentSelection = allPixels.filter(
                    p => !selectedSet.has(`${p.x},${p.y}`)
                );
            }

            selectionTools.createSelectionFromPixels(selectionTools.currentSelection);
            selectionTools.updateSelectionStats(selectionTools.currentSelection.length);
        }

        function saveSelection() {
            if (!selectionTools || !selectionTools.currentSelection) {
                alert('Žádný výběr k uložení!');
                return;
            }

            const selectionData = JSON.stringify(selectionTools.currentSelection);
            localStorage.setItem('savedSelection', selectionData);
            alert('Výběr uložen!');
        }

        function loadSelection() {
            if (!selectionTools) return;

            const selectionData = localStorage.getItem('savedSelection');
            if (!selectionData) {
                alert('Žádný uložený výběr!');
                return;
            }

            try {
                selectionTools.currentSelection = JSON.parse(selectionData);
                selectionTools.createSelectionFromPixels(selectionTools.currentSelection);
                selectionTools.updateSelectionStats(selectionTools.currentSelection.length);
                alert('Výběr načten!');
            } catch (error) {
                alert('Chyba při načítání výběru!');
            }
        }

        function addLayer() {
            if (!selectionTools) return;

            const name = prompt('Název nové vrstvy:', `Vrstva ${selectionTools.layers.length + 1}`);
            if (name) {
                selectionTools.layers.push({
                    id: Date.now().toString(),
                    name: name,
                    visible: true,
                    opacity: 100
                });
                selectionTools.updateLayersList();
            }
        }

        function deleteLayer() {
            if (!selectionTools || selectionTools.layers.length <= 1) {
                alert('Nelze smazat poslední vrstvu!');
                return;
            }

            if (confirm('Smazat vybranou vrstvu?')) {
                selectionTools.layers.splice(selectionTools.currentLayer, 1);
                selectionTools.currentLayer = Math.max(0, selectionTools.currentLayer - 1);
                selectionTools.updateLayersList();
            }
        }

        function duplicateLayer() {
            if (!selectionTools) return;

            const currentLayer = selectionTools.layers[selectionTools.currentLayer];
            const duplicatedLayer = {
                ...currentLayer,
                id: Date.now().toString(),
                name: currentLayer.name + ' kopie'
            };

            selectionTools.layers.splice(selectionTools.currentLayer + 1, 0, duplicatedLayer);
            selectionTools.currentLayer++;
            selectionTools.updateLayersList();
        }

        function createMask() {
            if (!selectionTools || !selectionTools.currentSelection) {
                alert('Nejprve vytvořte výběr!');
                return;
            }

            const mask = {
                id: Date.now().toString(),
                name: `Maska ${selectionTools.masks.length + 1}`,
                selection: [...selectionTools.currentSelection]
            };

            selectionTools.masks.push(mask);
            alert('Maska vytvořena!');
        }

        function deleteMask() {
            if (!selectionTools || selectionTools.masks.length === 0) {
                alert('Žádné masky k smazání!');
                return;
            }

            // Simple implementation - remove last mask
            selectionTools.masks.pop();
            alert('Maska smazána!');
        }

        function applyMask() {
            if (!selectionTools || selectionTools.masks.length === 0) {
                alert('Žádné masky k aplikaci!');
                return;
            }

            alert('Aplikace masky bude implementována!');
        }

        // Initialize the selection tools
        let selectionTools;
        document.addEventListener('DOMContentLoaded', () => {
            selectionTools = new SelectionTools();
        });

        // Chybějící globální funkce
        function selectAll() {
            if (selectionTools) {
                selectionTools.selectAll();
            }
        }

        function deselectAll() {
            if (selectionTools) {
                selectionTools.deselectAll();
            }
        }

        function invertSelection() {
            if (selectionTools) {
                selectionTools.invertSelection();
            }
        }

        function detectEdges() {
            if (selectionTools) {
                selectionTools.detectEdges();
            }
        }

        function addLayer() {
            if (selectionTools) {
                selectionTools.addLayer();
            }
        }

        function deleteLayer() {
            if (selectionTools) {
                selectionTools.deleteLayer();
            }
        }

        function duplicateLayer() {
            if (selectionTools) {
                selectionTools.duplicateLayer();
            }
        }

        function createMask() {
            if (selectionTools) {
                selectionTools.createMask();
            }
        }

        function deleteMask() {
            if (selectionTools) {
                selectionTools.deleteMask();
            }
        }

        function applyMask() {
            if (selectionTools) {
                selectionTools.applyMask();
            }
        }

        function saveSelection() {
            if (selectionTools) {
                selectionTools.saveSelection();
            }
        }

        function loadSelection() {
            if (selectionTools) {
                selectionTools.loadSelection();
            }
        }

        function modifySelection(type) {
            if (selectionTools) {
                selectionTools.modifySelection(type);
            }
        }

        function selectionOperation(operation) {
            if (selectionTools) {
                selectionTools.selectionOperation(operation);
            }
        }

        // Panel management functions
        function movePanel(panelName, position) {
            const panel = document.getElementById(panelName + 'Panel');
            if (!panel) return;

            // Remove all position classes
            panel.classList.remove('panel-left', 'panel-right', 'panel-top', 'panel-bottom');

            // Add new position class
            panel.classList.add('panel-' + position);
        }

        // Drag & Drop functionality
        let isDragging = false;
        let currentPanel = null;
        let dragOffset = { x: 0, y: 0 };

        function initializeDragAndDrop() {
            const panels = document.querySelectorAll('.collapsible-panel');

            panels.forEach(panel => {
                const header = panel.querySelector('.panel-header');
                if (header) {
                    header.addEventListener('mousedown', (e) => {
                        isDragging = true;
                        currentPanel = panel;

                        const rect = panel.getBoundingClientRect();
                        dragOffset.x = e.clientX - rect.left;
                        dragOffset.y = e.clientY - rect.top;

                        panel.style.transition = 'none';
                        panel.style.zIndex = '1001';

                        e.preventDefault();
                    });
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging || !currentPanel) return;

                const x = e.clientX - dragOffset.x;
                const y = e.clientY - dragOffset.y;

                // Omezení na obrazovku
                const maxX = window.innerWidth - currentPanel.offsetWidth;
                const maxY = window.innerHeight - currentPanel.offsetHeight;

                const clampedX = Math.max(0, Math.min(x, maxX));
                const clampedY = Math.max(0, Math.min(y, maxY));

                currentPanel.style.left = clampedX + 'px';
                currentPanel.style.top = clampedY + 'px';
                currentPanel.style.right = 'auto';
                currentPanel.style.bottom = 'auto';

                // Odstranění pozičních tříd při ručním přetahování
                currentPanel.classList.remove('panel-left', 'panel-right', 'panel-top', 'panel-bottom');
            });

            document.addEventListener('mouseup', () => {
                if (isDragging && currentPanel) {
                    currentPanel.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                    currentPanel.style.zIndex = '1000';
                }
                isDragging = false;
                currentPanel = null;
            });
        }

        // Enhanced toggle function
        function togglePanel(panelName) {
            const panel = document.getElementById(panelName + 'Panel');
            if (panel) {
                panel.classList.toggle('collapsed');
            }
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', () => {
            initializeDragAndDrop();
            console.log('Selection Tools with draggable panels initialized');
        });
    </script>
</body>
</html>
