// Advanced Color Tools for Professional Image Editor

class ColorTools {
    constructor() {
        this.hslChannels = {
            red: { hue: 0, saturation: 0, lightness: 0 },
            orange: { hue: 0, saturation: 0, lightness: 0 },
            yellow: { hue: 0, saturation: 0, lightness: 0 },
            green: { hue: 0, saturation: 0, lightness: 0 },
            aqua: { hue: 0, saturation: 0, lightness: 0 },
            blue: { hue: 0, saturation: 0, lightness: 0 },
            purple: { hue: 0, saturation: 0, lightness: 0 },
            magenta: { hue: 0, saturation: 0, lightness: 0 }
        };
        
        this.curves = {
            rgb: this.createLinearCurve(),
            red: this.createLinearCurve(),
            green: this.createLinearCurve(),
            blue: this.createLinearCurve()
        };
        
        this.colorRanges = {
            red: { min: 345, max: 15 },
            orange: { min: 15, max: 45 },
            yellow: { min: 45, max: 75 },
            green: { min: 75, max: 165 },
            aqua: { min: 165, max: 195 },
            blue: { min: 195, max: 255 },
            purple: { min: 255, max: 285 },
            magenta: { min: 285, max: 345 }
        };
    }
    
    createLinearCurve() {
        const curve = [];
        for (let i = 0; i <= 255; i++) {
            curve[i] = i;
        }
        return curve;
    }
    
    rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;
        
        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        
        return [h * 360, s * 100, l * 100];
    }
    
    hslToRgb(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;
        
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };
        
        let r, g, b;
        
        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }
        
        return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
    }
    
    getColorRange(hue) {
        // Normalize hue to 0-360
        hue = ((hue % 360) + 360) % 360;
        
        for (const [colorName, range] of Object.entries(this.colorRanges)) {
            if (range.min > range.max) { // Wraps around (like red)
                if (hue >= range.min || hue <= range.max) {
                    return colorName;
                }
            } else {
                if (hue >= range.min && hue <= range.max) {
                    return colorName;
                }
            }
        }
        return 'red'; // fallback
    }
    
    applyHSLAdjustments(imageData) {
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            // Convert to HSL
            let [h, s, l] = this.rgbToHsl(r, g, b);
            
            // Determine which color range this pixel belongs to
            const colorRange = this.getColorRange(h);
            const adjustments = this.hslChannels[colorRange];
            
            // Apply adjustments
            h += adjustments.hue;
            s = Math.max(0, Math.min(100, s + adjustments.saturation));
            l = Math.max(0, Math.min(100, l + adjustments.lightness));
            
            // Convert back to RGB
            const [newR, newG, newB] = this.hslToRgb(h, s, l);
            
            data[i] = newR;
            data[i + 1] = newG;
            data[i + 2] = newB;
        }
    }
    
    applyCurves(imageData) {
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            // Apply RGB curve first
            let r = this.curves.rgb[data[i]];
            let g = this.curves.rgb[data[i + 1]];
            let b = this.curves.rgb[data[i + 2]];
            
            // Then apply individual channel curves
            r = this.curves.red[Math.round(r)];
            g = this.curves.green[Math.round(g)];
            b = this.curves.blue[Math.round(b)];
            
            data[i] = Math.max(0, Math.min(255, r));
            data[i + 1] = Math.max(0, Math.min(255, g));
            data[i + 2] = Math.max(0, Math.min(255, b));
        }
    }
    
    createCurveFromPoints(points) {
        // Create a smooth curve from control points
        const curve = new Array(256);
        
        // Sort points by x coordinate
        points.sort((a, b) => a.x - b.x);
        
        // Ensure we have points at 0 and 255
        if (points[0].x > 0) {
            points.unshift({ x: 0, y: 0 });
        }
        if (points[points.length - 1].x < 255) {
            points.push({ x: 255, y: 255 });
        }
        
        // Linear interpolation between points
        for (let i = 0; i < 256; i++) {
            let leftPoint = points[0];
            let rightPoint = points[points.length - 1];
            
            for (let j = 0; j < points.length - 1; j++) {
                if (i >= points[j].x && i <= points[j + 1].x) {
                    leftPoint = points[j];
                    rightPoint = points[j + 1];
                    break;
                }
            }
            
            if (leftPoint.x === rightPoint.x) {
                curve[i] = leftPoint.y;
            } else {
                const t = (i - leftPoint.x) / (rightPoint.x - leftPoint.x);
                curve[i] = leftPoint.y + t * (rightPoint.y - leftPoint.y);
            }
        }
        
        return curve;
    }
    
    // Color grading presets
    applyColorGrade(imageData, gradeName) {
        const grades = {
            cinematic: {
                shadows: { r: 0.9, g: 0.95, b: 1.1 },
                midtones: { r: 1.0, g: 1.0, b: 1.0 },
                highlights: { r: 1.1, g: 1.05, b: 0.9 }
            },
            warm: {
                shadows: { r: 1.0, g: 0.95, b: 0.85 },
                midtones: { r: 1.05, g: 1.0, b: 0.95 },
                highlights: { r: 1.1, g: 1.05, b: 0.9 }
            },
            cool: {
                shadows: { r: 0.85, g: 0.95, b: 1.1 },
                midtones: { r: 0.95, g: 1.0, b: 1.05 },
                highlights: { r: 0.9, g: 1.05, b: 1.1 }
            },
            vintage: {
                shadows: { r: 1.1, g: 1.0, b: 0.8 },
                midtones: { r: 1.05, g: 1.0, b: 0.9 },
                highlights: { r: 1.0, g: 1.05, b: 0.95 }
            }
        };
        
        const grade = grades[gradeName];
        if (!grade) return;
        
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            
            // Calculate luminance to determine shadows/midtones/highlights
            const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
            
            let colorGrade;
            if (luminance < 85) {
                colorGrade = grade.shadows;
            } else if (luminance < 170) {
                colorGrade = grade.midtones;
            } else {
                colorGrade = grade.highlights;
            }
            
            data[i] = Math.max(0, Math.min(255, r * colorGrade.r));
            data[i + 1] = Math.max(0, Math.min(255, g * colorGrade.g));
            data[i + 2] = Math.max(0, Math.min(255, b * colorGrade.b));
        }
    }
    
    // LUT (Look-Up Table) application
    applyLUT(imageData, lutData) {
        // Simple 3D LUT application
        const data = imageData.data;
        const lutSize = Math.round(Math.pow(lutData.length / 3, 1/3));
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i] / 255;
            const g = data[i + 1] / 255;
            const b = data[i + 2] / 255;
            
            // Find position in LUT
            const rIndex = Math.min(Math.floor(r * (lutSize - 1)), lutSize - 1);
            const gIndex = Math.min(Math.floor(g * (lutSize - 1)), lutSize - 1);
            const bIndex = Math.min(Math.floor(b * (lutSize - 1)), lutSize - 1);
            
            const lutIndex = (rIndex * lutSize * lutSize + gIndex * lutSize + bIndex) * 3;
            
            if (lutIndex < lutData.length - 2) {
                data[i] = Math.round(lutData[lutIndex] * 255);
                data[i + 1] = Math.round(lutData[lutIndex + 1] * 255);
                data[i + 2] = Math.round(lutData[lutIndex + 2] * 255);
            }
        }
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorTools;
} else {
    window.ColorTools = ColorTools;
}
