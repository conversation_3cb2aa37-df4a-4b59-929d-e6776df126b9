<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAW Processing Simulator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .raw-container {
            display: grid;
            grid-template-areas: 
                "basic canvas develop"
                "tone canvas creative"
                "detail canvas output";
            grid-template-columns: 280px 1fr 280px;
            grid-template-rows: 1fr 1fr 1fr;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .basic-panel {
            grid-area: basic;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        
        .develop-panel {
            grid-area: develop;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            overflow-y: auto;
        }
        
        .tone-panel {
            grid-area: tone;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .creative-panel {
            grid-area: creative;
            background: #252525;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .detail-panel {
            grid-area: detail;
            background: #222;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .output-panel {
            grid-area: output;
            background: #222;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .control-group {
            margin-bottom: 12px;
        }
        
        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-value {
            font-size: 11px;
            color: #00d4ff;
            font-weight: 500;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 6px;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .main-canvas {
            width: 100%;
            height: 100%;
            cursor: crosshair;
        }
        
        .raw-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
        }
        
        .histogram-mini {
            width: 100%;
            height: 60px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
        }
        
        .wb-presets {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 10px;
        }
        
        .wb-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            transition: all 0.2s;
            text-align: center;
        }
        
        .wb-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .wb-button.active {
            background: #00d4ff;
            border-color: #00d4ff;
            color: #000;
        }
        
        .tone-curve {
            width: 100%;
            height: 120px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
            position: relative;
            cursor: crosshair;
        }
        
        .curve-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .split-toning {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .color-wheel-mini {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 1px solid #404040;
            margin: 0 auto 8px;
            cursor: pointer;
        }
        
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 10px;
        }
        
        .preset-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            transition: all 0.2s;
            text-align: center;
        }
        
        .preset-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .export-options {
            background: #1a1a1a;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .format-selector {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }
        
        .format-button {
            flex: 1;
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            text-align: center;
        }
        
        .format-button.active {
            background: #00d4ff;
            color: #000;
        }
        
        .processing-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
        }
        
        .spinner {
            border: 3px solid #404040;
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .clipping-warning {
            position: absolute;
            top: 50px;
            left: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 11px;
            display: none;
        }
        
        .highlight-recovery {
            background: #2a2a2a;
            border-radius: 4px;
            padding: 8px;
            margin: 8px 0;
            border: 1px solid #404040;
        }
        
        .recovery-title {
            font-size: 10px;
            color: #888;
            margin-bottom: 6px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="raw-container">
        <!-- Basic Panel -->
        <div class="basic-panel">
            <div class="panel-title">⚡ Basic</div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Exposure</span>
                    <span class="control-value" id="exposureValue">0.0</span>
                </div>
                <input type="range" class="control-slider" id="exposure" min="-5" max="5" value="0" step="0.1">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Highlights</span>
                    <span class="control-value" id="highlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="highlights" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Shadows</span>
                    <span class="control-value" id="shadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="shadows" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Whites</span>
                    <span class="control-value" id="whitesValue">0</span>
                </div>
                <input type="range" class="control-slider" id="whites" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Blacks</span>
                    <span class="control-value" id="blacksValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blacks" min="-100" max="100" value="0">
            </div>
            
            <canvas class="histogram-mini" id="basicHistogram" width="250" height="60"></canvas>
            
            <div class="highlight-recovery">
                <div class="recovery-title">Highlight Recovery</div>
                <div style="font-size: 10px; color: #ccc; margin-bottom: 4px;">
                    Clipped: <span id="clippedHighlights">0%</span>
                </div>
                <div style="font-size: 10px; color: #ccc;">
                    Recoverable: <span id="recoverableHighlights">0%</span>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="rawCanvas"></canvas>

            <div class="raw-info" id="rawInfo">
                <div>Camera: Canon EOS R5</div>
                <div>ISO: 800</div>
                <div>Aperture: f/2.8</div>
                <div>Shutter: 1/125s</div>
                <div>WB: <span id="currentWB">Auto</span></div>
            </div>

            <div class="clipping-warning" id="clippingWarning">
                ⚠️ Highlight clipping detected
            </div>

            <div class="processing-indicator" id="processingIndicator">
                <div class="spinner"></div>
                <div>Processing RAW data...</div>
            </div>
        </div>

        <!-- Develop Panel -->
        <div class="develop-panel">
            <div class="panel-title">🌡️ White Balance</div>

            <div class="wb-presets">
                <button class="wb-button active" onclick="setWhiteBalance('auto')">Auto</button>
                <button class="wb-button" onclick="setWhiteBalance('daylight')">Daylight</button>
                <button class="wb-button" onclick="setWhiteBalance('cloudy')">Cloudy</button>
                <button class="wb-button" onclick="setWhiteBalance('shade')">Shade</button>
                <button class="wb-button" onclick="setWhiteBalance('tungsten')">Tungsten</button>
                <button class="wb-button" onclick="setWhiteBalance('fluorescent')">Fluorescent</button>
                <button class="wb-button" onclick="setWhiteBalance('flash')">Flash</button>
                <button class="wb-button" onclick="setWhiteBalance('custom')">Custom</button>
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Temperature</span>
                    <span class="control-value" id="temperatureValue">5500K</span>
                </div>
                <input type="range" class="control-slider" id="temperature" min="2000" max="10000" value="5500" step="50">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Tint</span>
                    <span class="control-value" id="tintValue">0</span>
                </div>
                <input type="range" class="control-slider" id="tint" min="-100" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 20px;">✨ Presence</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Clarity</span>
                    <span class="control-value" id="clarityValue">0</span>
                </div>
                <input type="range" class="control-slider" id="clarity" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Vibrance</span>
                    <span class="control-value" id="vibranceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="vibrance" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Saturation</span>
                    <span class="control-value" id="saturationValue">0</span>
                </div>
                <input type="range" class="control-slider" id="saturation" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- Tone Panel -->
        <div class="tone-panel">
            <div class="panel-title">📈 Tone Curve</div>

            <canvas class="tone-curve" id="toneCurve" width="250" height="120">
                <svg class="curve-grid" width="250" height="120">
                    <!-- Grid lines -->
                </svg>
            </canvas>

            <div class="control-group">
                <div class="control-label">
                    <span>Highlights</span>
                    <span class="control-value" id="curveHighlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveHighlights" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Lights</span>
                    <span class="control-value" id="curveLightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveLights" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Darks</span>
                    <span class="control-value" id="curveDarksValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveDarks" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Shadows</span>
                    <span class="control-value" id="curveShadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveShadows" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- Creative Panel -->
        <div class="creative-panel">
            <div class="panel-title">🎨 Split Toning</div>

            <div class="split-toning">
                <div>
                    <div style="font-size: 10px; color: #888; text-align: center; margin-bottom: 4px;">Highlights</div>
                    <canvas class="color-wheel-mini" id="highlightWheel" width="60" height="60"></canvas>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Hue</span>
                            <span class="control-value" id="highlightHueValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="highlightHue" min="0" max="360" value="0">
                    </div>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Sat</span>
                            <span class="control-value" id="highlightSatValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="highlightSat" min="0" max="100" value="0">
                    </div>
                </div>

                <div>
                    <div style="font-size: 10px; color: #888; text-align: center; margin-bottom: 4px;">Shadows</div>
                    <canvas class="color-wheel-mini" id="shadowWheel" width="60" height="60"></canvas>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Hue</span>
                            <span class="control-value" id="shadowHueValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="shadowHue" min="0" max="360" value="0">
                    </div>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Sat</span>
                            <span class="control-value" id="shadowSatValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="shadowSat" min="0" max="100" value="0">
                    </div>
                </div>
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Balance</span>
                    <span class="control-value" id="balanceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="balance" min="-100" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 15px;">🎭 Creative Looks</div>

            <div class="preset-grid">
                <button class="preset-button" onclick="applyCreativeLook('natural')">Natural</button>
                <button class="preset-button" onclick="applyCreativeLook('vivid')">Vivid</button>
                <button class="preset-button" onclick="applyCreativeLook('dramatic')">Dramatic</button>
                <button class="preset-button" onclick="applyCreativeLook('vintage')">Vintage</button>
                <button class="preset-button" onclick="applyCreativeLook('film')">Film</button>
                <button class="preset-button" onclick="applyCreativeLook('bw')">B&W</button>
            </div>
        </div>

        <!-- Detail Panel -->
        <div class="detail-panel">
            <div class="panel-title">🔍 Detail</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Sharpening</span>
                    <span class="control-value" id="sharpeningValue">25</span>
                </div>
                <input type="range" class="control-slider" id="sharpening" min="0" max="150" value="25">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Radius</span>
                    <span class="control-value" id="sharpenRadiusValue">1.0</span>
                </div>
                <input type="range" class="control-slider" id="sharpenRadius" min="0.5" max="3.0" value="1.0" step="0.1">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Detail</span>
                    <span class="control-value" id="sharpenDetailValue">25</span>
                </div>
                <input type="range" class="control-slider" id="sharpenDetail" min="0" max="100" value="25">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Masking</span>
                    <span class="control-value" id="sharpenMaskingValue">0</span>
                </div>
                <input type="range" class="control-slider" id="sharpenMasking" min="0" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 15px;">🔇 Noise Reduction</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Luminance</span>
                    <span class="control-value" id="noiseLumValue">0</span>
                </div>
                <input type="range" class="control-slider" id="noiseLum" min="0" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Color</span>
                    <span class="control-value" id="noiseColorValue">25</span>
                </div>
                <input type="range" class="control-slider" id="noiseColor" min="0" max="100" value="25">
            </div>
        </div>

        <!-- Output Panel -->
        <div class="output-panel">
            <div class="panel-title">📤 Export</div>

            <div class="export-options">
                <div style="font-size: 10px; color: #888; margin-bottom: 6px;">Format</div>
                <div class="format-selector">
                    <button class="format-button active" onclick="selectFormat('jpeg')">JPEG</button>
                    <button class="format-button" onclick="selectFormat('tiff')">TIFF</button>
                    <button class="format-button" onclick="selectFormat('png')">PNG</button>
                </div>

                <div class="control-group">
                    <div class="control-label">
                        <span>Quality</span>
                        <span class="control-value" id="qualityValue">90</span>
                    </div>
                    <input type="range" class="control-slider" id="quality" min="1" max="100" value="90">
                </div>

                <div class="control-group">
                    <div class="control-label">
                        <span>Color Space</span>
                    </div>
                    <select style="width: 100%; background: #404040; color: #e0e0e0; border: 1px solid #555; padding: 4px; border-radius: 3px; font-size: 10px;">
                        <option>sRGB</option>
                        <option>Adobe RGB</option>
                        <option>ProPhoto RGB</option>
                        <option>Display P3</option>
                    </select>
                </div>
            </div>

            <button class="preset-button" onclick="exportImage()" style="width: 100%; margin-bottom: 8px; background: linear-gradient(135deg, #28a745, #20c997);">
                📥 Export Image
            </button>

            <div class="panel-title" style="margin-top: 15px;">💾 Presets</div>

            <div class="preset-grid">
                <button class="preset-button" onclick="savePreset()">💾 Save</button>
                <button class="preset-button" onclick="loadPreset()">📂 Load</button>
                <button class="preset-button" onclick="resetAll()">🔄 Reset</button>
                <button class="preset-button" onclick="autoAdjust()">🤖 Auto</button>
            </div>

            <canvas class="histogram-mini" id="outputHistogram" width="250" height="60"></canvas>
        </div>
    </div>

    <script>
        class RAWProcessor {
            constructor() {
                this.canvas = document.getElementById('rawCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.basicHistogramCanvas = document.getElementById('basicHistogram');
                this.basicHistogramCtx = this.basicHistogramCanvas.getContext('2d');
                this.outputHistogramCanvas = document.getElementById('outputHistogram');
                this.outputHistogramCtx = this.outputHistogramCanvas.getContext('2d');
                this.toneCurveCanvas = document.getElementById('toneCurve');
                this.toneCurveCtx = this.toneCurveCanvas.getContext('2d');

                this.originalImageData = null;
                this.currentImageData = null;
                this.currentWB = 'auto';
                this.currentFormat = 'jpeg';

                this.settings = {
                    // Basic
                    exposure: 0,
                    highlights: 0,
                    shadows: 0,
                    whites: 0,
                    blacks: 0,

                    // White Balance
                    temperature: 5500,
                    tint: 0,

                    // Presence
                    clarity: 0,
                    vibrance: 0,
                    saturation: 0,

                    // Tone Curve
                    curveHighlights: 0,
                    curveLights: 0,
                    curveDarks: 0,
                    curveShadows: 0,

                    // Split Toning
                    highlightHue: 0,
                    highlightSat: 0,
                    shadowHue: 0,
                    shadowSat: 0,
                    balance: 0,

                    // Detail
                    sharpening: 25,
                    sharpenRadius: 1.0,
                    sharpenDetail: 25,
                    sharpenMasking: 0,
                    noiseLum: 0,
                    noiseColor: 25,

                    // Export
                    quality: 90
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
                this.drawToneCurve();
                this.drawColorWheels();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;
            }

            initializeEventListeners() {
                // All sliders
                Object.keys(this.settings).forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateSetting(setting, parseFloat(e.target.value));
                        });
                    }
                });

                // Tone curve interaction
                this.toneCurveCanvas.addEventListener('click', (e) => {
                    this.handleToneCurveClick(e);
                });

                // Color wheels
                ['highlightWheel', 'shadowWheel'].forEach(wheelId => {
                    const wheel = document.getElementById(wheelId);
                    if (wheel) {
                        wheel.addEventListener('click', (e) => {
                            this.handleColorWheelClick(e, wheelId);
                        });
                    }
                });
            }

            loadTestImage() {
                // Create a test RAW-like image
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Create a realistic photo-like test image
                const gradient = this.ctx.createRadialGradient(400, 300, 0, 400, 300, 400);
                gradient.addColorStop(0, '#f4f4f4');
                gradient.addColorStop(0.3, '#e8e8e8');
                gradient.addColorStop(0.7, '#d0d0d0');
                gradient.addColorStop(1, '#a0a0a0');

                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, 800, 600);

                // Add some color areas to simulate a photo
                // Sky area
                const skyGradient = this.ctx.createLinearGradient(0, 0, 0, 200);
                skyGradient.addColorStop(0, '#87ceeb');
                skyGradient.addColorStop(1, '#b0e0e6');
                this.ctx.fillStyle = skyGradient;
                this.ctx.fillRect(0, 0, 800, 200);

                // Landscape
                this.ctx.fillStyle = '#8fbc8f';
                this.ctx.fillRect(0, 200, 800, 150);

                // Buildings
                this.ctx.fillStyle = '#696969';
                this.ctx.fillRect(100, 150, 80, 100);
                this.ctx.fillRect(250, 120, 100, 130);
                this.ctx.fillRect(450, 140, 90, 110);
                this.ctx.fillRect(600, 130, 70, 120);

                // Some highlights and shadows
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(200, 50, 50, 30); // Bright cloud

                this.ctx.fillStyle = '#2f4f4f';
                this.ctx.fillRect(0, 350, 800, 250); // Dark foreground

                // Add some color cast to simulate RAW
                this.ctx.fillStyle = 'rgba(255, 200, 150, 0.1)';
                this.ctx.fillRect(0, 0, 800, 600);

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );

                this.updateHistograms();
            }

            updateSetting(setting, value) {
                this.settings[setting] = value;

                // Update display value
                const valueElement = document.getElementById(setting + 'Value');
                if (valueElement) {
                    if (setting === 'temperature') {
                        valueElement.textContent = value + 'K';
                    } else if (setting === 'sharpenRadius') {
                        valueElement.textContent = value.toFixed(1);
                    } else {
                        valueElement.textContent = value;
                    }
                }

                this.processRAW();
                this.updateHistograms();
                this.checkClipping();
            }

            processRAW() {
                // Show processing indicator
                this.showProcessing(true);

                // Reset to original
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                // Apply processing in RAW workflow order
                this.applyWhiteBalance();
                this.applyBasicAdjustments();
                this.applyToneCurve();
                this.applySplitToning();
                this.applyPresence();
                this.applySharpening();
                this.applyNoiseReduction();

                // Draw to canvas
                this.ctx.putImageData(this.currentImageData, 0, 0);

                // Hide processing indicator
                setTimeout(() => {
                    this.showProcessing(false);
                }, 300);
            }

            applyWhiteBalance() {
                const data = this.currentImageData.data;
                const tempFactor = (this.settings.temperature - 5500) / 5500;
                const tintFactor = this.settings.tint / 100;

                for (let i = 0; i < data.length; i += 4) {
                    // Temperature adjustment
                    if (tempFactor > 0) { // Warmer
                        data[i] = Math.min(255, data[i] * (1 + tempFactor * 0.3)); // More red
                        data[i + 2] = Math.max(0, data[i + 2] * (1 - tempFactor * 0.2)); // Less blue
                    } else { // Cooler
                        data[i] = Math.max(0, data[i] * (1 + tempFactor * 0.2)); // Less red
                        data[i + 2] = Math.min(255, data[i + 2] * (1 - tempFactor * 0.3)); // More blue
                    }

                    // Tint adjustment
                    if (tintFactor > 0) { // More magenta
                        data[i] = Math.min(255, data[i] * (1 + tintFactor * 0.1));
                        data[i + 1] = Math.max(0, data[i + 1] * (1 - tintFactor * 0.1));
                        data[i + 2] = Math.min(255, data[i + 2] * (1 + tintFactor * 0.1));
                    } else { // More green
                        data[i + 1] = Math.min(255, data[i + 1] * (1 - tintFactor * 0.2));
                    }
                }
            }
        }

        // Initialize the RAW processor
        let rawProcessor;
        document.addEventListener('DOMContentLoaded', () => {
            rawProcessor = new RAWProcessor();
        });
