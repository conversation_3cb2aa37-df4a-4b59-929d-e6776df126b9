<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAW Processing Simulator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .raw-container {
            display: grid;
            grid-template-areas: 
                "basic canvas develop"
                "tone canvas creative"
                "detail canvas output";
            grid-template-columns: 280px 1fr 280px;
            grid-template-rows: 1fr 1fr 1fr;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .basic-panel {
            grid-area: basic;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .develop-panel {
            grid-area: develop;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
            overflow-y: auto;
        }
        
        .tone-panel {
            grid-area: tone;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .creative-panel {
            grid-area: creative;
            background: #252525;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .detail-panel {
            grid-area: detail;
            background: #222;
            padding: 15px;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .output-panel {
            grid-area: output;
            background: #222;
            padding: 15px;
            border-left: 1px solid #404040;
            border-top: 1px solid #404040;
            overflow-y: auto;
        }
        
        .panel-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .control-group {
            margin-bottom: 12px;
        }
        
        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-value {
            font-size: 11px;
            color: #00d4ff;
            font-weight: 500;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 6px;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            cursor: crosshair;
            object-fit: contain;
            background: #0f0f0f;
        }
        
        .raw-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
        }
        
        .histogram-mini {
            width: 100%;
            height: 60px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
        }
        
        .wb-presets {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 10px;
        }
        
        .wb-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            transition: all 0.2s;
            text-align: center;
        }
        
        .wb-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .wb-button.active {
            background: #00d4ff;
            border-color: #00d4ff;
            color: #000;
        }
        
        .tone-curve {
            width: 100%;
            height: 120px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
            position: relative;
            cursor: crosshair;
        }
        
        .curve-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .split-toning {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .color-wheel-mini {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 1px solid #404040;
            margin: 0 auto 8px;
            cursor: pointer;
        }
        
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-bottom: 10px;
        }
        
        .preset-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            transition: all 0.2s;
            text-align: center;
        }
        
        .preset-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .export-options {
            background: #1a1a1a;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .format-selector {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }
        
        .format-button {
            flex: 1;
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            text-align: center;
        }
        
        .format-button.active {
            background: #00d4ff;
            color: #000;
        }
        
        .processing-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
        }
        
        .spinner {
            border: 3px solid #404040;
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .clipping-warning {
            position: absolute;
            top: 50px;
            left: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 11px;
            display: none;
        }
        
        .highlight-recovery {
            background: #2a2a2a;
            border-radius: 4px;
            padding: 8px;
            margin: 8px 0;
            border: 1px solid #404040;
        }
        
        .recovery-title {
            font-size: 10px;
            color: #888;
            margin-bottom: 6px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="raw-container">
        <!-- Basic Panel -->
        <div class="basic-panel">
            <div class="panel-title">⚡ Basic</div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Exposure</span>
                    <span class="control-value" id="exposureValue">0.0</span>
                </div>
                <input type="range" class="control-slider" id="exposure" min="-5" max="5" value="0" step="0.1">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Highlights</span>
                    <span class="control-value" id="highlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="highlights" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Shadows</span>
                    <span class="control-value" id="shadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="shadows" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Whites</span>
                    <span class="control-value" id="whitesValue">0</span>
                </div>
                <input type="range" class="control-slider" id="whites" min="-100" max="100" value="0">
            </div>
            
            <div class="control-group">
                <div class="control-label">
                    <span>Blacks</span>
                    <span class="control-value" id="blacksValue">0</span>
                </div>
                <input type="range" class="control-slider" id="blacks" min="-100" max="100" value="0">
            </div>
            
            <canvas class="histogram-mini" id="basicHistogram" width="250" height="60"></canvas>
            
            <div class="highlight-recovery">
                <div class="recovery-title">Highlight Recovery</div>
                <div style="font-size: 10px; color: #ccc; margin-bottom: 4px;">
                    Clipped: <span id="clippedHighlights">0%</span>
                </div>
                <div style="font-size: 10px; color: #ccc;">
                    Recoverable: <span id="recoverableHighlights">0%</span>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="rawCanvas"></canvas>

            <div class="raw-info" id="rawInfo">
                <div>Camera: Canon EOS R5</div>
                <div>ISO: 800</div>
                <div>Aperture: f/2.8</div>
                <div>Shutter: 1/125s</div>
                <div>WB: <span id="currentWB">Auto</span></div>
            </div>

            <div class="clipping-warning" id="clippingWarning">
                ⚠️ Highlight clipping detected
            </div>

            <div class="processing-indicator" id="processingIndicator">
                <div class="spinner"></div>
                <div>Processing RAW data...</div>
            </div>
        </div>

        <!-- Develop Panel -->
        <div class="develop-panel">
            <div class="panel-title">🌡️ White Balance</div>

            <div class="wb-presets">
                <button class="wb-button active" onclick="setWhiteBalance('auto')">Auto</button>
                <button class="wb-button" onclick="setWhiteBalance('daylight')">Daylight</button>
                <button class="wb-button" onclick="setWhiteBalance('cloudy')">Cloudy</button>
                <button class="wb-button" onclick="setWhiteBalance('shade')">Shade</button>
                <button class="wb-button" onclick="setWhiteBalance('tungsten')">Tungsten</button>
                <button class="wb-button" onclick="setWhiteBalance('fluorescent')">Fluorescent</button>
                <button class="wb-button" onclick="setWhiteBalance('flash')">Flash</button>
                <button class="wb-button" onclick="setWhiteBalance('custom')">Custom</button>
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Temperature</span>
                    <span class="control-value" id="temperatureValue">5500K</span>
                </div>
                <input type="range" class="control-slider" id="temperature" min="2000" max="10000" value="5500" step="50">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Tint</span>
                    <span class="control-value" id="tintValue">0</span>
                </div>
                <input type="range" class="control-slider" id="tint" min="-100" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 20px;">✨ Presence</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Clarity</span>
                    <span class="control-value" id="clarityValue">0</span>
                </div>
                <input type="range" class="control-slider" id="clarity" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Vibrance</span>
                    <span class="control-value" id="vibranceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="vibrance" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Saturation</span>
                    <span class="control-value" id="saturationValue">0</span>
                </div>
                <input type="range" class="control-slider" id="saturation" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- Tone Panel -->
        <div class="tone-panel">
            <div class="panel-title">📈 Tone Curve</div>

            <canvas class="tone-curve" id="toneCurve" width="250" height="120">
                <svg class="curve-grid" width="250" height="120">
                    <!-- Grid lines -->
                </svg>
            </canvas>

            <div class="control-group">
                <div class="control-label">
                    <span>Highlights</span>
                    <span class="control-value" id="curveHighlightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveHighlights" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Lights</span>
                    <span class="control-value" id="curveLightsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveLights" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Darks</span>
                    <span class="control-value" id="curveDarksValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveDarks" min="-100" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Shadows</span>
                    <span class="control-value" id="curveShadowsValue">0</span>
                </div>
                <input type="range" class="control-slider" id="curveShadows" min="-100" max="100" value="0">
            </div>
        </div>

        <!-- Creative Panel -->
        <div class="creative-panel">
            <div class="panel-title">🎨 Split Toning</div>

            <div class="split-toning">
                <div>
                    <div style="font-size: 10px; color: #888; text-align: center; margin-bottom: 4px;">Highlights</div>
                    <canvas class="color-wheel-mini" id="highlightWheel" width="60" height="60"></canvas>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Hue</span>
                            <span class="control-value" id="highlightHueValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="highlightHue" min="0" max="360" value="0">
                    </div>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Sat</span>
                            <span class="control-value" id="highlightSatValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="highlightSat" min="0" max="100" value="0">
                    </div>
                </div>

                <div>
                    <div style="font-size: 10px; color: #888; text-align: center; margin-bottom: 4px;">Shadows</div>
                    <canvas class="color-wheel-mini" id="shadowWheel" width="60" height="60"></canvas>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Hue</span>
                            <span class="control-value" id="shadowHueValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="shadowHue" min="0" max="360" value="0">
                    </div>
                    <div class="control-group">
                        <div class="control-label">
                            <span>Sat</span>
                            <span class="control-value" id="shadowSatValue">0</span>
                        </div>
                        <input type="range" class="control-slider" id="shadowSat" min="0" max="100" value="0">
                    </div>
                </div>
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Balance</span>
                    <span class="control-value" id="balanceValue">0</span>
                </div>
                <input type="range" class="control-slider" id="balance" min="-100" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 15px;">🎭 Creative Looks</div>

            <div class="preset-grid">
                <button class="preset-button" onclick="applyCreativeLook('natural')">Natural</button>
                <button class="preset-button" onclick="applyCreativeLook('vivid')">Vivid</button>
                <button class="preset-button" onclick="applyCreativeLook('dramatic')">Dramatic</button>
                <button class="preset-button" onclick="applyCreativeLook('vintage')">Vintage</button>
                <button class="preset-button" onclick="applyCreativeLook('film')">Film</button>
                <button class="preset-button" onclick="applyCreativeLook('bw')">B&W</button>
            </div>
        </div>

        <!-- Detail Panel -->
        <div class="detail-panel">
            <div class="panel-title">🔍 Detail</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Sharpening</span>
                    <span class="control-value" id="sharpeningValue">25</span>
                </div>
                <input type="range" class="control-slider" id="sharpening" min="0" max="150" value="25">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Radius</span>
                    <span class="control-value" id="sharpenRadiusValue">1.0</span>
                </div>
                <input type="range" class="control-slider" id="sharpenRadius" min="0.5" max="3.0" value="1.0" step="0.1">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Detail</span>
                    <span class="control-value" id="sharpenDetailValue">25</span>
                </div>
                <input type="range" class="control-slider" id="sharpenDetail" min="0" max="100" value="25">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Masking</span>
                    <span class="control-value" id="sharpenMaskingValue">0</span>
                </div>
                <input type="range" class="control-slider" id="sharpenMasking" min="0" max="100" value="0">
            </div>

            <div class="panel-title" style="margin-top: 15px;">🔇 Noise Reduction</div>

            <div class="control-group">
                <div class="control-label">
                    <span>Luminance</span>
                    <span class="control-value" id="noiseLumValue">0</span>
                </div>
                <input type="range" class="control-slider" id="noiseLum" min="0" max="100" value="0">
            </div>

            <div class="control-group">
                <div class="control-label">
                    <span>Color</span>
                    <span class="control-value" id="noiseColorValue">25</span>
                </div>
                <input type="range" class="control-slider" id="noiseColor" min="0" max="100" value="25">
            </div>
        </div>

        <!-- Output Panel -->
        <div class="output-panel">
            <div class="panel-title">📤 Export</div>

            <div class="export-options">
                <div style="font-size: 10px; color: #888; margin-bottom: 6px;">Format</div>
                <div class="format-selector">
                    <button class="format-button active" onclick="selectFormat('jpeg')">JPEG</button>
                    <button class="format-button" onclick="selectFormat('tiff')">TIFF</button>
                    <button class="format-button" onclick="selectFormat('png')">PNG</button>
                </div>

                <div class="control-group">
                    <div class="control-label">
                        <span>Quality</span>
                        <span class="control-value" id="qualityValue">90</span>
                    </div>
                    <input type="range" class="control-slider" id="quality" min="1" max="100" value="90">
                </div>

                <div class="control-group">
                    <div class="control-label">
                        <span>Color Space</span>
                    </div>
                    <select style="width: 100%; background: #404040; color: #e0e0e0; border: 1px solid #555; padding: 4px; border-radius: 3px; font-size: 10px;">
                        <option>sRGB</option>
                        <option>Adobe RGB</option>
                        <option>ProPhoto RGB</option>
                        <option>Display P3</option>
                    </select>
                </div>
            </div>

            <button class="preset-button" onclick="exportImage()" style="width: 100%; margin-bottom: 8px; background: linear-gradient(135deg, #28a745, #20c997);">
                📥 Export Image
            </button>

            <div class="panel-title" style="margin-top: 15px;">💾 Presets</div>

            <div class="preset-grid">
                <button class="preset-button" onclick="savePreset()">💾 Save</button>
                <button class="preset-button" onclick="loadPreset()">📂 Load</button>
                <button class="preset-button" onclick="resetAll()">🔄 Reset</button>
                <button class="preset-button" onclick="autoAdjust()">🤖 Auto</button>
            </div>

            <canvas class="histogram-mini" id="outputHistogram" width="250" height="60"></canvas>
        </div>
    </div>

    <script>
        class RAWProcessor {
            constructor() {
                this.canvas = document.getElementById('rawCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.basicHistogramCanvas = document.getElementById('basicHistogram');
                this.basicHistogramCtx = this.basicHistogramCanvas.getContext('2d');
                this.outputHistogramCanvas = document.getElementById('outputHistogram');
                this.outputHistogramCtx = this.outputHistogramCanvas.getContext('2d');
                this.toneCurveCanvas = document.getElementById('toneCurve');
                this.toneCurveCtx = this.toneCurveCanvas.getContext('2d');

                this.originalImageData = null;
                this.currentImageData = null;
                this.currentWB = 'auto';
                this.currentFormat = 'jpeg';

                this.settings = {
                    // Basic
                    exposure: 0,
                    highlights: 0,
                    shadows: 0,
                    whites: 0,
                    blacks: 0,

                    // White Balance
                    temperature: 5500,
                    tint: 0,

                    // Presence
                    clarity: 0,
                    vibrance: 0,
                    saturation: 0,

                    // Tone Curve
                    curveHighlights: 0,
                    curveLights: 0,
                    curveDarks: 0,
                    curveShadows: 0,

                    // Split Toning
                    highlightHue: 0,
                    highlightSat: 0,
                    shadowHue: 0,
                    shadowSat: 0,
                    balance: 0,

                    // Detail
                    sharpening: 25,
                    sharpenRadius: 1.0,
                    sharpenDetail: 25,
                    sharpenMasking: 0,
                    noiseLum: 0,
                    noiseColor: 25,

                    // Export
                    quality: 90
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
                this.drawToneCurve();
                this.drawColorWheels();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;
            }

            initializeEventListeners() {
                // All sliders
                Object.keys(this.settings).forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateSetting(setting, parseFloat(e.target.value));
                        });
                    }
                });

                // Tone curve interaction
                this.toneCurveCanvas.addEventListener('click', (e) => {
                    this.handleToneCurveClick(e);
                });

                // Color wheels
                ['highlightWheel', 'shadowWheel'].forEach(wheelId => {
                    const wheel = document.getElementById(wheelId);
                    if (wheel) {
                        wheel.addEventListener('click', (e) => {
                            this.handleColorWheelClick(e, wheelId);
                        });
                    }
                });
            }

            loadTestImage() {
                // Create a test RAW-like image
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Create a realistic photo-like test image
                const gradient = this.ctx.createRadialGradient(400, 300, 0, 400, 300, 400);
                gradient.addColorStop(0, '#f4f4f4');
                gradient.addColorStop(0.3, '#e8e8e8');
                gradient.addColorStop(0.7, '#d0d0d0');
                gradient.addColorStop(1, '#a0a0a0');

                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, 800, 600);

                // Add some color areas to simulate a photo
                // Sky area
                const skyGradient = this.ctx.createLinearGradient(0, 0, 0, 200);
                skyGradient.addColorStop(0, '#87ceeb');
                skyGradient.addColorStop(1, '#b0e0e6');
                this.ctx.fillStyle = skyGradient;
                this.ctx.fillRect(0, 0, 800, 200);

                // Landscape
                this.ctx.fillStyle = '#8fbc8f';
                this.ctx.fillRect(0, 200, 800, 150);

                // Buildings
                this.ctx.fillStyle = '#696969';
                this.ctx.fillRect(100, 150, 80, 100);
                this.ctx.fillRect(250, 120, 100, 130);
                this.ctx.fillRect(450, 140, 90, 110);
                this.ctx.fillRect(600, 130, 70, 120);

                // Some highlights and shadows
                this.ctx.fillStyle = '#ffffff';
                this.ctx.fillRect(200, 50, 50, 30); // Bright cloud

                this.ctx.fillStyle = '#2f4f4f';
                this.ctx.fillRect(0, 350, 800, 250); // Dark foreground

                // Add some color cast to simulate RAW
                this.ctx.fillStyle = 'rgba(255, 200, 150, 0.1)';
                this.ctx.fillRect(0, 0, 800, 600);

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );

                this.updateHistograms();
            }

            updateSetting(setting, value) {
                this.settings[setting] = value;

                // Update display value
                const valueElement = document.getElementById(setting + 'Value');
                if (valueElement) {
                    if (setting === 'temperature') {
                        valueElement.textContent = value + 'K';
                    } else if (setting === 'sharpenRadius') {
                        valueElement.textContent = value.toFixed(1);
                    } else {
                        valueElement.textContent = value;
                    }
                }

                this.processRAW();
                this.updateHistograms();
                this.checkClipping();
            }

            processRAW() {
                // Show processing indicator
                this.showProcessing(true);

                // Reset to original
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                // Apply processing in RAW workflow order
                this.applyWhiteBalance();
                this.applyBasicAdjustments();
                this.applyToneCurve();
                this.applySplitToning();
                this.applyPresence();
                this.applySharpening();
                this.applyNoiseReduction();

                // Draw to canvas
                this.ctx.putImageData(this.currentImageData, 0, 0);

                // Hide processing indicator
                setTimeout(() => {
                    this.showProcessing(false);
                }, 300);
            }

            applyWhiteBalance() {
                const data = this.currentImageData.data;
                const tempFactor = (this.settings.temperature - 5500) / 5500;
                const tintFactor = this.settings.tint / 100;

                for (let i = 0; i < data.length; i += 4) {
                    // Temperature adjustment
                    if (tempFactor > 0) { // Warmer
                        data[i] = Math.min(255, data[i] * (1 + tempFactor * 0.3)); // More red
                        data[i + 2] = Math.max(0, data[i + 2] * (1 - tempFactor * 0.2)); // Less blue
                    } else { // Cooler
                        data[i] = Math.max(0, data[i] * (1 + tempFactor * 0.2)); // Less red
                        data[i + 2] = Math.min(255, data[i + 2] * (1 - tempFactor * 0.3)); // More blue
                    }

                    // Tint adjustment
                    if (tintFactor > 0) { // More magenta
                        data[i] = Math.min(255, data[i] * (1 + tintFactor * 0.1));
                        data[i + 1] = Math.max(0, data[i + 1] * (1 - tintFactor * 0.1));
                        data[i + 2] = Math.min(255, data[i + 2] * (1 + tintFactor * 0.1));
                    } else { // More green
                        data[i + 1] = Math.min(255, data[i + 1] * (1 - tintFactor * 0.2));
                    }
                }
            }
        }

            applyBasicAdjustments() {
                const data = this.currentImageData.data;
                const exposureFactor = Math.pow(2, this.settings.exposure);

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    // Calculate luminance
                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                    const normalizedLum = luminance / 255;

                    // Exposure
                    let newR = r * exposureFactor;
                    let newG = g * exposureFactor;
                    let newB = b * exposureFactor;

                    // Highlights/Shadows
                    if (normalizedLum > 0.7) { // Highlights
                        const factor = 1 + (this.settings.highlights / 100) * (normalizedLum - 0.7) / 0.3;
                        newR *= factor;
                        newG *= factor;
                        newB *= factor;
                    } else if (normalizedLum < 0.3) { // Shadows
                        const factor = 1 + (this.settings.shadows / 100) * (0.3 - normalizedLum) / 0.3;
                        newR *= factor;
                        newG *= factor;
                        newB *= factor;
                    }

                    // Whites/Blacks
                    if (normalizedLum > 0.8) { // Whites
                        const factor = 1 + (this.settings.whites / 100) * (normalizedLum - 0.8) / 0.2;
                        newR *= factor;
                        newG *= factor;
                        newB *= factor;
                    } else if (normalizedLum < 0.2) { // Blacks
                        const factor = 1 + (this.settings.blacks / 100) * (0.2 - normalizedLum) / 0.2;
                        newR *= factor;
                        newG *= factor;
                        newB *= factor;
                    }

                    data[i] = Math.max(0, Math.min(255, newR));
                    data[i + 1] = Math.max(0, Math.min(255, newG));
                    data[i + 2] = Math.max(0, Math.min(255, newB));
                }
            }

            applyToneCurve() {
                // Simple tone curve based on control points
                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i] / 255;
                    const g = data[i + 1] / 255;
                    const b = data[i + 2] / 255;

                    // Apply curve adjustments based on luminance zones
                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

                    let adjustment = 0;
                    if (luminance > 0.75) { // Highlights
                        adjustment = this.settings.curveHighlights / 100;
                    } else if (luminance > 0.5) { // Lights
                        adjustment = this.settings.curveLights / 100;
                    } else if (luminance > 0.25) { // Darks
                        adjustment = this.settings.curveDarks / 100;
                    } else { // Shadows
                        adjustment = this.settings.curveShadows / 100;
                    }

                    const factor = 1 + adjustment;
                    data[i] = Math.max(0, Math.min(255, data[i] * factor));
                    data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                    data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                }
            }

            applySplitToning() {
                const data = this.currentImageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i] / 255;
                    const g = data[i + 1] / 255;
                    const b = data[i + 2] / 255;

                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

                    // Determine if pixel is in highlights or shadows
                    const balance = (this.settings.balance + 100) / 200; // 0-1 range
                    const isHighlight = luminance > balance;

                    if (isHighlight && this.settings.highlightSat > 0) {
                        // Apply highlight toning
                        const hue = this.settings.highlightHue;
                        const saturation = this.settings.highlightSat / 100;
                        const toneColor = this.hslToRgb(hue, saturation, luminance);

                        data[i] = Math.max(0, Math.min(255, (data[i] + toneColor.r * saturation * 255) / (1 + saturation)));
                        data[i + 1] = Math.max(0, Math.min(255, (data[i + 1] + toneColor.g * saturation * 255) / (1 + saturation)));
                        data[i + 2] = Math.max(0, Math.min(255, (data[i + 2] + toneColor.b * saturation * 255) / (1 + saturation)));
                    } else if (!isHighlight && this.settings.shadowSat > 0) {
                        // Apply shadow toning
                        const hue = this.settings.shadowHue;
                        const saturation = this.settings.shadowSat / 100;
                        const toneColor = this.hslToRgb(hue, saturation, luminance);

                        data[i] = Math.max(0, Math.min(255, (data[i] + toneColor.r * saturation * 255) / (1 + saturation)));
                        data[i + 1] = Math.max(0, Math.min(255, (data[i + 1] + toneColor.g * saturation * 255) / (1 + saturation)));
                        data[i + 2] = Math.max(0, Math.min(255, (data[i + 2] + toneColor.b * saturation * 255) / (1 + saturation)));
                    }
                }
            }

            applyPresence() {
                const data = this.currentImageData.data;
                const clarityFactor = this.settings.clarity / 100;
                const vibranceFactor = this.settings.vibrance / 100;
                const saturationFactor = this.settings.saturation / 100;

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i] / 255;
                    const g = data[i + 1] / 255;
                    const b = data[i + 2] / 255;

                    // Saturation adjustment
                    if (saturationFactor !== 0) {
                        const hsl = this.rgbToHsl(r, g, b);
                        hsl.s = Math.max(0, Math.min(1, hsl.s * (1 + saturationFactor)));
                        const rgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);

                        data[i] = rgb.r * 255;
                        data[i + 1] = rgb.g * 255;
                        data[i + 2] = rgb.b * 255;
                    }

                    // Vibrance (protect skin tones)
                    if (vibranceFactor !== 0) {
                        const max = Math.max(r, g, b);
                        const min = Math.min(r, g, b);
                        const currentSat = max - min;

                        if (currentSat < 0.5) { // Only boost less saturated colors
                            const factor = 1 + vibranceFactor * (1 - currentSat);
                            data[i] = Math.max(0, Math.min(255, data[i] * factor));
                            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
                            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
                        }
                    }

                    // Clarity (local contrast)
                    if (clarityFactor !== 0) {
                        const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                        const factor = 1 + clarityFactor * 0.3;

                        data[i] = Math.max(0, Math.min(255, data[i] + (data[i] - luminance) * clarityFactor * 0.5));
                        data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + (data[i + 1] - luminance) * clarityFactor * 0.5));
                        data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + (data[i + 2] - luminance) * clarityFactor * 0.5));
                    }
                }
            }

            applySharpening() {
                if (this.settings.sharpening === 0) return;

                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const amount = this.settings.sharpening / 100;
                const radius = this.settings.sharpenRadius;

                // Simple unsharp mask
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const index = (y * width + x) * 4;

                        for (let c = 0; c < 3; c++) {
                            const center = data[index + c];
                            const surrounding = (
                                data[((y-1) * width + x) * 4 + c] +
                                data[((y+1) * width + x) * 4 + c] +
                                data[(y * width + (x-1)) * 4 + c] +
                                data[(y * width + (x+1)) * 4 + c]
                            ) / 4;

                            const difference = center - surrounding;
                            output[index + c] = Math.max(0, Math.min(255, center + difference * amount));
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyNoiseReduction() {
                // Simple noise reduction implementation
                if (this.settings.noiseLum === 0 && this.settings.noiseColor === 0) return;

                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const lumStrength = this.settings.noiseLum / 100;
                const colorStrength = this.settings.noiseColor / 100;

                // Simple blur for noise reduction
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const index = (y * width + x) * 4;

                        for (let c = 0; c < 3; c++) {
                            const surrounding = (
                                data[((y-1) * width + (x-1)) * 4 + c] +
                                data[((y-1) * width + x) * 4 + c] +
                                data[((y-1) * width + (x+1)) * 4 + c] +
                                data[(y * width + (x-1)) * 4 + c] +
                                data[index + c] +
                                data[(y * width + (x+1)) * 4 + c] +
                                data[((y+1) * width + (x-1)) * 4 + c] +
                                data[((y+1) * width + x) * 4 + c] +
                                data[((y+1) * width + (x+1)) * 4 + c]
                            ) / 9;

                            const strength = c === 0 ? lumStrength : colorStrength;
                            output[index + c] = data[index + c] * (1 - strength) + surrounding * strength;
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            rgbToHsl(r, g, b) {
                const max = Math.max(r, g, b);
                const min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;

                if (max === min) {
                    h = s = 0;
                } else {
                    const d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }

                return { h: h * 360, s, l };
            }

            hslToRgb(h, s, l) {
                h /= 360;
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1/6) return p + (q - p) * 6 * t;
                    if (t < 1/2) return q;
                    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                    return p;
                };

                let r, g, b;
                if (s === 0) {
                    r = g = b = l;
                } else {
                    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    const p = 2 * l - q;
                    r = hue2rgb(p, q, h + 1/3);
                    g = hue2rgb(p, q, h);
                    b = hue2rgb(p, q, h - 1/3);
                }

                return { r, g, b };
            }
        }

            updateHistograms() {
                this.drawHistogram(this.basicHistogramCtx, this.basicHistogramCanvas);
                this.drawHistogram(this.outputHistogramCtx, this.outputHistogramCanvas);
            }

            drawHistogram(ctx, canvas) {
                const data = this.currentImageData.data;
                const histogram = { red: new Array(256).fill(0), green: new Array(256).fill(0), blue: new Array(256).fill(0) };

                for (let i = 0; i < data.length; i += 4) {
                    histogram.red[data[i]]++;
                    histogram.green[data[i + 1]]++;
                    histogram.blue[data[i + 2]]++;
                }

                const maxValue = Math.max(
                    Math.max(...histogram.red),
                    Math.max(...histogram.green),
                    Math.max(...histogram.blue)
                );

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const barWidth = canvas.width / 256;

                // Draw RGB channels
                ['red', 'green', 'blue'].forEach((channel, index) => {
                    const colors = ['#ff444444', '#44ff4444', '#4444ff44'];
                    ctx.fillStyle = colors[index];

                    for (let i = 0; i < 256; i++) {
                        const barHeight = (histogram[channel][i] / maxValue) * canvas.height;
                        ctx.fillRect(i * barWidth, canvas.height - barHeight, barWidth, barHeight);
                    }
                });
            }

            drawToneCurve() {
                const ctx = this.toneCurveCtx;
                const width = this.toneCurveCanvas.width;
                const height = this.toneCurveCanvas.height;

                ctx.clearRect(0, 0, width, height);
                ctx.fillStyle = '#0f0f0f';
                ctx.fillRect(0, 0, width, height);

                // Draw grid
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;

                for (let i = 0; i <= 4; i++) {
                    const x = (i / 4) * width;
                    const y = (i / 4) * height;

                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }

                // Draw diagonal reference line
                ctx.strokeStyle = '#555';
                ctx.beginPath();
                ctx.moveTo(0, height);
                ctx.lineTo(width, 0);
                ctx.stroke();

                // Draw tone curve
                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 2;
                ctx.beginPath();

                for (let x = 0; x <= width; x++) {
                    const input = x / width;
                    let output = input;

                    // Apply curve adjustments
                    if (input > 0.75) {
                        output += (this.settings.curveHighlights / 100) * (input - 0.75) / 0.25;
                    } else if (input > 0.5) {
                        output += (this.settings.curveLights / 100) * (input - 0.5) / 0.25;
                    } else if (input > 0.25) {
                        output += (this.settings.curveDarks / 100) * (input - 0.25) / 0.25;
                    } else {
                        output += (this.settings.curveShadows / 100) * input / 0.25;
                    }

                    const y = height - (output * height);

                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }

                ctx.stroke();
            }

            drawColorWheels() {
                this.drawColorWheel('highlightWheel', this.settings.highlightHue);
                this.drawColorWheel('shadowWheel', this.settings.shadowHue);
            }

            drawColorWheel(canvasId, hue) {
                const canvas = document.getElementById(canvasId);
                const ctx = canvas.getContext('2d');
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 2;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw color wheel
                for (let angle = 0; angle < 360; angle += 1) {
                    const startAngle = (angle - 1) * Math.PI / 180;
                    const endAngle = angle * Math.PI / 180;

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.lineWidth = radius / 2;
                    ctx.strokeStyle = `hsl(${angle}, 100%, 50%)`;
                    ctx.stroke();
                }

                // Draw current hue indicator
                const angle = (hue - 90) * Math.PI / 180;
                const x = centerX + Math.cos(angle) * radius * 0.8;
                const y = centerY + Math.sin(angle) * radius * 0.8;

                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
                ctx.strokeStyle = '#000';
                ctx.lineWidth = 1;
                ctx.stroke();
            }

            checkClipping() {
                const data = this.currentImageData.data;
                let clippedHighlights = 0;
                let recoverableHighlights = 0;
                let totalPixels = data.length / 4;

                for (let i = 0; i < data.length; i += 4) {
                    const max = Math.max(data[i], data[i + 1], data[i + 2]);
                    if (max >= 255) {
                        clippedHighlights++;
                    } else if (max >= 240) {
                        recoverableHighlights++;
                    }
                }

                const clippedPercent = ((clippedHighlights / totalPixels) * 100).toFixed(1);
                const recoverablePercent = ((recoverableHighlights / totalPixels) * 100).toFixed(1);

                document.getElementById('clippedHighlights').textContent = clippedPercent + '%';
                document.getElementById('recoverableHighlights').textContent = recoverablePercent + '%';

                // Show clipping warning
                const warning = document.getElementById('clippingWarning');
                if (clippedHighlights > totalPixels * 0.01) { // More than 1% clipped
                    warning.style.display = 'block';
                } else {
                    warning.style.display = 'none';
                }
            }

            showProcessing(show) {
                document.getElementById('processingIndicator').style.display = show ? 'flex' : 'none';
            }

            handleToneCurveClick(e) {
                const rect = this.toneCurveCanvas.getBoundingClientRect();
                const x = (e.clientX - rect.left) / rect.width;
                const y = 1 - (e.clientY - rect.top) / rect.height;

                // Determine which zone was clicked and adjust
                if (x > 0.75) {
                    this.settings.curveHighlights = Math.round((y - x) * 100);
                    document.getElementById('curveHighlights').value = this.settings.curveHighlights;
                    document.getElementById('curveHighlightsValue').textContent = this.settings.curveHighlights;
                } else if (x > 0.5) {
                    this.settings.curveLights = Math.round((y - x) * 100);
                    document.getElementById('curveLights').value = this.settings.curveLights;
                    document.getElementById('curveLightsValue').textContent = this.settings.curveLights;
                } else if (x > 0.25) {
                    this.settings.curveDarks = Math.round((y - x) * 100);
                    document.getElementById('curveDarks').value = this.settings.curveDarks;
                    document.getElementById('curveDarksValue').textContent = this.settings.curveDarks;
                } else {
                    this.settings.curveShadows = Math.round((y - x) * 100);
                    document.getElementById('curveShadows').value = this.settings.curveShadows;
                    document.getElementById('curveShadowsValue').textContent = this.settings.curveShadows;
                }

                this.drawToneCurve();
                this.processRAW();
            }

            handleColorWheelClick(e, wheelId) {
                const canvas = e.target;
                const rect = canvas.getBoundingClientRect();
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const x = e.clientX - rect.left - centerX;
                const y = e.clientY - rect.top - centerY;

                const angle = Math.atan2(y, x) * 180 / Math.PI;
                const hue = (angle + 360) % 360;

                if (wheelId === 'highlightWheel') {
                    this.settings.highlightHue = Math.round(hue);
                    document.getElementById('highlightHue').value = this.settings.highlightHue;
                    document.getElementById('highlightHueValue').textContent = this.settings.highlightHue;
                } else {
                    this.settings.shadowHue = Math.round(hue);
                    document.getElementById('shadowHue').value = this.settings.shadowHue;
                    document.getElementById('shadowHueValue').textContent = this.settings.shadowHue;
                }

                this.drawColorWheels();
                this.processRAW();
            }
        }

        // Global functions
        function setWhiteBalance(preset) {
            if (!rawProcessor) return;

            // Update button states
            document.querySelectorAll('.wb-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const presets = {
                auto: { temp: 5500, tint: 0 },
                daylight: { temp: 5500, tint: 0 },
                cloudy: { temp: 6500, tint: 0 },
                shade: { temp: 7500, tint: 0 },
                tungsten: { temp: 3200, tint: 0 },
                fluorescent: { temp: 4000, tint: 20 },
                flash: { temp: 5500, tint: 0 },
                custom: { temp: rawProcessor.settings.temperature, tint: rawProcessor.settings.tint }
            };

            const preset_values = presets[preset];
            if (preset_values) {
                document.getElementById('temperature').value = preset_values.temp;
                document.getElementById('tint').value = preset_values.tint;
                rawProcessor.updateSetting('temperature', preset_values.temp);
                rawProcessor.updateSetting('tint', preset_values.tint);
            }

            rawProcessor.currentWB = preset;
            document.getElementById('currentWB').textContent = preset.charAt(0).toUpperCase() + preset.slice(1);
        }

        function applyCreativeLook(lookName) {
            if (!rawProcessor) return;

            const looks = {
                natural: { vibrance: 10, saturation: 5, clarity: 5 },
                vivid: { vibrance: 40, saturation: 20, clarity: 15 },
                dramatic: { vibrance: 20, saturation: 10, clarity: 30, curveHighlights: -20, curveShadows: 20 },
                vintage: { vibrance: -10, saturation: -15, highlightHue: 30, highlightSat: 20, shadowHue: 200, shadowSat: 15 },
                film: { vibrance: 5, saturation: -5, curveHighlights: -10, curveShadows: 10 },
                bw: { saturation: -100, clarity: 20 }
            };

            const look = looks[lookName];
            if (look) {
                Object.keys(look).forEach(setting => {
                    const slider = document.getElementById(setting);
                    if (slider) {
                        slider.value = look[setting];
                        rawProcessor.updateSetting(setting, look[setting]);
                    }
                });
            }
        }

        function selectFormat(format) {
            if (!rawProcessor) return;

            document.querySelectorAll('.format-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            rawProcessor.currentFormat = format;
        }

        function exportImage() {
            if (!rawProcessor) return;

            rawProcessor.showProcessing(true);

            setTimeout(() => {
                // Create download link
                const link = document.createElement('a');
                link.download = `processed_image.${rawProcessor.currentFormat}`;

                if (rawProcessor.currentFormat === 'jpeg') {
                    link.href = rawProcessor.canvas.toDataURL('image/jpeg', rawProcessor.settings.quality / 100);
                } else if (rawProcessor.currentFormat === 'png') {
                    link.href = rawProcessor.canvas.toDataURL('image/png');
                } else {
                    link.href = rawProcessor.canvas.toDataURL('image/png'); // TIFF fallback to PNG
                }

                link.click();
                rawProcessor.showProcessing(false);
            }, 1000);
        }

        function savePreset() {
            if (!rawProcessor) return;

            const name = prompt('Název presetu:');
            if (name) {
                const preset = JSON.stringify(rawProcessor.settings);
                localStorage.setItem('rawPreset_' + name, preset);
                alert('Preset uložen!');
            }
        }

        function loadPreset() {
            if (!rawProcessor) return;

            const name = prompt('Název presetu k načtení:');
            if (name) {
                const preset = localStorage.getItem('rawPreset_' + name);
                if (preset) {
                    const settings = JSON.parse(preset);
                    Object.keys(settings).forEach(key => {
                        const slider = document.getElementById(key);
                        if (slider) {
                            slider.value = settings[key];
                            rawProcessor.updateSetting(key, settings[key]);
                        }
                    });
                    alert('Preset načten!');
                } else {
                    alert('Preset nenalezen!');
                }
            }
        }

        function resetAll() {
            if (!rawProcessor) return;

            if (confirm('Resetovat všechna nastavení?')) {
                Object.keys(rawProcessor.settings).forEach(key => {
                    const slider = document.getElementById(key);
                    if (slider) {
                        let defaultValue = 0;
                        if (key === 'temperature') defaultValue = 5500;
                        if (key === 'sharpening') defaultValue = 25;
                        if (key === 'sharpenRadius') defaultValue = 1.0;
                        if (key === 'sharpenDetail') defaultValue = 25;
                        if (key === 'noiseColor') defaultValue = 25;
                        if (key === 'quality') defaultValue = 90;

                        slider.value = defaultValue;
                        rawProcessor.updateSetting(key, defaultValue);
                    }
                });
            }
        }

        function autoAdjust() {
            if (!rawProcessor) return;

            rawProcessor.showProcessing(true);

            setTimeout(() => {
                // Simulate auto adjustments
                const autoSettings = {
                    exposure: 0.3,
                    highlights: -25,
                    shadows: 15,
                    whites: 10,
                    blacks: -10,
                    vibrance: 15,
                    clarity: 10
                };

                Object.keys(autoSettings).forEach(key => {
                    const slider = document.getElementById(key);
                    if (slider) {
                        slider.value = autoSettings[key];
                        rawProcessor.updateSetting(key, autoSettings[key]);
                    }
                });

                rawProcessor.showProcessing(false);
            }, 2000);
        }

        // Initialize the RAW processor
        let rawProcessor;
        document.addEventListener('DOMContentLoaded', () => {
            rawProcessor = new RAWProcessor();
        });
