<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Histogram & Scopes - <PERSON><PERSON><PERSON><PERSON> obrazu</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .scopes-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            grid-template-rows: 1fr 300px;
            grid-template-areas: 
                "canvas controls"
                "scopes controls";
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }
        
        .scopes-area {
            grid-area: scopes;
            background: #2d2d2d;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 1px;
            background: #404040;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
            border-left: 1px solid #404040;
        }
        
        .scope-panel {
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            border: 1px solid #404040;
        }
        
        .scope-title {
            position: absolute;
            top: 5px;
            left: 10px;
            font-size: 12px;
            font-weight: bold;
            color: #00d4ff;
            z-index: 10;
        }
        
        .scope-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #0f0f0f;
            display: block;
            margin: auto;
            border: 1px solid #404040;
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px dashed #00d4ff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .auto-button {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .auto-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }
        
        .control-group {
            margin-bottom: 15px;
            background: #252525;
            padding: 12px;
            border-radius: 6px;
        }
        
        .control-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #ccc;
        }
        
        .control-value {
            color: #00d4ff;
            font-weight: bold;
            min-width: 35px;
            text-align: right;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin: 15px 0 10px 0;
            border-bottom: 1px solid #404040;
            padding-bottom: 5px;
        }
        
        .tool-button {
            width: 100%;
            background: #404040;
            color: #e0e0e0;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px 0;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .tool-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
            color: #00d4ff;
        }
        
        .tool-button.active {
            background: #00d4ff;
            color: #000;
            border-color: #00d4ff;
        }
        
        .info-display {
            background: #252525;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .color-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .color-value {
            background: #1a1a1a;
            padding: 5px;
            border-radius: 3px;
            text-align: center;
        }
        
        /* Help System */
        .help-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
            transition: all 0.3s;
        }
        
        .help-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 212, 255, 0.4);
        }
        
        .help-toggle.active {
            background: #ff6b6b;
            color: #fff;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: #fff;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.4;
            max-width: 280px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            border: 1px solid #00d4ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
        }
        
        .tooltip.show {
            opacity: 1;
            visibility: visible;
        }
        
        .tooltip::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #00d4ff;
        }
        
        .tooltip-title {
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 4px;
        }
        
        .control-group.help-active {
            position: relative;
            border: 2px solid #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
    </style>
</head>
<body>
    <!-- Help Toggle Button -->
    <button class="help-toggle" id="helpToggle" onclick="toggleHelp()" title="Zapnout/vypnout nápovědu">?</button>
    
    <div class="scopes-container">
        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas" width="800" height="600"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📊</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
        </div>

        <!-- Scopes Area -->
        <div class="scopes-area">
            <div class="scope-panel" data-help="Histogram|Graf rozložení světlosti v obrázku. Levá strana = tmavé tóny, pravá = světlé tóny.">
                <div class="scope-title">📊 Histogram</div>
                <canvas class="scope-canvas" id="histogramCanvas" width="300" height="150"></canvas>
            </div>
            
            <div class="scope-panel" data-help="Waveform|Zobrazuje světlost pixelů podle jejich horizontální pozice. Užitečné pro kontrolu expozice.">
                <div class="scope-title">〰️ Waveform</div>
                <canvas class="scope-canvas" id="waveformCanvas" width="300" height="150"></canvas>
            </div>
            
            <div class="scope-panel" data-help="Vectorscope|Kruhový graf zobrazující sytost a odstín barev. Střed = šedá, okraje = syté barvy.">
                <div class="scope-title">🎯 Vectorscope</div>
                <canvas class="scope-canvas" id="vectorscopeCanvas" width="300" height="150"></canvas>
            </div>
            
            <div class="scope-panel" data-help="RGB Parade|Tři samostatné waveformy pro červený, zelený a modrý kanál. Ideální pro color grading.">
                <div class="scope-title">🌈 RGB Parade</div>
                <canvas class="scope-canvas" id="rgbParadeCanvas" width="300" height="150"></canvas>
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="panel-title">📊 Histogram & Scopes</div>
            
            <button class="auto-button" onclick="document.getElementById('fileInput').click()" style="background: linear-gradient(135deg, #28a745, #20c997); margin-bottom: 10px;" data-help="Import|Nahrajte vlastní obrázek z počítače pro analýzu histogramu a scope.">📁 Importovat obrázek</button>
            
            <button class="auto-button" id="autoButton" onclick="autoAnalyze()" data-help="Auto analýza|Automatická analýza obrazu s doporučeními pro úpravy. 3 různé módy analýzy.">📈 Auto analýza</button>
            
            <div class="section-title">Informace o pixelu</div>
            
            <div class="info-display" id="pixelInfo" data-help="Pixel Info|Informace o barvě pixelu pod kurzorem myši. Zobrazuje RGB, HSL a další hodnoty.">
                <div class="color-info">
                    <div class="color-value">R: <span id="redValue">-</span></div>
                    <div class="color-value">G: <span id="greenValue">-</span></div>
                    <div class="color-value">B: <span id="blueValue">-</span></div>
                    <div class="color-value">L: <span id="lumValue">-</span></div>
                </div>
                <div style="text-align: center; font-size: 10px; color: #888;">
                    Najeďte myší na obrázek
                </div>
            </div>
            
            <div class="section-title">Nastavení scope</div>
            
            <div class="control-group" data-help="Citlivost|Citlivost zobrazení v scope. Vyšší hodnoty = citlivější detekce.">
                <div class="control-label">
                    <span>Citlivost</span>
                    <span class="control-value" id="sensitivityValue">50</span>
                </div>
                <input type="range" class="control-slider" id="sensitivity" min="1" max="100" value="50">
            </div>
            
            <div class="control-group" data-help="Zoom|Přiblížení scope pro detailnější analýzu. Užitečné pro jemné úpravy.">
                <div class="control-label">
                    <span>Zoom</span>
                    <span class="control-value" id="zoomValue">100</span>
                </div>
                <input type="range" class="control-slider" id="zoom" min="50" max="200" value="100">
            </div>
            
            <div class="section-title">Analýza obrazu</div>
            
            <div class="info-display" id="imageAnalysis" data-help="Image Analysis|Automatická analýza kvality obrazu s doporučeními pro úpravy.">
                <div style="font-weight: bold; margin-bottom: 5px;">Kvalita obrazu:</div>
                <div id="qualityInfo">Načtěte obrázek pro analýzu</div>
            </div>
            
            <div class="section-title">Scope módy</div>
            
            <div class="control-group" data-help="Histogram Mode|Přepínání mezi různými módy histogramu - RGB, jednotlivé kanály, luminance.">
                <button class="tool-button active" onclick="setScopeMode('histogram', 'rgb')" id="histRgbBtn">RGB</button>
                <button class="tool-button" onclick="setScopeMode('histogram', 'red')" id="histRedBtn">Červený</button>
                <button class="tool-button" onclick="setScopeMode('histogram', 'green')" id="histGreenBtn">Zelený</button>
                <button class="tool-button" onclick="setScopeMode('histogram', 'blue')" id="histBlueBtn">Modrý</button>
            </div>
            
            <div class="section-title">Export dat</div>
            
            <div class="control-group" data-help="Export Functions|Export naměřených dat a analýz pro další zpracování.">
                <button class="tool-button" onclick="exportHistogramData()" style="width: 100%; margin-bottom: 5px;">📊 Export histogram dat</button>
                <button class="tool-button" onclick="exportScopeImage()" style="width: 100%; margin-bottom: 5px;">📸 Export scope obrázek</button>
                <button class="tool-button" onclick="generateReport()" style="width: 100%;">📋 Generovat report</button>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="tool-button" onclick="loadTestImage()" style="width: 100%; margin-top: 10px;" data-help="Test obrázek|Načte ukázkový obrázek s různými barvami pro testování scope funkcí.">🎨 Test obrázek</button>
                <button class="tool-button" onclick="resetScopes()" style="width: 100%; margin-top: 5px;" data-help="Reset|Resetuje všechna nastavení scope na výchozí hodnoty.">🔄 Reset scope</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let canvas, ctx;
        let histogramCanvas, histogramCtx;
        let waveformCanvas, waveformCtx;
        let vectorscopeCanvas, vectorscopeCtx;
        let rgbParadeCanvas, rgbParadeCtx;
        let originalImageData, currentImageData;
        let helpMode = false;
        let currentTooltip = null;
        let autoMode = 0;
        
        const scopeSettings = {
            sensitivity: 50,
            zoom: 100,
            histogramMode: 'rgb'
        };

        function initializeApp() {
            console.log('🚀 Initializing Histogram & Scopes...');

            // Get canvas elements
            canvas = document.getElementById('mainCanvas');
            histogramCanvas = document.getElementById('histogramCanvas');
            waveformCanvas = document.getElementById('waveformCanvas');
            vectorscopeCanvas = document.getElementById('vectorscopeCanvas');
            rgbParadeCanvas = document.getElementById('rgbParadeCanvas');

            if (!canvas || !histogramCanvas || !waveformCanvas || !vectorscopeCanvas || !rgbParadeCanvas) {
                console.error('❌ Canvas elements not found!');
                return false;
            }

            ctx = canvas.getContext('2d');
            histogramCtx = histogramCanvas.getContext('2d');
            waveformCtx = waveformCanvas.getContext('2d');
            vectorscopeCtx = vectorscopeCanvas.getContext('2d');
            rgbParadeCtx = rgbParadeCanvas.getContext('2d');

            setupEventListeners();
            loadTestImage();
            updateAutoButtonText();

            console.log('✅ Histogram & Scopes initialized successfully');
            return true;
        }

        function setupEventListeners() {
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('drop', handleDrop);
                uploadZone.addEventListener('dragleave', (e) => {
                    e.currentTarget.style.background = '';
                });
            }

            // Mouse tracking for pixel info
            canvas.addEventListener('mousemove', updatePixelInfo);
            canvas.addEventListener('mouseleave', clearPixelInfo);

            // Settings sliders
            ['sensitivity', 'zoom'].forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    slider.addEventListener('input', (e) => {
                        updateSetting(key, parseFloat(e.target.value));
                    });
                }
            });
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('📁 Loading file:', file.name);
                loadImageFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.background = 'rgba(0, 212, 255, 0.15)';
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.background = '';
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                console.log('📁 Loading dropped file:', files[0].name);
                loadImageFile(files[0]);
            }
        }

        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    console.log('🖼️ Image loaded:', img.width, 'x', img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    console.error('❌ Error loading image');
                    alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('❌ Error reading file');
                alert('Chyba při čtení souboru.');
            };
            reader.readAsDataURL(file);
        }

        function drawImageToCanvas(img) {
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;

            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;

            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;

            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, maxWidth, maxHeight);
            ctx.drawImage(img, x, y, width, height);

            originalImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);
            currentImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);

            hideUploadZone();
            updateAllScopes();
            analyzeImage();

            console.log('✅ Image drawn to canvas and scopes updated');
        }

        function loadTestImage() {
            console.log('🎨 Loading test image...');

            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Create test pattern with various colors and gradients
            const gradient1 = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient1.addColorStop(0, '#000000');
            gradient1.addColorStop(0.25, '#ff0000');
            gradient1.addColorStop(0.5, '#00ff00');
            gradient1.addColorStop(0.75, '#0000ff');
            gradient1.addColorStop(1, '#ffffff');

            ctx.fillStyle = gradient1;
            ctx.fillRect(0, 0, canvas.width, 200);

            // Color bars for scope testing
            const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
            const barWidth = canvas.width / colors.length;

            colors.forEach((color, index) => {
                ctx.fillStyle = color;
                ctx.fillRect(index * barWidth, 200, barWidth, 100);
            });

            // Gradient circles
            for (let i = 0; i < 5; i++) {
                const gradient = ctx.createRadialGradient(
                    150 + i * 120, 400, 0,
                    150 + i * 120, 400, 50
                );
                gradient.addColorStop(0, `hsl(${i * 72}, 100%, 50%)`);
                gradient.addColorStop(1, `hsl(${i * 72}, 100%, 20%)`);

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(150 + i * 120, 400, 50, 0, 2 * Math.PI);
                ctx.fill();
            }

            // Add text
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('HISTOGRAM & SCOPES TEST PATTERN', 180, 550);

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            hideUploadZone();
            updateAllScopes();
            analyzeImage();

            console.log('✅ Test image loaded');
        }

        function updateSetting(setting, value) {
            scopeSettings[setting] = value;

            const valueDisplay = document.getElementById(setting + 'Value');
            if (valueDisplay) {
                valueDisplay.textContent = value;
            }

            console.log(`🎚️ ${setting} changed to:`, value);
            updateAllScopes();
        }

        function updatePixelInfo(event) {
            if (!currentImageData) return;

            const rect = canvas.getBoundingClientRect();
            const x = Math.floor(event.clientX - rect.left);
            const y = Math.floor(event.clientY - rect.top);

            if (x >= 0 && x < canvas.width && y >= 0 && y < canvas.height) {
                const index = (y * canvas.width + x) * 4;
                const data = currentImageData.data;

                const r = data[index];
                const g = data[index + 1];
                const b = data[index + 2];
                const luminance = Math.round(0.299 * r + 0.587 * g + 0.114 * b);

                document.getElementById('redValue').textContent = r;
                document.getElementById('greenValue').textContent = g;
                document.getElementById('blueValue').textContent = b;
                document.getElementById('lumValue').textContent = luminance;
            }
        }

        function clearPixelInfo() {
            document.getElementById('redValue').textContent = '-';
            document.getElementById('greenValue').textContent = '-';
            document.getElementById('blueValue').textContent = '-';
            document.getElementById('lumValue').textContent = '-';
        }

        function updateAllScopes() {
            if (!currentImageData) return;

            updateHistogram();
            updateWaveform();
            updateVectorscope();
            updateRGBParade();
        }

        function updateHistogram() {
            if (!histogramCtx || !currentImageData) return;

            histogramCtx.fillStyle = '#1a1a1a';
            histogramCtx.fillRect(0, 0, histogramCanvas.width, histogramCanvas.height);

            const data = currentImageData.data;
            const histogram = new Array(256).fill(0);

            if (scopeSettings.histogramMode === 'rgb') {
                for (let i = 0; i < data.length; i += 4) {
                    const brightness = Math.round((data[i] + data[i + 1] + data[i + 2]) / 3);
                    histogram[brightness]++;
                }

                drawHistogramCurve(histogram, '#00d4ff');
            } else {
                const channelIndex = scopeSettings.histogramMode === 'red' ? 0 :
                                   scopeSettings.histogramMode === 'green' ? 1 : 2;
                const color = scopeSettings.histogramMode === 'red' ? '#ff4444' :
                             scopeSettings.histogramMode === 'green' ? '#44ff44' : '#4444ff';

                for (let i = channelIndex; i < data.length; i += 4) {
                    histogram[data[i]]++;
                }

                drawHistogramCurve(histogram, color);
            }
        }

        function drawHistogramCurve(histogram, color) {
            const maxValue = Math.max(...histogram);
            if (maxValue === 0) return;

            // Apply sensitivity and zoom settings
            const sensitivity = scopeSettings.sensitivity / 50; // Normalize to 0.02-2.0
            const zoom = scopeSettings.zoom / 100; // Normalize to 0.5-2.0

            histogramCtx.strokeStyle = color;
            histogramCtx.lineWidth = Math.max(1, 2 * zoom);
            histogramCtx.beginPath();

            for (let i = 0; i < 256; i++) {
                const x = (i / 255) * histogramCanvas.width;
                // Apply sensitivity to height calculation
                const normalizedHeight = (histogram[i] / maxValue) * sensitivity;
                const y = histogramCanvas.height - Math.min(normalizedHeight, 1) * histogramCanvas.height * zoom;

                if (i === 0) {
                    histogramCtx.moveTo(x, y);
                } else {
                    histogramCtx.lineTo(x, y);
                }
            }

            histogramCtx.stroke();
        }

        function updateWaveform() {
            if (!waveformCtx || !currentImageData) return;

            waveformCtx.fillStyle = '#1a1a1a';
            waveformCtx.fillRect(0, 0, waveformCanvas.width, waveformCanvas.height);

            const data = currentImageData.data;
            const width = canvas.width;
            const height = canvas.height;

            // Apply sensitivity and zoom settings
            const sensitivity = scopeSettings.sensitivity / 50;
            const zoom = scopeSettings.zoom / 100;
            const alpha = Math.min(0.8, 0.1 + sensitivity * 0.3);
            const step = Math.max(1, Math.round(4 / zoom)); // More points when zoomed

            waveformCtx.fillStyle = `rgba(0, 212, 255, ${alpha})`;

            for (let x = 0; x < width; x += step) {
                for (let y = 0; y < height; y += step) {
                    const index = (y * width + x) * 4;
                    const luminance = (data[index] + data[index + 1] + data[index + 2]) / 3;

                    const waveX = (x / width) * waveformCanvas.width;
                    const waveY = waveformCanvas.height - (luminance / 255) * waveformCanvas.height * zoom;

                    const pointSize = Math.max(1, Math.round(zoom));
                    waveformCtx.fillRect(waveX, waveY, pointSize, pointSize);
                }
            }
        }

        function updateVectorscope() {
            if (!vectorscopeCtx || !currentImageData) return;

            vectorscopeCtx.fillStyle = '#1a1a1a';
            vectorscopeCtx.fillRect(0, 0, vectorscopeCanvas.width, vectorscopeCanvas.height);

            const centerX = vectorscopeCanvas.width / 2;
            const centerY = vectorscopeCanvas.height / 2;
            const radius = Math.min(centerX, centerY) - 10;

            // Draw vectorscope grid
            vectorscopeCtx.strokeStyle = '#333';
            vectorscopeCtx.lineWidth = 1;

            // Draw circles
            for (let r = radius / 4; r <= radius; r += radius / 4) {
                vectorscopeCtx.beginPath();
                vectorscopeCtx.arc(centerX, centerY, r, 0, 2 * Math.PI);
                vectorscopeCtx.stroke();
            }

            // Draw color targets
            const targets = [
                { angle: 0, color: '#ff0000', label: 'R' },
                { angle: 60, color: '#ffff00', label: 'Y' },
                { angle: 120, color: '#00ff00', label: 'G' },
                { angle: 180, color: '#00ffff', label: 'C' },
                { angle: 240, color: '#0000ff', label: 'B' },
                { angle: 300, color: '#ff00ff', label: 'M' }
            ];

            targets.forEach(target => {
                const angle = (target.angle - 90) * Math.PI / 180;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;

                vectorscopeCtx.fillStyle = target.color;
                vectorscopeCtx.beginPath();
                vectorscopeCtx.arc(x, y, 3, 0, 2 * Math.PI);
                vectorscopeCtx.fill();
            });

            // Plot image data
            const data = currentImageData.data;

            // Apply sensitivity and zoom settings
            const sensitivity = scopeSettings.sensitivity / 50;
            const zoom = scopeSettings.zoom / 100;
            const alpha = Math.min(0.5, 0.05 + sensitivity * 0.2);
            const step = Math.max(4, Math.round(16 / zoom)); // More samples when zoomed
            const effectiveRadius = radius * zoom;

            vectorscopeCtx.fillStyle = `rgba(255, 255, 255, ${alpha})`;

            for (let i = 0; i < data.length; i += step) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                // Convert to U and V components
                const u = -0.147 * r - 0.289 * g + 0.436 * b;
                const v = 0.615 * r - 0.515 * g - 0.100 * b;

                const x = centerX + (u / 128) * effectiveRadius;
                const y = centerY - (v / 128) * effectiveRadius;

                if (x >= 0 && x < vectorscopeCanvas.width && y >= 0 && y < vectorscopeCanvas.height) {
                    const pointSize = Math.max(1, Math.round(zoom));
                    vectorscopeCtx.fillRect(x, y, pointSize, pointSize);
                }
            }
        }

        function updateRGBParade() {
            if (!rgbParadeCtx || !currentImageData) return;

            rgbParadeCtx.fillStyle = '#1a1a1a';
            rgbParadeCtx.fillRect(0, 0, rgbParadeCanvas.width, rgbParadeCanvas.height);

            const data = currentImageData.data;
            const width = canvas.width;
            const height = canvas.height;
            const paradeWidth = rgbParadeCanvas.width / 3;

            // Draw RGB channels separately
            const channels = [
                { index: 0, color: 'rgba(255, 0, 0, 0.3)', offset: 0 },
                { index: 1, color: 'rgba(0, 255, 0, 0.3)', offset: paradeWidth },
                { index: 2, color: 'rgba(0, 0, 255, 0.3)', offset: paradeWidth * 2 }
            ];

            // Apply sensitivity and zoom settings
            const sensitivity = scopeSettings.sensitivity / 50;
            const zoom = scopeSettings.zoom / 100;
            const step = Math.max(1, Math.round(3 / zoom));

            channels.forEach(channel => {
                // Adjust alpha based on sensitivity
                const baseAlpha = 0.3;
                const alpha = Math.min(0.8, baseAlpha + sensitivity * 0.2);
                const adjustedColor = channel.color.replace('0.3', alpha.toString());
                rgbParadeCtx.fillStyle = adjustedColor;

                for (let x = 0; x < width; x += step) {
                    for (let y = 0; y < height; y += step) {
                        const index = (y * width + x) * 4 + channel.index;
                        const value = data[index];

                        const paradeX = channel.offset + (x / width) * paradeWidth;
                        const paradeY = rgbParadeCanvas.height - (value / 255) * rgbParadeCanvas.height * zoom;

                        const pointSize = Math.max(1, Math.round(zoom));
                        rgbParadeCtx.fillRect(paradeX, paradeY, pointSize, pointSize);
                    }
                }
            });
        }

        function setScopeMode(scope, mode) {
            if (scope === 'histogram') {
                scopeSettings.histogramMode = mode;

                // Update button states
                ['rgb', 'red', 'green', 'blue'].forEach(m => {
                    const btn = document.getElementById(`hist${m.charAt(0).toUpperCase() + m.slice(1)}Btn`);
                    if (btn) {
                        if (m === mode) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    }
                });

                updateHistogram();
            }
        }

        // Auto analysis with multiple modes
        function autoAnalyze() {
            console.log('📈 Auto analyzing...');

            if (!originalImageData) {
                console.log('❌ No image to analyze');
                return;
            }

            autoMode = (autoMode + 1) % 3;

            switch(autoMode) {
                case 0:
                    performExposureAnalysis();
                    console.log('✅ Exposure analysis completed');
                    break;
                case 1:
                    performColorAnalysis();
                    console.log('✅ Color analysis completed');
                    break;
                case 2:
                    performQualityAnalysis();
                    console.log('✅ Quality analysis completed');
                    break;
            }

            updateAutoButtonText();
        }

        function performExposureAnalysis() {
            const data = currentImageData.data;
            let underexposed = 0;
            let overexposed = 0;
            let totalPixels = data.length / 4;

            for (let i = 0; i < data.length; i += 4) {
                const luminance = (data[i] + data[i + 1] + data[i + 2]) / 3;
                if (luminance < 16) underexposed++;
                if (luminance > 240) overexposed++;
            }

            const underPercent = (underexposed / totalPixels * 100).toFixed(1);
            const overPercent = (overexposed / totalPixels * 100).toFixed(1);

            const analysis = `
                <div style="font-weight: bold; color: #00d4ff;">Analýza expozice:</div>
                <div>• Podexponované: ${underPercent}%</div>
                <div>• Přeexponované: ${overPercent}%</div>
                <div style="margin-top: 5px; font-size: 10px;">
                    ${underPercent > 5 ? '⚠️ Zvyšte expozici' : overPercent > 5 ? '⚠️ Snižte expozici' : '✅ Expozice OK'}
                </div>
            `;

            document.getElementById('qualityInfo').innerHTML = analysis;
        }

        function performColorAnalysis() {
            const data = currentImageData.data;
            let totalR = 0, totalG = 0, totalB = 0;
            let totalPixels = data.length / 4;

            for (let i = 0; i < data.length; i += 4) {
                totalR += data[i];
                totalG += data[i + 1];
                totalB += data[i + 2];
            }

            const avgR = Math.round(totalR / totalPixels);
            const avgG = Math.round(totalG / totalPixels);
            const avgB = Math.round(totalB / totalPixels);

            const colorCast = avgR > avgG + 10 ? 'Teplý' : avgB > avgG + 10 ? 'Chladný' : 'Neutrální';

            const analysis = `
                <div style="font-weight: bold; color: #00d4ff;">Analýza barev:</div>
                <div>• Průměr RGB: ${avgR}, ${avgG}, ${avgB}</div>
                <div>• Barevný nádech: ${colorCast}</div>
                <div style="margin-top: 5px; font-size: 10px;">
                    ${colorCast !== 'Neutrální' ? '⚠️ Upravte white balance' : '✅ Barvy OK'}
                </div>
            `;

            document.getElementById('qualityInfo').innerHTML = analysis;
        }

        function performQualityAnalysis() {
            const data = currentImageData.data;
            let sharpness = 0;
            let contrast = 0;
            let samples = 0;

            // Simple sharpness detection
            for (let y = 1; y < canvas.height - 1; y += 10) {
                for (let x = 1; x < canvas.width - 1; x += 10) {
                    const index = (y * canvas.width + x) * 4;
                    const center = (data[index] + data[index + 1] + data[index + 2]) / 3;

                    const neighbors = [
                        ((y-1) * canvas.width + x) * 4,
                        ((y+1) * canvas.width + x) * 4,
                        (y * canvas.width + (x-1)) * 4,
                        (y * canvas.width + (x+1)) * 4
                    ];

                    let maxDiff = 0;
                    neighbors.forEach(nIndex => {
                        const neighbor = (data[nIndex] + data[nIndex + 1] + data[nIndex + 2]) / 3;
                        maxDiff = Math.max(maxDiff, Math.abs(center - neighbor));
                    });

                    sharpness += maxDiff;
                    samples++;
                }
            }

            sharpness = Math.round(sharpness / samples);
            const quality = sharpness > 20 ? 'Vysoká' : sharpness > 10 ? 'Střední' : 'Nízká';

            const analysis = `
                <div style="font-weight: bold; color: #00d4ff;">Analýza kvality:</div>
                <div>• Ostrost: ${quality} (${sharpness})</div>
                <div>• Rozlišení: ${canvas.width}×${canvas.height}</div>
                <div style="margin-top: 5px; font-size: 10px;">
                    ${quality === 'Nízká' ? '⚠️ Zvyšte ostrost' : '✅ Kvalita OK'}
                </div>
            `;

            document.getElementById('qualityInfo').innerHTML = analysis;
        }

        function updateAutoButtonText() {
            const button = document.getElementById('autoButton');
            if (!button) return;

            const modes = [
                { text: '📊 Auto: Expozice', color: 'linear-gradient(135deg, #28a745, #20c997)' },
                { text: '🎨 Auto: Barvy', color: 'linear-gradient(135deg, #007bff, #0056b3)' },
                { text: '🔍 Auto: Kvalita', color: 'linear-gradient(135deg, #dc3545, #c82333)' }
            ];

            const currentMode = modes[autoMode];
            button.textContent = currentMode.text;
            button.style.background = currentMode.color;
        }

        function analyzeImage() {
            if (!currentImageData) return;

            // Perform initial analysis
            performExposureAnalysis();
        }

        // Export functions
        function exportHistogramData() {
            if (!currentImageData) {
                alert('Nejdříve nahrajte obrázek!');
                return;
            }

            const data = currentImageData.data;
            const histogram = new Array(256).fill(0);

            for (let i = 0; i < data.length; i += 4) {
                const brightness = Math.round((data[i] + data[i + 1] + data[i + 2]) / 3);
                histogram[brightness]++;
            }

            const csvData = histogram.map((value, index) => `${index},${value}`).join('\n');
            const blob = new Blob(['Brightness,Count\n' + csvData], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `histogram_data_${Date.now()}.csv`;
            link.click();

            URL.revokeObjectURL(url);
            console.log('📊 Histogram data exported');
        }

        function exportScopeImage() {
            if (!currentImageData) {
                alert('Nejdříve nahrajte obrázek!');
                return;
            }

            const exportCanvas = document.createElement('canvas');
            const exportCtx = exportCanvas.getContext('2d');

            exportCanvas.width = 600;
            exportCanvas.height = 600;

            // Draw all scopes in one image
            exportCtx.drawImage(histogramCanvas, 0, 0, 300, 300);
            exportCtx.drawImage(waveformCanvas, 300, 0, 300, 300);
            exportCtx.drawImage(vectorscopeCanvas, 0, 300, 300, 300);
            exportCtx.drawImage(rgbParadeCanvas, 300, 300, 300, 300);

            const link = document.createElement('a');
            link.download = `scopes_${Date.now()}.png`;
            link.href = exportCanvas.toDataURL('image/png');
            link.click();

            console.log('📸 Scope image exported');
        }

        function generateReport() {
            if (!currentImageData) {
                alert('Nejdříve nahrajte obrázek!');
                return;
            }

            const report = `
IMAGE ANALYSIS REPORT
Generated: ${new Date().toLocaleString()}

Image Information:
- Resolution: ${canvas.width} × ${canvas.height}
- Total Pixels: ${(canvas.width * canvas.height).toLocaleString()}

${document.getElementById('qualityInfo').textContent}

Scope Settings:
- Sensitivity: ${scopeSettings.sensitivity}
- Zoom: ${scopeSettings.zoom}%
- Histogram Mode: ${scopeSettings.histogramMode.toUpperCase()}
            `.trim();

            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `image_analysis_report_${Date.now()}.txt`;
            link.click();

            URL.revokeObjectURL(url);
            console.log('📋 Report generated');
        }

        function resetScopes() {
            scopeSettings.sensitivity = 50;
            scopeSettings.zoom = 100;
            scopeSettings.histogramMode = 'rgb';

            document.getElementById('sensitivityValue').textContent = '50';
            document.getElementById('zoomValue').textContent = '100';
            document.getElementById('sensitivity').value = 50;
            document.getElementById('zoom').value = 100;

            setScopeMode('histogram', 'rgb');
            updateAllScopes();

            console.log('🔄 Scopes reset');
        }

        function hideUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }
        }

        // Help System Functions
        function toggleHelp() {
            helpMode = !helpMode;
            const helpToggle = document.getElementById('helpToggle');

            if (helpMode) {
                helpToggle.classList.add('active');
                helpToggle.textContent = '✕';
                helpToggle.title = 'Vypnout nápovědu';
                setupHelpListeners();
                showWelcomeTooltip();
            } else {
                helpToggle.classList.remove('active');
                helpToggle.textContent = '?';
                helpToggle.title = 'Zapnout nápovědu';
                removeHelpListeners();
                hideTooltip();
            }

            console.log('🔧 Help mode:', helpMode ? 'ON' : 'OFF');
        }

        function setupHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.addEventListener('mouseenter', showTooltip);
                element.addEventListener('mouseleave', hideTooltip);
            });
        }

        function removeHelpListeners() {
            const helpElements = document.querySelectorAll('[data-help]');
            helpElements.forEach(element => {
                element.removeEventListener('mouseenter', showTooltip);
                element.removeEventListener('mouseleave', hideTooltip);
                element.classList.remove('help-active');
            });
        }

        function showTooltip(event) {
            if (!helpMode) return;

            const element = event.currentTarget;
            const helpText = element.getAttribute('data-help');
            if (!helpText) return;

            const [title, description] = helpText.split('|');

            hideTooltip();

            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">${title}</div>
                <div>${description}</div>
            `;

            const rect = element.getBoundingClientRect();

            let left = rect.left;
            let top = rect.bottom + 10;

            if (left + 280 > window.innerWidth) {
                left = window.innerWidth - 280 - 20;
            }

            if (top + 80 > window.innerHeight) {
                top = rect.top - 80 - 10;
            }

            if (left < 10) left = 10;
            if (top < 10) top = rect.bottom + 10;

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            document.body.appendChild(tooltip);
            currentTooltip = tooltip;

            element.classList.add('help-active');
        }

        function hideTooltip() {
            if (currentTooltip) {
                currentTooltip.remove();
                currentTooltip = null;
            }

            const activeElements = document.querySelectorAll('.help-active');
            activeElements.forEach(el => el.classList.remove('help-active'));
        }

        function showWelcomeTooltip() {
            const helpToggle = document.getElementById('helpToggle');
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.innerHTML = `
                <div class="tooltip-title">Histogram & Scopes nápověda! 📊</div>
                <div>Najeďte myší na jakýkoliv prvek pro zobrazení nápovědy o analýze obrazu.</div>
            `;

            const rect = helpToggle.getBoundingClientRect();
            tooltip.style.left = (rect.left - 200) + 'px';
            tooltip.style.top = (rect.bottom + 10) + 'px';

            document.body.appendChild(tooltip);

            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 3000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded');
            if (initializeApp()) {
                console.log('✅ Histogram & Scopes app initialized successfully');
            } else {
                console.log('❌ Histogram & Scopes app initialization failed');
            }
        });
    </script>
</body>
</html>
