<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Canvas</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }
        
        #testCanvas {
            border: 2px solid #00d4ff;
            background: #000;
            margin: 20px 0;
            max-width: 100%;
        }
        
        .upload-area {
            border: 2px dashed #00d4ff;
            padding: 30px;
            margin: 20px 0;
            cursor: pointer;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .upload-area:hover {
            background: rgba(0, 212, 255, 0.2);
        }
        
        button {
            background: #00d4ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0099cc;
        }
        
        .log {
            background: #333;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: left;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .control-group {
            margin: 10px 0;
            display: inline-block;
            width: 200px;
            text-align: left;
        }
        
        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Canvas Test</h1>
        
        <div>
            <button onclick="drawTest()">🎨 Nakreslit test</button>
            <button onclick="clearCanvas()">🗑️ Vymazat</button>
            <button onclick="runDiagnostic()">🔍 Diagnostika</button>
        </div>
        
        <canvas id="testCanvas" width="800" height="400"></canvas>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📁 Klikněte pro nahrání obrázku</h3>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Jas: <span id="brightnessValue">0</span></label>
                <input type="range" id="brightness" min="-100" max="100" value="0" oninput="updateBrightness(this.value)">
            </div>
            
            <div class="control-group">
                <label>Kontrast: <span id="contrastValue">0</span></label>
                <input type="range" id="contrast" min="-100" max="100" value="0" oninput="updateContrast(this.value)">
            </div>
        </div>
        
        <div class="log" id="log">
            <strong>Log zpráv:</strong><br>
        </div>
    </div>

    <script>
        let canvas, ctx, originalImageData;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `${time}: ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function init() {
            log('🚀 Inicializace...');
            
            canvas = document.getElementById('testCanvas');
            if (!canvas) {
                log('❌ Canvas nenalezen!');
                return false;
            }
            log('✅ Canvas nalezen');
            
            ctx = canvas.getContext('2d');
            if (!ctx) {
                log('❌ Context nenalezen!');
                return false;
            }
            log('✅ Context nalezen');
            
            // Setup file input
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFile);
            log('✅ File input nastaven');
            
            // Draw initial test
            drawTest();
            
            return true;
        }
        
        function drawTest() {
            log('🎨 Kreslím testovací obrázek...');
            
            if (!canvas || !ctx) {
                log('❌ Canvas nebo context není dostupný');
                return;
            }
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#ff0000');
            gradient.addColorStop(0.5, '#00ff00');
            gradient.addColorStop(1, '#0000ff');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw shapes
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(50, 50, 100, 100);
            
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.arc(canvas.width - 100, 100, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 30px Arial';
            ctx.fillText('TEST CANVAS', canvas.width/2 - 100, canvas.height/2);
            
            // Store original
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            log('✅ Testovací obrázek nakreslen');
        }
        
        function clearCanvas() {
            if (!ctx) return;
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            log('🗑️ Canvas vymazán');
        }
        
        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            log(`📁 Načítám soubor: ${file.name}`);
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    log(`🖼️ Obrázek načten: ${img.width}x${img.height}`);
                    
                    // Clear canvas
                    ctx.fillStyle = '#000';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // Calculate size
                    const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
                    const width = img.width * scale;
                    const height = img.height * scale;
                    const x = (canvas.width - width) / 2;
                    const y = (canvas.height - height) / 2;
                    
                    // Draw image
                    ctx.drawImage(img, x, y, width, height);
                    
                    // Store original
                    originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    
                    log('✅ Obrázek vykreslen');
                };
                img.onerror = function() {
                    log('❌ Chyba při načítání obrázku');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                log('❌ Chyba při čtení souboru');
            };
            reader.readAsDataURL(file);
        }
        
        function updateBrightness(value) {
            document.getElementById('brightnessValue').textContent = value;
            applyFilters();
        }
        
        function updateContrast(value) {
            document.getElementById('contrastValue').textContent = value;
            applyFilters();
        }
        
        function applyFilters() {
            if (!originalImageData) return;
            
            const brightness = parseInt(document.getElementById('brightness').value);
            const contrast = parseInt(document.getElementById('contrast').value);
            
            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;
            
            for (let i = 0; i < data.length; i += 4) {
                // Apply brightness
                let r = originalData[i] + brightness;
                let g = originalData[i + 1] + brightness;
                let b = originalData[i + 2] + brightness;
                
                // Apply contrast
                const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
                r = factor * (r - 128) + 128;
                g = factor * (g - 128) + 128;
                b = factor * (b - 128) + 128;
                
                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
                data[i + 3] = originalData[i + 3];
            }
            
            ctx.putImageData(imageData, 0, 0);
        }
        
        function runDiagnostic() {
            log('🔍 Spouštím diagnostiku...');
            log(`Canvas element: ${canvas ? 'OK' : 'CHYBA'}`);
            log(`Canvas context: ${ctx ? 'OK' : 'CHYBA'}`);
            log(`Canvas rozměry: ${canvas ? canvas.width + 'x' + canvas.height : 'N/A'}`);
            log(`Canvas viditelný: ${canvas && canvas.offsetWidth > 0 ? 'ANO' : 'NE'}`);
            log(`Original data: ${originalImageData ? 'OK' : 'CHYBA'}`);
            
            if (canvas) {
                const rect = canvas.getBoundingClientRect();
                log(`Canvas pozice: ${rect.left}, ${rect.top}, ${rect.width}x${rect.height}`);
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 DOM načten');
            if (init()) {
                log('✅ Inicializace úspěšná');
            } else {
                log('❌ Inicializace selhala');
            }
        });
    </script>
</body>
</html>
