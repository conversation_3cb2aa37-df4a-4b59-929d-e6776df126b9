<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Scopes & Color Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0a0a;
            color: #e8e8e8;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            overflow: hidden;
            user-select: none;
        }
        
        .pro-workspace {
            display: grid;
            grid-template-columns: 320px 1fr 380px;
            grid-template-rows: 40px 1fr 200px;
            grid-template-areas: 
                "toolbar toolbar toolbar"
                "controls canvas scopes"
                "timeline timeline timeline";
            height: 100vh;
            background: #0a0a0a;
        }
        
        .pro-toolbar {
            grid-area: toolbar;
            background: linear-gradient(180deg, #1e1e1e 0%, #161616 100%);
            border-bottom: 1px solid #2a2a2a;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 20px;
        }
        
        .toolbar-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .toolbar-button {
            background: #2a2a2a;
            border: 1px solid #3a3a3a;
            color: #e8e8e8;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.15s ease;
        }
        
        .toolbar-button:hover {
            background: #3a3a3a;
            border-color: #4a4a4a;
        }
        
        .toolbar-button.active {
            background: #0066cc;
            border-color: #0077dd;
            color: white;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #141414;
            border-right: 1px solid #2a2a2a;
            overflow-y: auto;
            padding: 0;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #0a0a0a;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #1a1a1a;
        }
        
        .scopes-panel {
            grid-area: scopes;
            background: #141414;
            border-left: 1px solid #2a2a2a;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 1px;
            background: #2a2a2a;
        }
        
        .timeline-panel {
            grid-area: timeline;
            background: #161616;
            border-top: 1px solid #2a2a2a;
            padding: 15px;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background: #000;
            border: 1px solid #2a2a2a;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }
        
        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 2px dashed #3a3a3a;
            border-radius: 8px;
            padding: 60px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(20, 20, 20, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .upload-zone:hover {
            border-color: #0066cc;
            background: rgba(0, 102, 204, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }
        
        .upload-text {
            font-size: 14px;
            color: #888;
            font-weight: 500;
        }
        
        .scope-container {
            background: #0a0a0a;
            position: relative;
            border: 1px solid #1a1a1a;
        }
        
        .scope-header {
            position: absolute;
            top: 8px;
            left: 12px;
            font-size: 11px;
            font-weight: 600;
            color: #888;
            z-index: 10;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .scope-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .control-section {
            border-bottom: 1px solid #2a2a2a;
            background: #141414;
        }
        
        .section-header {
            background: #1a1a1a;
            padding: 12px 16px;
            font-size: 11px;
            font-weight: 600;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #2a2a2a;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-content {
            padding: 16px;
        }
        
        .color-wheels-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .color-wheel-group {
            text-align: center;
        }
        
        .color-wheel-label {
            font-size: 10px;
            color: #888;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .color-wheel {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 2px solid #2a2a2a;
            cursor: crosshair;
            margin: 0 auto 8px;
            position: relative;
            background: conic-gradient(from 0deg, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000);
        }
        
        .color-wheel::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: white;
            border: 2px solid #000;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        
        .wheel-reset {
            background: #2a2a2a;
            border: 1px solid #3a3a3a;
            color: #888;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 9px;
            cursor: pointer;
        }
        
        .wheel-reset:hover {
            background: #3a3a3a;
            color: #ccc;
        }
        
        .pro-slider-group {
            margin-bottom: 16px;
        }
        
        .slider-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            font-size: 11px;
            color: #ccc;
        }
        
        .slider-value {
            color: #0066cc;
            font-weight: 600;
            font-family: 'SF Mono', Monaco, monospace;
            min-width: 40px;
            text-align: right;
        }
        
        .pro-slider {
            width: 100%;
            height: 4px;
            background: #2a2a2a;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            position: relative;
        }
        
        .pro-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #0066cc;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,102,204,0.3);
            border: 2px solid #fff;
        }
        
        .pro-slider::-webkit-slider-track {
            background: #2a2a2a;
            height: 4px;
            border-radius: 2px;
        }
        
        .pixel-info {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 16px;
        }
        
        .pixel-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .pixel-value {
            background: #0a0a0a;
            padding: 6px;
            border-radius: 4px;
            text-align: center;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 10px;
        }
        
        .pixel-value.red { border-left: 3px solid #ff4444; }
        .pixel-value.green { border-left: 3px solid #44ff44; }
        .pixel-value.blue { border-left: 3px solid #4444ff; }
        .pixel-value.luma { border-left: 3px solid #888; }
        
        .analysis-panel {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 6px;
            padding: 12px;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .analysis-title {
            color: #0066cc;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .analysis-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .analysis-value {
            font-family: 'SF Mono', Monaco, monospace;
            color: #ccc;
        }
        
        .timeline-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .timeline-button {
            background: #2a2a2a;
            border: 1px solid #3a3a3a;
            color: #e8e8e8;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.15s ease;
        }
        
        .timeline-button:hover {
            background: #3a3a3a;
        }
        
        .timeline-button.primary {
            background: #0066cc;
            border-color: #0077dd;
        }
        
        .timeline-button.primary:hover {
            background: #0077dd;
        }
        
        /* Collapsible sections */
        .section-content.collapsed {
            display: none;
        }
        
        .section-header .chevron {
            transition: transform 0.2s ease;
        }
        
        .section-header.collapsed .chevron {
            transform: rotate(-90deg);
        }
        
        /* Professional tooltips */
        .pro-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            color: #fff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.3;
            max-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            border: 1px solid #3a3a3a;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .pro-tooltip.show {
            opacity: 1;
            visibility: visible;
        }
        
        /* Responsive design */
        @media (max-width: 1400px) {
            .pro-workspace {
                grid-template-columns: 280px 1fr 320px;
            }
        }
        
        @media (max-width: 1200px) {
            .color-wheels-container {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .color-wheel {
                width: 60px;
                height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="pro-workspace">
        <!-- Professional Toolbar -->
        <div class="pro-toolbar">
            <div class="toolbar-section">
                <div style="font-weight: 600; color: #0066cc;">SCOPES & ANALYSIS</div>
            </div>
            
            <div class="toolbar-section">
                <button class="toolbar-button" onclick="document.getElementById('fileInput').click()">Import</button>
                <button class="toolbar-button" onclick="loadTestImage()">Test Pattern</button>
                <button class="toolbar-button" onclick="resetAll()">Reset</button>
            </div>
            
            <div class="toolbar-section">
                <button class="toolbar-button active" onclick="toggleScope('histogram')">Histogram</button>
                <button class="toolbar-button active" onclick="toggleScope('waveform')">Waveform</button>
                <button class="toolbar-button active" onclick="toggleScope('vectorscope')">Vectorscope</button>
                <button class="toolbar-button active" onclick="toggleScope('parade')">RGB Parade</button>
            </div>
            
            <div class="toolbar-section" style="margin-left: auto;">
                <button class="toolbar-button" onclick="exportData()">Export Data</button>
                <button class="toolbar-button" onclick="generateReport()">Report</button>
            </div>
            
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <!-- Professional Controls Panel -->
        <div class="controls-panel">
            <!-- Lift Gamma Gain Section -->
            <div class="control-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span>Lift • Gamma • Gain</span>
                    <span class="chevron">▼</span>
                </div>
                <div class="section-content">
                    <div class="color-wheels-container">
                        <div class="color-wheel-group">
                            <div class="color-wheel-label">Lift</div>
                            <div class="color-wheel" id="liftWheel" data-type="lift"></div>
                            <button class="wheel-reset" onclick="resetWheel('lift')">Reset</button>
                        </div>
                        <div class="color-wheel-group">
                            <div class="color-wheel-label">Gamma</div>
                            <div class="color-wheel" id="gammaWheel" data-type="gamma"></div>
                            <button class="wheel-reset" onclick="resetWheel('gamma')">Reset</button>
                        </div>
                        <div class="color-wheel-group">
                            <div class="color-wheel-label">Gain</div>
                            <div class="color-wheel" id="gainWheel" data-type="gain"></div>
                            <button class="wheel-reset" onclick="resetWheel('gain')">Reset</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Primary Corrections -->
            <div class="control-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span>Primary Corrections</span>
                    <span class="chevron">▼</span>
                </div>
                <div class="section-content">
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Exposure</span>
                            <span class="slider-value" id="exposureValue">0.00</span>
                        </div>
                        <input type="range" class="pro-slider" id="exposure" min="-3" max="3" step="0.01" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Contrast</span>
                            <span class="slider-value" id="contrastValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="contrast" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Highlights</span>
                            <span class="slider-value" id="highlightsValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="highlights" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Shadows</span>
                            <span class="slider-value" id="shadowsValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="shadows" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Whites</span>
                            <span class="slider-value" id="whitesValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="whites" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Blacks</span>
                            <span class="slider-value" id="blacksValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="blacks" min="-100" max="100" value="0">
                    </div>
                </div>
            </div>

            <!-- Color Grading -->
            <div class="control-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span>Color Grading</span>
                    <span class="chevron">▼</span>
                </div>
                <div class="section-content">
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Temperature</span>
                            <span class="slider-value" id="temperatureValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="temperature" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Tint</span>
                            <span class="slider-value" id="tintValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="tint" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Vibrance</span>
                            <span class="slider-value" id="vibranceValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="vibrance" min="-100" max="100" value="0">
                    </div>
                    
                    <div class="pro-slider-group">
                        <div class="slider-label">
                            <span>Saturation</span>
                            <span class="slider-value" id="saturationValue">0</span>
                        </div>
                        <input type="range" class="pro-slider" id="saturation" min="-100" max="100" value="0">
                    </div>
                </div>
            </div>

            <!-- Pixel Inspector -->
            <div class="control-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span>Pixel Inspector</span>
                    <span class="chevron">▼</span>
                </div>
                <div class="section-content">
                    <div class="pixel-info">
                        <div class="pixel-grid">
                            <div class="pixel-value red">R: <span id="redValue">-</span></div>
                            <div class="pixel-value green">G: <span id="greenValue">-</span></div>
                            <div class="pixel-value blue">B: <span id="blueValue">-</span></div>
                            <div class="pixel-value luma">L: <span id="lumaValue">-</span></div>
                        </div>
                        <div style="text-align: center; font-size: 10px; color: #666;">
                            Hover over image for pixel data
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="mainCanvas" width="1200" height="800"></canvas>
            
            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📊</div>
                <div class="upload-text">Drop image here or click to browse</div>
            </div>
        </div>

        <!-- Professional Scopes Panel -->
        <div class="scopes-panel">
            <div class="scope-container" id="histogramScope">
                <div class="scope-header">Histogram</div>
                <canvas class="scope-canvas" id="histogramCanvas" width="380" height="200"></canvas>
            </div>
            
            <div class="scope-container" id="waveformScope">
                <div class="scope-header">Waveform</div>
                <canvas class="scope-canvas" id="waveformCanvas" width="380" height="200"></canvas>
            </div>
            
            <div class="scope-container" id="vectorscopeScope">
                <div class="scope-header">Vectorscope</div>
                <canvas class="scope-canvas" id="vectorscopeCanvas" width="380" height="200"></canvas>
            </div>
            
            <div class="scope-container" id="paradeScope">
                <div class="scope-header">RGB Parade</div>
                <canvas class="scope-canvas" id="paradeCanvas" width="380" height="200"></canvas>
            </div>
        </div>

        <!-- Timeline/Analysis Panel -->
        <div class="timeline-panel">
            <div class="analysis-panel">
                <div class="analysis-title">Image Analysis</div>
                <div id="analysisContent">
                    <div class="analysis-item">
                        <span>Dynamic Range:</span>
                        <span class="analysis-value" id="dynamicRange">-</span>
                    </div>
                    <div class="analysis-item">
                        <span>Avg Luminance:</span>
                        <span class="analysis-value" id="avgLuminance">-</span>
                    </div>
                    <div class="analysis-item">
                        <span>Color Temperature:</span>
                        <span class="analysis-value" id="colorTemp">-</span>
                    </div>
                    <div class="analysis-item">
                        <span>Saturation Index:</span>
                        <span class="analysis-value" id="satIndex">-</span>
                    </div>
                </div>
            </div>
            
            <div class="timeline-controls">
                <button class="timeline-button primary" onclick="autoAnalyze()">Auto Analyze</button>
                <button class="timeline-button" onclick="autoCorrect()">Auto Correct</button>
                <button class="timeline-button" onclick="exportImage()">Export Image</button>
                <button class="timeline-button" onclick="savePreset()">Save Preset</button>
                <button class="timeline-button" onclick="loadPreset()">Load Preset</button>
            </div>
        </div>
    </div>

    <script>
        // Professional-grade global variables
        let canvas, ctx;
        let histogramCanvas, histogramCtx;
        let waveformCanvas, waveformCtx;
        let vectorscopeCanvas, vectorscopeCtx;
        let paradeCanvas, paradeCtx;
        let originalImageData, currentImageData;
        
        // Professional color grading parameters
        const colorGrading = {
            // Lift Gamma Gain
            lift: { r: 0, g: 0, b: 0, master: 0 },
            gamma: { r: 1, g: 1, b: 1, master: 1 },
            gain: { r: 1, g: 1, b: 1, master: 1 },
            
            // Primary corrections
            exposure: 0,
            contrast: 0,
            highlights: 0,
            shadows: 0,
            whites: 0,
            blacks: 0,
            
            // Color
            temperature: 0,
            tint: 0,
            vibrance: 0,
            saturation: 0
        };
        
        const scopeSettings = {
            histogram: { enabled: true, mode: 'rgb' },
            waveform: { enabled: true, scale: 1.0 },
            vectorscope: { enabled: true, scale: 1.0 },
            parade: { enabled: true, scale: 1.0 }
        };

        // Professional initialization
        function initializeProfessionalWorkspace() {
            console.log('🎬 Initializing Professional Workspace...');

            // Get all canvas elements
            canvas = document.getElementById('mainCanvas');
            histogramCanvas = document.getElementById('histogramCanvas');
            waveformCanvas = document.getElementById('waveformCanvas');
            vectorscopeCanvas = document.getElementById('vectorscopeCanvas');
            paradeCanvas = document.getElementById('paradeCanvas');

            if (!canvas || !histogramCanvas || !waveformCanvas || !vectorscopeCanvas || !paradeCanvas) {
                console.error('❌ Canvas elements not found!');
                return false;
            }

            // Get contexts
            ctx = canvas.getContext('2d');
            histogramCtx = histogramCanvas.getContext('2d');
            waveformCtx = waveformCanvas.getContext('2d');
            vectorscopeCtx = vectorscopeCanvas.getContext('2d');
            paradeCtx = paradeCanvas.getContext('2d');

            // Setup professional event listeners
            setupProfessionalControls();
            setupColorWheels();
            setupCanvasInteraction();

            // Load professional test pattern
            loadProfessionalTestPattern();

            console.log('✅ Professional workspace initialized');
            return true;
        }

        function setupProfessionalControls() {
            // File handling
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelect);
            }

            // Drag & drop
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('drop', handleDrop);
                uploadZone.addEventListener('dragleave', (e) => {
                    e.currentTarget.style.background = '';
                });
            }

            // Professional sliders
            const sliders = [
                'exposure', 'contrast', 'highlights', 'shadows', 'whites', 'blacks',
                'temperature', 'tint', 'vibrance', 'saturation'
            ];

            sliders.forEach(sliderId => {
                const slider = document.getElementById(sliderId);
                if (slider) {
                    slider.addEventListener('input', (e) => {
                        updateColorGrading(sliderId, parseFloat(e.target.value));
                    });

                    // Double-click to reset
                    slider.addEventListener('dblclick', () => {
                        slider.value = 0;
                        updateColorGrading(sliderId, 0);
                    });
                }
            });
        }

        function setupColorWheels() {
            const wheels = ['liftWheel', 'gammaWheel', 'gainWheel'];

            wheels.forEach(wheelId => {
                const wheel = document.getElementById(wheelId);
                if (wheel) {
                    const type = wheel.dataset.type;

                    wheel.addEventListener('mousedown', (e) => startWheelDrag(e, type));
                    wheel.addEventListener('mousemove', (e) => updateWheelDrag(e, type));
                    wheel.addEventListener('mouseup', () => endWheelDrag());
                    wheel.addEventListener('mouseleave', () => endWheelDrag());

                    // Double-click to reset
                    wheel.addEventListener('dblclick', () => resetWheel(type));

                    drawColorWheel(wheel, type);
                }
            });
        }

        function setupCanvasInteraction() {
            // Pixel inspector
            canvas.addEventListener('mousemove', updatePixelInspector);
            canvas.addEventListener('mouseleave', clearPixelInspector);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('📁 Loading professional image:', file.name);
                loadImageFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.background = 'rgba(0, 102, 204, 0.2)';
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.background = '';
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                console.log('📁 Loading dropped file:', files[0].name);
                loadImageFile(files[0]);
            }
        }

        function loadImageFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    console.log('🖼️ Professional image loaded:', img.width, 'x', img.height);
                    drawImageToCanvas(img);
                };
                img.onerror = function() {
                    console.error('❌ Error loading image');
                    alert('Error loading image. Please try a different format.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                console.error('❌ Error reading file');
                alert('Error reading file.');
            };
            reader.readAsDataURL(file);
        }

        function drawImageToCanvas(img) {
            const maxWidth = canvas.width;
            const maxHeight = canvas.height;
            let { width, height } = img;

            // Calculate optimal size maintaining aspect ratio
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;

            const x = (maxWidth - width) / 2;
            const y = (maxHeight - height) / 2;

            // Clear with professional black
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, maxWidth, maxHeight);

            // Draw image with high quality
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(img, x, y, width, height);

            // Store image data
            originalImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);
            currentImageData = ctx.getImageData(0, 0, maxWidth, maxHeight);

            hideUploadZone();
            updateAllScopes();
            performProfessionalAnalysis();

            console.log('✅ Professional image processing complete');
        }

        function loadProfessionalTestPattern() {
            console.log('🎨 Loading professional test pattern...');

            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Professional color bars (SMPTE standard)
            const colorBars = [
                '#c0c0c0', // 75% White
                '#c0c000', // 75% Yellow
                '#00c0c0', // 75% Cyan
                '#00c000', // 75% Green
                '#c000c0', // 75% Magenta
                '#c00000', // 75% Red
                '#0000c0'  // 75% Blue
            ];

            const barWidth = canvas.width / colorBars.length;
            colorBars.forEach((color, index) => {
                ctx.fillStyle = color;
                ctx.fillRect(index * barWidth, 0, barWidth, canvas.height * 0.6);
            });

            // Professional grayscale ramp
            const steps = 16;
            const stepWidth = canvas.width / steps;
            for (let i = 0; i < steps; i++) {
                const gray = Math.round((i / (steps - 1)) * 255);
                ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                ctx.fillRect(i * stepWidth, canvas.height * 0.6, stepWidth, canvas.height * 0.2);
            }

            // Professional skin tone patches
            const skinTones = ['#f4c2a1', '#e8b896', '#d4a574', '#c19660', '#a67c52'];
            const patchWidth = canvas.width / skinTones.length;
            skinTones.forEach((tone, index) => {
                ctx.fillStyle = tone;
                ctx.fillRect(index * patchWidth, canvas.height * 0.8, patchWidth, canvas.height * 0.2);
            });

            // Professional labeling
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px SF Pro Display, sans-serif';
            ctx.fillText('PROFESSIONAL TEST PATTERN - SMPTE COLOR BARS', 50, 50);

            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            hideUploadZone();
            updateAllScopes();
            performProfessionalAnalysis();

            console.log('✅ Professional test pattern loaded');
        }

        // Professional color grading functions
        function updateColorGrading(parameter, value) {
            colorGrading[parameter] = value;

            // Update UI
            const valueDisplay = document.getElementById(parameter + 'Value');
            if (valueDisplay) {
                if (parameter === 'exposure') {
                    valueDisplay.textContent = value.toFixed(2);
                } else {
                    valueDisplay.textContent = Math.round(value);
                }
            }

            // Apply professional color grading
            applyProfessionalColorGrading();
            updateAllScopes();

            console.log(`🎨 ${parameter}: ${value}`);
        }

        function applyProfessionalColorGrading() {
            if (!originalImageData) return;

            const imageData = ctx.createImageData(originalImageData);
            const data = imageData.data;
            const originalData = originalImageData.data;

            for (let i = 0; i < data.length; i += 4) {
                let r = originalData[i] / 255;
                let g = originalData[i + 1] / 255;
                let b = originalData[i + 2] / 255;

                // Apply Lift Gamma Gain (professional standard)
                r = applyLiftGammaGain(r, colorGrading.lift.r, colorGrading.gamma.r, colorGrading.gain.r);
                g = applyLiftGammaGain(g, colorGrading.lift.g, colorGrading.gamma.g, colorGrading.gain.g);
                b = applyLiftGammaGain(b, colorGrading.lift.b, colorGrading.gamma.b, colorGrading.gain.b);

                // Apply exposure (stops)
                if (colorGrading.exposure !== 0) {
                    const exposureFactor = Math.pow(2, colorGrading.exposure);
                    r *= exposureFactor;
                    g *= exposureFactor;
                    b *= exposureFactor;
                }

                // Apply contrast (professional curve)
                if (colorGrading.contrast !== 0) {
                    const contrast = (colorGrading.contrast + 100) / 100;
                    r = ((r - 0.5) * contrast + 0.5);
                    g = ((g - 0.5) * contrast + 0.5);
                    b = ((b - 0.5) * contrast + 0.5);
                }

                // Apply highlights/shadows (professional tone mapping)
                const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

                if (colorGrading.highlights !== 0) {
                    const highlightMask = Math.pow(luminance, 2);
                    const highlightFactor = 1 + (colorGrading.highlights / 100) * highlightMask;
                    r *= highlightFactor;
                    g *= highlightFactor;
                    b *= highlightFactor;
                }

                if (colorGrading.shadows !== 0) {
                    const shadowMask = Math.pow(1 - luminance, 2);
                    const shadowFactor = 1 + (colorGrading.shadows / 100) * shadowMask;
                    r *= shadowFactor;
                    g *= shadowFactor;
                    b *= shadowFactor;
                }

                // Apply whites/blacks (professional levels)
                if (colorGrading.whites !== 0) {
                    const whiteFactor = 1 + (colorGrading.whites / 100) * 0.5;
                    r = Math.min(1, r * whiteFactor);
                    g = Math.min(1, g * whiteFactor);
                    b = Math.min(1, b * whiteFactor);
                }

                if (colorGrading.blacks !== 0) {
                    const blackFactor = colorGrading.blacks / 100 * 0.2;
                    r = Math.max(0, r + blackFactor);
                    g = Math.max(0, g + blackFactor);
                    b = Math.max(0, b + blackFactor);
                }

                // Apply temperature/tint (professional white balance)
                if (colorGrading.temperature !== 0 || colorGrading.tint !== 0) {
                    const tempFactor = 1 + (colorGrading.temperature / 100) * 0.3;
                    const tintFactor = 1 + (colorGrading.tint / 100) * 0.2;

                    r *= (colorGrading.temperature > 0) ? tempFactor : (1 / (1 + Math.abs(colorGrading.temperature / 100) * 0.3));
                    b *= (colorGrading.temperature < 0) ? tempFactor : (1 / (1 + Math.abs(colorGrading.temperature / 100) * 0.3));
                    g *= tintFactor;
                }

                // Apply vibrance (professional smart saturation)
                if (colorGrading.vibrance !== 0) {
                    const maxChannel = Math.max(r, g, b);
                    const minChannel = Math.min(r, g, b);
                    const saturation = maxChannel - minChannel;
                    const vibranceFactor = 1 + (colorGrading.vibrance / 100) * (1 - saturation);

                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    r = gray + vibranceFactor * (r - gray);
                    g = gray + vibranceFactor * (g - gray);
                    b = gray + vibranceFactor * (b - gray);
                }

                // Apply saturation (professional)
                if (colorGrading.saturation !== 0) {
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    const satFactor = 1 + (colorGrading.saturation / 100);
                    r = gray + satFactor * (r - gray);
                    g = gray + satFactor * (g - gray);
                    b = gray + satFactor * (b - gray);
                }

                // Clamp and convert back to 8-bit
                data[i] = Math.max(0, Math.min(255, r * 255));
                data[i + 1] = Math.max(0, Math.min(255, g * 255));
                data[i + 2] = Math.max(0, Math.min(255, b * 255));
                data[i + 3] = originalData[i + 3]; // Alpha
            }

            ctx.putImageData(imageData, 0, 0);
            currentImageData = imageData;
        }

        function applyLiftGammaGain(value, lift, gamma, gain) {
            // Professional Lift Gamma Gain formula
            value = Math.max(0, value + lift);
            value = Math.pow(value, 1 / gamma);
            value = value * gain;
            return Math.max(0, Math.min(1, value));
        }

        // Professional color wheel functions
        let isDraggingWheel = false;
        let currentWheel = null;

        function startWheelDrag(event, type) {
            isDraggingWheel = true;
            currentWheel = type;
            updateWheelValue(event, type);
        }

        function updateWheelDrag(event, type) {
            if (isDraggingWheel && currentWheel === type) {
                updateWheelValue(event, type);
            }
        }

        function endWheelDrag() {
            isDraggingWheel = false;
            currentWheel = null;
        }

        function updateWheelValue(event, type) {
            const wheel = document.getElementById(type + 'Wheel');
            const rect = wheel.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const x = event.clientX - rect.left - centerX;
            const y = event.clientY - rect.top - centerY;

            const distance = Math.sqrt(x * x + y * y);
            const maxRadius = 35; // Adjusted for 80px wheel

            if (distance <= maxRadius) {
                const angle = Math.atan2(y, x);
                const intensity = Math.min(distance / maxRadius, 1);

                // Convert to color adjustment values
                const hue = (angle + Math.PI) / (2 * Math.PI) * 360;
                const saturation = intensity;

                // Apply to lift/gamma/gain
                const adjustment = saturation * 0.2; // Professional range
                const rAdjust = Math.cos((hue - 0) * Math.PI / 180) * adjustment;
                const gAdjust = Math.cos((hue - 120) * Math.PI / 180) * adjustment;
                const bAdjust = Math.cos((hue - 240) * Math.PI / 180) * adjustment;

                if (type === 'lift') {
                    colorGrading.lift.r = rAdjust;
                    colorGrading.lift.g = gAdjust;
                    colorGrading.lift.b = bAdjust;
                } else if (type === 'gamma') {
                    colorGrading.gamma.r = 1 + rAdjust;
                    colorGrading.gamma.g = 1 + gAdjust;
                    colorGrading.gamma.b = 1 + bAdjust;
                } else if (type === 'gain') {
                    colorGrading.gain.r = 1 + rAdjust;
                    colorGrading.gain.g = 1 + gAdjust;
                    colorGrading.gain.b = 1 + bAdjust;
                }

                drawColorWheel(wheel, type);
                applyProfessionalColorGrading();
                updateAllScopes();
            }
        }

        function drawColorWheel(wheel, type) {
            // Update wheel indicator position based on current values
            const indicator = wheel.querySelector('::after') || wheel;
            // Professional wheel drawing would be implemented here
        }

        function resetWheel(type) {
            if (type === 'lift') {
                colorGrading.lift = { r: 0, g: 0, b: 0, master: 0 };
            } else if (type === 'gamma') {
                colorGrading.gamma = { r: 1, g: 1, b: 1, master: 1 };
            } else if (type === 'gain') {
                colorGrading.gain = { r: 1, g: 1, b: 1, master: 1 };
            }

            const wheel = document.getElementById(type + 'Wheel');
            drawColorWheel(wheel, type);
            applyProfessionalColorGrading();
            updateAllScopes();

            console.log(`🔄 ${type} wheel reset`);
        }

        // Professional scope functions
        function updateAllScopes() {
            if (!currentImageData) return;

            if (scopeSettings.histogram.enabled) updateProfessionalHistogram();
            if (scopeSettings.waveform.enabled) updateProfessionalWaveform();
            if (scopeSettings.vectorscope.enabled) updateProfessionalVectorscope();
            if (scopeSettings.parade.enabled) updateProfessionalParade();
        }

        function updateProfessionalHistogram() {
            if (!histogramCtx || !currentImageData) return;

            // Clear with professional background
            histogramCtx.fillStyle = '#0a0a0a';
            histogramCtx.fillRect(0, 0, histogramCanvas.width, histogramCanvas.height);

            // Draw professional grid
            drawHistogramGrid();

            const data = currentImageData.data;
            const histogram = { r: new Array(256).fill(0), g: new Array(256).fill(0), b: new Array(256).fill(0) };

            // Calculate histogram data
            for (let i = 0; i < data.length; i += 4) {
                histogram.r[data[i]]++;
                histogram.g[data[i + 1]]++;
                histogram.b[data[i + 2]]++;
            }

            // Draw professional histogram curves
            drawHistogramCurve(histogram.r, '#ff4444', 0.7);
            drawHistogramCurve(histogram.g, '#44ff44', 0.7);
            drawHistogramCurve(histogram.b, '#4444ff', 0.7);

            // Draw luminance curve
            const luminanceHist = new Array(256).fill(0);
            for (let i = 0; i < data.length; i += 4) {
                const luma = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
                luminanceHist[luma]++;
            }
            drawHistogramCurve(luminanceHist, '#ffffff', 1.0);
        }

        function drawHistogramGrid() {
            histogramCtx.strokeStyle = '#2a2a2a';
            histogramCtx.lineWidth = 1;

            // Vertical grid lines (stops)
            for (let i = 0; i <= 8; i++) {
                const x = (i / 8) * histogramCanvas.width;
                histogramCtx.beginPath();
                histogramCtx.moveTo(x, 0);
                histogramCtx.lineTo(x, histogramCanvas.height);
                histogramCtx.stroke();
            }

            // Horizontal grid lines
            for (let i = 0; i <= 4; i++) {
                const y = (i / 4) * histogramCanvas.height;
                histogramCtx.beginPath();
                histogramCtx.moveTo(0, y);
                histogramCtx.lineTo(histogramCanvas.width, y);
                histogramCtx.stroke();
            }
        }

        function drawHistogramCurve(histogram, color, alpha) {
            const maxValue = Math.max(...histogram);
            if (maxValue === 0) return;

            histogramCtx.strokeStyle = color;
            histogramCtx.globalAlpha = alpha;
            histogramCtx.lineWidth = 1.5;
            histogramCtx.beginPath();

            for (let i = 0; i < 256; i++) {
                const x = (i / 255) * histogramCanvas.width;
                const y = histogramCanvas.height - (histogram[i] / maxValue) * histogramCanvas.height;

                if (i === 0) {
                    histogramCtx.moveTo(x, y);
                } else {
                    histogramCtx.lineTo(x, y);
                }
            }

            histogramCtx.stroke();
            histogramCtx.globalAlpha = 1.0;
        }

        function updateProfessionalWaveform() {
            if (!waveformCtx || !currentImageData) return;

            waveformCtx.fillStyle = '#0a0a0a';
            waveformCtx.fillRect(0, 0, waveformCanvas.width, waveformCanvas.height);

            // Draw professional waveform grid
            drawWaveformGrid();

            const data = currentImageData.data;
            const width = canvas.width;
            const height = canvas.height;

            waveformCtx.fillStyle = 'rgba(255, 255, 255, 0.3)';

            // Professional waveform sampling
            for (let x = 0; x < width; x += 2) {
                for (let y = 0; y < height; y += 3) {
                    const index = (y * width + x) * 4;
                    const luminance = 0.299 * data[index] + 0.587 * data[index + 1] + 0.114 * data[index + 2];

                    const waveX = (x / width) * waveformCanvas.width;
                    const waveY = waveformCanvas.height - (luminance / 255) * waveformCanvas.height;

                    waveformCtx.fillRect(waveX, waveY, 1, 1);
                }
            }
        }

        function drawWaveformGrid() {
            waveformCtx.strokeStyle = '#2a2a2a';
            waveformCtx.lineWidth = 1;

            // IRE scale lines (professional video standard)
            const ireLines = [0, 7.5, 100, 109]; // Black, super black, white, super white
            ireLines.forEach(ire => {
                const y = waveformCanvas.height - (ire / 109) * waveformCanvas.height;
                waveformCtx.beginPath();
                waveformCtx.moveTo(0, y);
                waveformCtx.lineTo(waveformCanvas.width, y);
                waveformCtx.stroke();
            });
        }

        function updateProfessionalVectorscope() {
            if (!vectorscopeCtx || !currentImageData) return;

            vectorscopeCtx.fillStyle = '#0a0a0a';
            vectorscopeCtx.fillRect(0, 0, vectorscopeCanvas.width, vectorscopeCanvas.height);

            const centerX = vectorscopeCanvas.width / 2;
            const centerY = vectorscopeCanvas.height / 2;
            const radius = Math.min(centerX, centerY) - 20;

            // Draw professional vectorscope grid and targets
            drawVectorscopeGrid(centerX, centerY, radius);

            const data = currentImageData.data;
            vectorscopeCtx.fillStyle = 'rgba(255, 255, 255, 0.2)';

            // Professional vectorscope plotting
            for (let i = 0; i < data.length; i += 8) { // Sample every 2nd pixel
                const r = data[i] / 255;
                const g = data[i + 1] / 255;
                const b = data[i + 2] / 255;

                // Convert to YUV color space (professional standard)
                const u = -0.147 * r - 0.289 * g + 0.436 * b;
                const v = 0.615 * r - 0.515 * g - 0.100 * b;

                const x = centerX + (u * radius * 2);
                const y = centerY - (v * radius * 2);

                if (x >= 0 && x < vectorscopeCanvas.width && y >= 0 && y < vectorscopeCanvas.height) {
                    vectorscopeCtx.fillRect(x, y, 1, 1);
                }
            }
        }

        function drawVectorscopeGrid(centerX, centerY, radius) {
            vectorscopeCtx.strokeStyle = '#2a2a2a';
            vectorscopeCtx.lineWidth = 1;

            // Draw concentric circles
            for (let r = radius / 4; r <= radius; r += radius / 4) {
                vectorscopeCtx.beginPath();
                vectorscopeCtx.arc(centerX, centerY, r, 0, 2 * Math.PI);
                vectorscopeCtx.stroke();
            }

            // Draw professional color targets (SMPTE standard)
            const targets = [
                { angle: 103, color: '#ffff00', label: 'Yl' }, // Yellow
                { angle: 167, color: '#ff0000', label: 'R' },  // Red
                { angle: 241, color: '#ff00ff', label: 'Mg' }, // Magenta
                { angle: 283, color: '#0000ff', label: 'B' },  // Blue
                { angle: 347, color: '#00ffff', label: 'Cy' }, // Cyan
                { angle: 61, color: '#00ff00', label: 'G' }    // Green
            ];

            targets.forEach(target => {
                const angle = (target.angle - 90) * Math.PI / 180;
                const x = centerX + Math.cos(angle) * radius * 0.75;
                const y = centerY + Math.sin(angle) * radius * 0.75;

                vectorscopeCtx.fillStyle = target.color;
                vectorscopeCtx.beginPath();
                vectorscopeCtx.arc(x, y, 3, 0, 2 * Math.PI);
                vectorscopeCtx.fill();

                // Draw target boxes
                vectorscopeCtx.strokeStyle = target.color;
                vectorscopeCtx.strokeRect(x - 5, y - 5, 10, 10);
            });
        }

        function updateProfessionalParade() {
            if (!paradeCtx || !currentImageData) return;

            paradeCtx.fillStyle = '#0a0a0a';
            paradeCtx.fillRect(0, 0, paradeCanvas.width, paradeCanvas.height);

            const data = currentImageData.data;
            const width = canvas.width;
            const height = canvas.height;
            const channelWidth = paradeCanvas.width / 3;

            // Professional RGB parade
            const channels = [
                { index: 0, color: 'rgba(255, 100, 100, 0.4)', offset: 0 },
                { index: 1, color: 'rgba(100, 255, 100, 0.4)', offset: channelWidth },
                { index: 2, color: 'rgba(100, 100, 255, 0.4)', offset: channelWidth * 2 }
            ];

            channels.forEach(channel => {
                paradeCtx.fillStyle = channel.color;

                for (let x = 0; x < width; x += 2) {
                    for (let y = 0; y < height; y += 3) {
                        const index = (y * width + x) * 4 + channel.index;
                        const value = data[index];

                        const paradeX = channel.offset + (x / width) * channelWidth;
                        const paradeY = paradeCanvas.height - (value / 255) * paradeCanvas.height;

                        paradeCtx.fillRect(paradeX, paradeY, 1, 1);
                    }
                }

                // Draw channel separator
                if (channel.offset > 0) {
                    paradeCtx.strokeStyle = '#2a2a2a';
                    paradeCtx.lineWidth = 1;
                    paradeCtx.beginPath();
                    paradeCtx.moveTo(channel.offset, 0);
                    paradeCtx.lineTo(channel.offset, paradeCanvas.height);
                    paradeCtx.stroke();
                }
            });
        }

        // Professional analysis and utility functions
        function updatePixelInspector(event) {
            if (!currentImageData) return;

            const rect = canvas.getBoundingClientRect();
            const x = Math.floor(event.clientX - rect.left);
            const y = Math.floor(event.clientY - rect.top);

            if (x >= 0 && x < canvas.width && y >= 0 && y < canvas.height) {
                const index = (y * canvas.width + x) * 4;
                const data = currentImageData.data;

                const r = data[index];
                const g = data[index + 1];
                const b = data[index + 2];
                const luma = Math.round(0.299 * r + 0.587 * g + 0.114 * b);

                document.getElementById('redValue').textContent = r;
                document.getElementById('greenValue').textContent = g;
                document.getElementById('blueValue').textContent = b;
                document.getElementById('lumaValue').textContent = luma;
            }
        }

        function clearPixelInspector() {
            document.getElementById('redValue').textContent = '-';
            document.getElementById('greenValue').textContent = '-';
            document.getElementById('blueValue').textContent = '-';
            document.getElementById('lumaValue').textContent = '-';
        }

        function performProfessionalAnalysis() {
            if (!currentImageData) return;

            const data = currentImageData.data;
            let minLuma = 255, maxLuma = 0;
            let totalLuma = 0, totalSat = 0;
            let warmPixels = 0, coolPixels = 0;
            const totalPixels = data.length / 4;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                const luma = 0.299 * r + 0.587 * g + 0.114 * b;
                const saturation = Math.max(r, g, b) - Math.min(r, g, b);

                minLuma = Math.min(minLuma, luma);
                maxLuma = Math.max(maxLuma, luma);
                totalLuma += luma;
                totalSat += saturation;

                // Color temperature analysis
                if (r > b + 10) warmPixels++;
                if (b > r + 10) coolPixels++;
            }

            const dynamicRange = maxLuma - minLuma;
            const avgLuminance = totalLuma / totalPixels;
            const avgSaturation = totalSat / totalPixels;
            const colorTemp = warmPixels > coolPixels ? 'Warm' : coolPixels > warmPixels ? 'Cool' : 'Neutral';

            // Update analysis display
            document.getElementById('dynamicRange').textContent = dynamicRange.toFixed(1);
            document.getElementById('avgLuminance').textContent = avgLuminance.toFixed(1);
            document.getElementById('colorTemp').textContent = colorTemp;
            document.getElementById('satIndex').textContent = (avgSaturation / 255 * 100).toFixed(1) + '%';
        }

        // Professional utility functions
        function toggleSection(header) {
            const content = header.nextElementSibling;
            const chevron = header.querySelector('.chevron');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                header.classList.remove('collapsed');
                chevron.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                header.classList.add('collapsed');
                chevron.textContent = '▶';
            }
        }

        function toggleScope(scopeType) {
            const button = event.target;
            const scope = document.getElementById(scopeType + 'Scope');

            if (button.classList.contains('active')) {
                button.classList.remove('active');
                scope.style.display = 'none';
                scopeSettings[scopeType].enabled = false;
            } else {
                button.classList.add('active');
                scope.style.display = 'block';
                scopeSettings[scopeType].enabled = true;
            }

            updateAllScopes();
        }

        function autoAnalyze() {
            console.log('🔍 Professional auto analysis...');
            performProfessionalAnalysis();

            // Professional auto analysis with recommendations
            const data = currentImageData.data;
            let underexposed = 0, overexposed = 0;
            const totalPixels = data.length / 4;

            for (let i = 0; i < data.length; i += 4) {
                const luma = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                if (luma < 16) underexposed++;
                if (luma > 235) overexposed++; // Broadcast safe
            }

            const underPercent = (underexposed / totalPixels * 100);
            const overPercent = (overexposed / totalPixels * 100);

            console.log(`📊 Analysis: ${underPercent.toFixed(1)}% underexposed, ${overPercent.toFixed(1)}% overexposed`);
        }

        function autoCorrect() {
            console.log('🎯 Professional auto correction...');

            // Professional auto correction based on analysis
            const data = currentImageData.data;
            let avgR = 0, avgG = 0, avgB = 0;
            const totalPixels = data.length / 4;

            for (let i = 0; i < data.length; i += 4) {
                avgR += data[i];
                avgG += data[i + 1];
                avgB += data[i + 2];
            }

            avgR /= totalPixels;
            avgG /= totalPixels;
            avgB /= totalPixels;

            // Auto white balance
            const grayTarget = 128;
            const tempCorrection = (avgB - avgR) / 255 * 50;
            const tintCorrection = (avgG - grayTarget) / 255 * 30;

            // Apply corrections
            document.getElementById('temperature').value = tempCorrection;
            document.getElementById('tint').value = tintCorrection;
            updateColorGrading('temperature', tempCorrection);
            updateColorGrading('tint', tintCorrection);

            console.log('✅ Professional auto correction applied');
        }

        function resetAll() {
            console.log('🔄 Professional reset...');

            // Reset all color grading parameters
            Object.keys(colorGrading).forEach(key => {
                if (typeof colorGrading[key] === 'object') {
                    Object.keys(colorGrading[key]).forEach(subKey => {
                        colorGrading[key][subKey] = (key === 'gamma') ? 1 : 0;
                    });
                } else {
                    colorGrading[key] = (key.includes('gamma')) ? 1 : 0;
                }
            });

            // Reset UI sliders
            const sliders = [
                'exposure', 'contrast', 'highlights', 'shadows', 'whites', 'blacks',
                'temperature', 'tint', 'vibrance', 'saturation'
            ];

            sliders.forEach(sliderId => {
                const slider = document.getElementById(sliderId);
                const valueDisplay = document.getElementById(sliderId + 'Value');
                if (slider && valueDisplay) {
                    slider.value = 0;
                    valueDisplay.textContent = sliderId === 'exposure' ? '0.00' : '0';
                }
            });

            // Reset color wheels
            ['lift', 'gamma', 'gain'].forEach(type => resetWheel(type));

            // Restore original image
            if (originalImageData) {
                ctx.putImageData(originalImageData, 0, 0);
                currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            }

            updateAllScopes();
            performProfessionalAnalysis();

            console.log('✅ Professional reset complete');
        }

        function exportImage() {
            if (!canvas) {
                alert('No image to export!');
                return;
            }

            const link = document.createElement('a');
            link.download = `professional_graded_${Date.now()}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();

            console.log('📥 Professional image exported');
        }

        function exportData() {
            if (!currentImageData) {
                alert('No image data to export!');
                return;
            }

            const exportData = {
                colorGrading: colorGrading,
                analysis: {
                    dynamicRange: document.getElementById('dynamicRange').textContent,
                    avgLuminance: document.getElementById('avgLuminance').textContent,
                    colorTemp: document.getElementById('colorTemp').textContent,
                    satIndex: document.getElementById('satIndex').textContent
                },
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `professional_data_${Date.now()}.json`;
            link.click();

            URL.revokeObjectURL(url);
            console.log('📊 Professional data exported');
        }

        function generateReport() {
            if (!currentImageData) {
                alert('No image to analyze!');
                return;
            }

            const report = `
PROFESSIONAL COLOR GRADING REPORT
Generated: ${new Date().toLocaleString()}

IMAGE ANALYSIS:
- Dynamic Range: ${document.getElementById('dynamicRange').textContent}
- Average Luminance: ${document.getElementById('avgLuminance').textContent}
- Color Temperature: ${document.getElementById('colorTemp').textContent}
- Saturation Index: ${document.getElementById('satIndex').textContent}

COLOR GRADING SETTINGS:
- Exposure: ${colorGrading.exposure.toFixed(2)} stops
- Contrast: ${colorGrading.contrast}
- Highlights: ${colorGrading.highlights}
- Shadows: ${colorGrading.shadows}
- Whites: ${colorGrading.whites}
- Blacks: ${colorGrading.blacks}
- Temperature: ${colorGrading.temperature}
- Tint: ${colorGrading.tint}
- Vibrance: ${colorGrading.vibrance}
- Saturation: ${colorGrading.saturation}

LIFT GAMMA GAIN:
- Lift: R:${colorGrading.lift.r.toFixed(3)} G:${colorGrading.lift.g.toFixed(3)} B:${colorGrading.lift.b.toFixed(3)}
- Gamma: R:${colorGrading.gamma.r.toFixed(3)} G:${colorGrading.gamma.g.toFixed(3)} B:${colorGrading.gamma.b.toFixed(3)}
- Gain: R:${colorGrading.gain.r.toFixed(3)} G:${colorGrading.gain.g.toFixed(3)} B:${colorGrading.gain.b.toFixed(3)}
            `.trim();

            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `professional_report_${Date.now()}.txt`;
            link.click();

            URL.revokeObjectURL(url);
            console.log('📋 Professional report generated');
        }

        function savePreset() {
            const presetName = prompt('Enter preset name:');
            if (presetName) {
                const preset = {
                    name: presetName,
                    colorGrading: JSON.parse(JSON.stringify(colorGrading)),
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem(`professional_preset_${presetName}`, JSON.stringify(preset));
                console.log(`💾 Preset "${presetName}" saved`);
            }
        }

        function loadPreset() {
            const presetName = prompt('Enter preset name to load:');
            if (presetName) {
                const presetData = localStorage.getItem(`professional_preset_${presetName}`);
                if (presetData) {
                    const preset = JSON.parse(presetData);

                    // Load color grading settings
                    Object.assign(colorGrading, preset.colorGrading);

                    // Update UI
                    const sliders = [
                        'exposure', 'contrast', 'highlights', 'shadows', 'whites', 'blacks',
                        'temperature', 'tint', 'vibrance', 'saturation'
                    ];

                    sliders.forEach(sliderId => {
                        const slider = document.getElementById(sliderId);
                        const valueDisplay = document.getElementById(sliderId + 'Value');
                        if (slider && valueDisplay) {
                            slider.value = colorGrading[sliderId];
                            valueDisplay.textContent = sliderId === 'exposure' ?
                                colorGrading[sliderId].toFixed(2) :
                                Math.round(colorGrading[sliderId]);
                        }
                    });

                    applyProfessionalColorGrading();
                    updateAllScopes();

                    console.log(`📂 Preset "${presetName}" loaded`);
                } else {
                    alert('Preset not found!');
                }
            }
        }

        function hideUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            if (uploadZone) {
                uploadZone.style.display = 'none';
            }
        }

        // Initialize professional workspace when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎬 Professional workspace loading...');
            if (initializeProfessionalWorkspace()) {
                console.log('✅ Professional workspace ready');
            } else {
                console.log('❌ Professional workspace initialization failed');
            }
        });
    </script>
</body>
</html>
