<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lens Correction & Advanced Filters</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #e0e0e0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .correction-container {
            display: grid;
            grid-template-areas: 
                "controls canvas preview"
                "presets canvas preview";
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 1fr 200px;
            height: 100vh;
            gap: 1px;
            background: #404040;
        }
        
        .controls-panel {
            grid-area: controls;
            background: #2d2d2d;
            padding: 15px;
            overflow-y: auto;
        }
        
        .canvas-area {
            grid-area: canvas;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-panel {
            grid-area: preview;
            background: #2d2d2d;
            padding: 15px;
            border-left: 1px solid #404040;
        }
        
        .presets-panel {
            grid-area: presets;
            background: #252525;
            padding: 15px;
            border-top: 1px solid #404040;
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 12px;
            text-transform: uppercase;
            color: #888;
            margin-bottom: 12px;
            letter-spacing: 1px;
            border-bottom: 1px solid #404040;
            padding-bottom: 6px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-slider {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 8px;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,212,255,0.3);
        }
        
        .control-value {
            font-size: 11px;
            color: #00d4ff;
            font-weight: 500;
        }
        
        .main-canvas {
            max-width: 100%;
            max-height: 100%;
            cursor: crosshair;
            object-fit: contain;
            background: #0f0f0f;
        }
        
        .preview-canvas {
            width: 100%;
            height: 200px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.3;
        }
        
        .correction-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 6px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
        }
        
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        
        .preset-button {
            background: linear-gradient(135deg, #404040, #353535);
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 8px 6px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
            text-align: center;
        }
        
        .preset-button:hover {
            background: linear-gradient(135deg, #4a4a4a, #3f3f3f);
            border-color: #00d4ff;
        }
        
        .preset-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-color: #00d4ff;
            color: #000;
        }
        
        .toggle-button {
            background: #404040;
            border: 1px solid #555;
            color: #e0e0e0;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            margin-right: 8px;
        }
        
        .toggle-button:hover {
            background: #4a4a4a;
            border-color: #00d4ff;
        }
        
        .toggle-button.active {
            background: #00d4ff;
            border-color: #00d4ff;
            color: #000;
        }
        
        .before-after {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .comparison-canvas {
            flex: 1;
            height: 120px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .canvas-label {
            font-size: 10px;
            color: #888;
            text-align: center;
            margin-top: 4px;
        }
        
        .chromatic-preview {
            width: 100%;
            height: 80px;
            background: #0f0f0f;
            border: 1px solid #404040;
            border-radius: 4px;
            margin: 8px 0;
            position: relative;
        }
        
        .aberration-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .red-channel { border-color: #ff4444; }
        .green-channel { border-color: #44ff44; }
        .blue-channel { border-color: #4444ff; }
        
        .distortion-grid {
            stroke: #666;
            stroke-width: 1;
            fill: none;
            opacity: 0.5;
        }

        .upload-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 3px dashed #00d4ff;
            border-radius: 12px;
            padding: 60px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: rgba(0, 212, 255, 0.05);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .upload-zone:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .upload-text {
            font-size: 14px;
            color: #ccc;
        }
        
        .perspective-handles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #00d4ff;
            border: 1px solid #fff;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10;
        }
        
        .handle-tl { top: 10px; left: 10px; }
        .handle-tr { top: 10px; right: 10px; }
        .handle-bl { bottom: 10px; left: 10px; }
        .handle-br { bottom: 10px; right: 10px; }
        
        .auto-correction {
            background: #252525;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
            border: 1px solid #3a3a3a;
        }
        
        .auto-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: 1px solid #28a745;
            color: #fff;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
            width: 100%;
            margin-bottom: 8px;
        }
        
        .auto-button:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="correction-container">
        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="section">
                <div class="section-title">🔧 Lens Distortion</div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Barrel/Pincushion</span>
                        <span class="control-value" id="distortionValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="distortion" min="-100" max="100" value="0">
                </div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Vignetting</span>
                        <span class="control-value" id="vignettingValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="vignetting" min="-100" max="100" value="0">
                </div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Scale</span>
                        <span class="control-value" id="scaleValue">100%</span>
                    </div>
                    <input type="range" class="control-slider" id="scale" min="50" max="150" value="100">
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">🌈 Chromatic Aberration</div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Red/Cyan Fringe</span>
                        <span class="control-value" id="redFringeValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="redFringe" min="-50" max="50" value="0">
                </div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Blue/Yellow Fringe</span>
                        <span class="control-value" id="blueFringeValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="blueFringe" min="-50" max="50" value="0">
                </div>
                
                <div class="chromatic-preview" id="chromaticPreview">
                    <div class="aberration-indicator red-channel" style="left: 48%;"></div>
                    <div class="aberration-indicator green-channel"></div>
                    <div class="aberration-indicator blue-channel" style="left: 52%;"></div>
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">📐 Perspective Correction</div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Horizontal</span>
                        <span class="control-value" id="horizontalValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="horizontal" min="-45" max="45" value="0">
                </div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Vertical</span>
                        <span class="control-value" id="verticalValue">0</span>
                    </div>
                    <input type="range" class="control-slider" id="vertical" min="-45" max="45" value="0">
                </div>
                
                <div class="control-group">
                    <div class="control-label">
                        <span>Rotation</span>
                        <span class="control-value" id="rotationValue">0°</span>
                    </div>
                    <input type="range" class="control-slider" id="rotation" min="-10" max="10" value="0" step="0.1">
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">⚙️ Options</div>
                
                <div style="margin-bottom: 10px;">
                    <button class="toggle-button active" id="gridToggle" onclick="toggleGrid()">Grid</button>
                    <button class="toggle-button" id="previewToggle" onclick="togglePreview()">Preview</button>
                </div>
                
                <div class="auto-correction">
                    <button class="auto-button" onclick="autoCorrectPerspective()">🤖 Auto Perspective</button>
                    <button class="auto-button" onclick="autoCorrectDistortion()">🔍 Auto Distortion</button>
                    <button class="auto-button" onclick="autoCorrectChromatic()">🌈 Auto Chromatic</button>
                    <div class="progress-bar" id="progressBar" style="display: none;">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area">
            <canvas class="main-canvas" id="correctionCanvas"></canvas>

            <!-- Grid Overlay -->
            <svg class="grid-overlay" id="gridOverlay">
                <!-- Grid lines will be drawn here -->
            </svg>

            <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Klikněte nebo přetáhněte obrázek sem</div>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
            </svg>

            <!-- Perspective Handles -->
            <div class="perspective-handles handle-tl" id="handleTL"></div>
            <div class="perspective-handles handle-tr" id="handleTR"></div>
            <div class="perspective-handles handle-bl" id="handleBL"></div>
            <div class="perspective-handles handle-br" id="handleBR"></div>

            <div class="correction-info" id="correctionInfo">
                <div>Lens: Standard</div>
                <div>Focal Length: 50mm</div>
                <div>Distortion: <span id="infoDistortion">0%</span></div>
                <div>CA Correction: <span id="infoChromaticAberration">Off</span></div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <div class="section-title">👁️ Before / After</div>

            <div class="before-after">
                <div>
                    <canvas class="comparison-canvas" id="beforeCanvas" width="140" height="120"></canvas>
                    <div class="canvas-label">Before</div>
                </div>
                <div>
                    <canvas class="comparison-canvas" id="afterCanvas" width="140" height="120"></canvas>
                    <div class="canvas-label">After</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">🔍 Detail View</div>
                <canvas class="preview-canvas" id="detailCanvas" width="270" height="200"></canvas>
                <div style="font-size: 10px; color: #888; text-align: center;">
                    Click on main image to inspect details
                </div>
            </div>

            <div class="section">
                <div class="section-title">📊 Correction Stats</div>
                <div style="font-size: 11px; line-height: 1.4;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>Barrel Distortion:</span>
                        <span id="statDistortion">0%</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>CA Red/Cyan:</span>
                        <span id="statRedCA">0px</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>CA Blue/Yellow:</span>
                        <span id="statBlueCA">0px</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>Perspective H:</span>
                        <span id="statHorizontal">0°</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>Perspective V:</span>
                        <span id="statVertical">0°</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Rotation:</span>
                        <span id="statRotation">0°</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Presets Panel -->
        <div class="presets-panel">
            <div class="section-title">📷 Lens Presets</div>

            <div class="preset-grid">
                <button class="preset-button" onclick="applyLensPreset('standard')">📷 Standard</button>
                <button class="preset-button" onclick="applyLensPreset('wideangle')">🌐 Wide Angle</button>
                <button class="preset-button" onclick="applyLensPreset('fisheye')">🐟 Fisheye</button>
                <button class="preset-button" onclick="applyLensPreset('telephoto')">🔭 Telephoto</button>
                <button class="preset-button" onclick="applyLensPreset('macro')">🔬 Macro</button>
                <button class="preset-button" onclick="applyLensPreset('vintage')">📸 Vintage</button>
                <button class="preset-button" onclick="applyLensPreset('gopro')">📹 Action Cam</button>
                <button class="preset-button" onclick="applyLensPreset('drone')">🚁 Drone</button>
            </div>

            <div style="margin-top: 15px;">
                <div class="section-title">🎯 Quick Actions</div>
                <button class="preset-button" onclick="resetCorrections()" style="width: 100%; margin-bottom: 6px;">🔄 Reset All</button>
                <button class="preset-button" onclick="copySettings()" style="width: 100%; margin-bottom: 6px;">📋 Copy Settings</button>
                <button class="preset-button" onclick="pasteSettings()" style="width: 100%; margin-bottom: 6px;">📄 Paste Settings</button>
                <button class="preset-button" onclick="savePreset()" style="width: 100%;">💾 Save Preset</button>
            </div>
        </div>
    </div>

    <script>
        class LensCorrection {
            constructor() {
                this.canvas = document.getElementById('correctionCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.beforeCanvas = document.getElementById('beforeCanvas');
                this.beforeCtx = this.beforeCanvas.getContext('2d');
                this.afterCanvas = document.getElementById('afterCanvas');
                this.afterCtx = this.afterCanvas.getContext('2d');
                this.detailCanvas = document.getElementById('detailCanvas');
                this.detailCtx = this.detailCanvas.getContext('2d');

                this.originalImageData = null;
                this.currentImageData = null;
                this.showGrid = true;
                this.showPreview = false;

                this.corrections = {
                    distortion: 0,
                    vignetting: 0,
                    scale: 100,
                    redFringe: 0,
                    blueFringe: 0,
                    horizontal: 0,
                    vertical: 0,
                    rotation: 0
                };

                this.perspectiveHandles = {
                    tl: { x: 0.1, y: 0.1 },
                    tr: { x: 0.9, y: 0.1 },
                    bl: { x: 0.1, y: 0.9 },
                    br: { x: 0.9, y: 0.9 }
                };

                this.initializeCanvas();
                this.initializeEventListeners();
                this.loadTestImage();
                this.drawGrid();
            }

            initializeCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;
            }

            initializeEventListeners() {
                // File upload
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.loadImage(e.target.files[0]);
                    }
                });

                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.15)';
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.05)';
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.style.background = 'rgba(0, 212, 255, 0.05)';
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                // Control sliders
                Object.keys(this.corrections).forEach(correction => {
                    const slider = document.getElementById(correction);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateCorrection(correction, parseFloat(e.target.value));
                        });
                    }
                });

                // Canvas click for detail view
                this.canvas.addEventListener('click', (e) => {
                    this.updateDetailView(e);
                });

                // Perspective handles
                this.initializePerspectiveHandles();
            }

            initializePerspectiveHandles() {
                const handles = ['TL', 'TR', 'BL', 'BR'];
                handles.forEach(handle => {
                    const element = document.getElementById('handle' + handle);
                    if (element) {
                        element.addEventListener('mousedown', (e) => {
                            this.startDragHandle(handle.toLowerCase(), e);
                        });
                    }
                });

                document.addEventListener('mousemove', (e) => {
                    this.dragHandle(e);
                });

                document.addEventListener('mouseup', () => {
                    this.endDragHandle();
                });
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // Get canvas container size
                        const canvasContainer = this.canvas.parentElement;
                        const containerWidth = canvasContainer.clientWidth;
                        const containerHeight = canvasContainer.clientHeight;

                        // Calculate optimal size maintaining aspect ratio
                        let { width, height } = img;
                        const imgRatio = width / height;
                        const containerRatio = containerWidth / containerHeight;

                        if (imgRatio > containerRatio) {
                            width = containerWidth * 0.9;
                            height = width / imgRatio;
                        } else {
                            height = containerHeight * 0.9;
                            width = height * imgRatio;
                        }

                        // Set canvas size
                        this.canvas.width = width;
                        this.canvas.height = height;
                        this.canvas.style.width = width + 'px';
                        this.canvas.style.height = height + 'px';

                        // Clear and draw image
                        this.ctx.clearRect(0, 0, width, height);
                        this.ctx.drawImage(img, 0, 0, width, height);

                        this.originalImageData = this.ctx.getImageData(0, 0, width, height);

                        document.getElementById('uploadZone').style.display = 'none';
                        console.log('Image loaded successfully:', file.name);
                    };
                    img.onerror = () => {
                        alert('Chyba při načítání obrázku. Zkuste jiný formát.');
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    alert('Chyba při čtení souboru.');
                };
                reader.readAsDataURL(file);
            }

            loadTestImage() {
                // Create a test image with distortion patterns
                this.canvas.width = 800;
                this.canvas.height = 600;

                // Create a grid pattern to show distortion
                this.ctx.fillStyle = '#4a5568';
                this.ctx.fillRect(0, 0, 800, 600);

                // Draw test grid
                this.ctx.strokeStyle = '#e2e8f0';
                this.ctx.lineWidth = 2;

                for (let x = 0; x <= 800; x += 100) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, 600);
                    this.ctx.stroke();
                }

                for (let y = 0; y <= 600; y += 100) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(800, y);
                    this.ctx.stroke();
                }

                // Add some geometric shapes
                this.ctx.fillStyle = '#ff6b6b';
                this.ctx.fillRect(100, 100, 200, 150);

                this.ctx.fillStyle = '#4ecdc4';
                this.ctx.beginPath();
                this.ctx.arc(600, 200, 80, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.fillStyle = '#45b7d1';
                this.ctx.beginPath();
                this.ctx.moveTo(400, 350);
                this.ctx.lineTo(500, 350);
                this.ctx.lineTo(450, 450);
                this.ctx.closePath();
                this.ctx.fill();

                // Add text
                this.ctx.fillStyle = '#2d3748';
                this.ctx.font = 'bold 24px Arial';
                this.ctx.fillText('Lens Correction Test', 250, 500);

                this.originalImageData = this.ctx.getImageData(0, 0, 800, 600);
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    800,
                    600
                );

                this.updatePreviews();
                document.getElementById('uploadZone').style.display = 'none';
            }

            updateCorrection(type, value) {
                this.corrections[type] = value;

                // Update display value
                const valueElement = document.getElementById(type + 'Value');
                if (valueElement) {
                    if (type === 'scale') {
                        valueElement.textContent = value + '%';
                    } else if (type === 'rotation') {
                        valueElement.textContent = value + '°';
                    } else {
                        valueElement.textContent = value;
                    }
                }

                this.applyCorrections();
                this.updateStats();
                this.updateCorrectionInfo();
            }

            applyCorrections() {
                // Reset to original
                this.currentImageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                // Apply corrections in order
                if (this.corrections.distortion !== 0) {
                    this.applyBarrelDistortion();
                }

                if (this.corrections.vignetting !== 0) {
                    this.applyVignetting();
                }

                if (this.corrections.redFringe !== 0 || this.corrections.blueFringe !== 0) {
                    this.applyChromaticAberrationCorrection();
                }

                if (this.corrections.horizontal !== 0 || this.corrections.vertical !== 0) {
                    this.applyPerspectiveCorrection();
                }

                if (this.corrections.rotation !== 0) {
                    this.applyRotation();
                }

                if (this.corrections.scale !== 100) {
                    this.applyScale();
                }

                // Draw to canvas
                this.ctx.putImageData(this.currentImageData, 0, 0);
                this.updatePreviews();
            }

            applyBarrelDistortion() {
                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const centerX = width / 2;
                const centerY = height / 2;
                const maxRadius = Math.sqrt(centerX * centerX + centerY * centerY);
                const strength = this.corrections.distortion / 100;

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const dx = x - centerX;
                        const dy = y - centerY;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const normalizedDistance = distance / maxRadius;

                        // Barrel/pincushion distortion formula
                        const distortionFactor = 1 + strength * normalizedDistance * normalizedDistance;

                        const sourceX = centerX + dx / distortionFactor;
                        const sourceY = centerY + dy / distortionFactor;

                        if (sourceX >= 0 && sourceX < width && sourceY >= 0 && sourceY < height) {
                            const sourceIndex = (Math.floor(sourceY) * width + Math.floor(sourceX)) * 4;
                            const targetIndex = (y * width + x) * 4;

                            output[targetIndex] = data[sourceIndex];
                            output[targetIndex + 1] = data[sourceIndex + 1];
                            output[targetIndex + 2] = data[sourceIndex + 2];
                            output[targetIndex + 3] = data[sourceIndex + 3];
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyVignetting() {
                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const centerX = width / 2;
                const centerY = height / 2;
                const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
                const intensity = this.corrections.vignetting / 100;

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                        const vignetteFactor = intensity > 0 ?
                            1 - (distance / maxDistance) * intensity : // Darken edges
                            1 + (distance / maxDistance) * Math.abs(intensity); // Lighten edges

                        const index = (y * width + x) * 4;
                        data[index] = Math.max(0, Math.min(255, data[index] * vignetteFactor));
                        data[index + 1] = Math.max(0, Math.min(255, data[index + 1] * vignetteFactor));
                        data[index + 2] = Math.max(0, Math.min(255, data[index + 2] * vignetteFactor));
                    }
                }
            }
        }

            applyChromaticAberrationCorrection() {
                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const redShift = this.corrections.redFringe / 10;
                const blueShift = this.corrections.blueFringe / 10;

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const index = (y * width + x) * 4;

                        // Red channel shift
                        const redX = Math.round(x + redShift);
                        const redY = y;
                        if (redX >= 0 && redX < width && redY >= 0 && redY < height) {
                            const redIndex = (redY * width + redX) * 4;
                            output[index] = data[redIndex];
                        }

                        // Green channel (no shift)
                        output[index + 1] = data[index + 1];

                        // Blue channel shift
                        const blueX = Math.round(x + blueShift);
                        const blueY = y;
                        if (blueX >= 0 && blueX < width && blueY >= 0 && blueY < height) {
                            const blueIndex = (blueY * width + blueX) * 4;
                            output[index + 2] = data[blueIndex + 2];
                        }

                        output[index + 3] = data[index + 3]; // Alpha
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyPerspectiveCorrection() {
                // Simple perspective correction using horizontal and vertical skew
                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const hSkew = this.corrections.horizontal * Math.PI / 180;
                const vSkew = this.corrections.vertical * Math.PI / 180;

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        // Apply perspective transformation
                        const normalizedX = (x - width / 2) / width;
                        const normalizedY = (y - height / 2) / height;

                        const sourceX = x + normalizedY * Math.tan(hSkew) * width / 2;
                        const sourceY = y + normalizedX * Math.tan(vSkew) * height / 2;

                        if (sourceX >= 0 && sourceX < width && sourceY >= 0 && sourceY < height) {
                            const sourceIndex = (Math.floor(sourceY) * width + Math.floor(sourceX)) * 4;
                            const targetIndex = (y * width + x) * 4;

                            output[targetIndex] = data[sourceIndex];
                            output[targetIndex + 1] = data[sourceIndex + 1];
                            output[targetIndex + 2] = data[sourceIndex + 2];
                            output[targetIndex + 3] = data[sourceIndex + 3];
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyRotation() {
                const data = this.currentImageData.data;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;
                const output = new Uint8ClampedArray(data);

                const angle = this.corrections.rotation * Math.PI / 180;
                const centerX = width / 2;
                const centerY = height / 2;
                const cos = Math.cos(-angle);
                const sin = Math.sin(-angle);

                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const dx = x - centerX;
                        const dy = y - centerY;

                        const sourceX = centerX + dx * cos - dy * sin;
                        const sourceY = centerY + dx * sin + dy * cos;

                        if (sourceX >= 0 && sourceX < width && sourceY >= 0 && sourceY < height) {
                            const sourceIndex = (Math.floor(sourceY) * width + Math.floor(sourceX)) * 4;
                            const targetIndex = (y * width + x) * 4;

                            output[targetIndex] = data[sourceIndex];
                            output[targetIndex + 1] = data[sourceIndex + 1];
                            output[targetIndex + 2] = data[sourceIndex + 2];
                            output[targetIndex + 3] = data[sourceIndex + 3];
                        }
                    }
                }

                // Copy back
                for (let i = 0; i < data.length; i++) {
                    data[i] = output[i];
                }
            }

            applyScale() {
                const scaleFactor = this.corrections.scale / 100;
                const width = this.currentImageData.width;
                const height = this.currentImageData.height;

                // Create temporary canvas for scaling
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = width;
                tempCanvas.height = height;

                tempCtx.putImageData(this.currentImageData, 0, 0);

                // Clear and scale
                this.ctx.clearRect(0, 0, width, height);
                this.ctx.save();
                this.ctx.translate(width / 2, height / 2);
                this.ctx.scale(scaleFactor, scaleFactor);
                this.ctx.translate(-width / 2, -height / 2);
                this.ctx.drawImage(tempCanvas, 0, 0);
                this.ctx.restore();

                this.currentImageData = this.ctx.getImageData(0, 0, width, height);
            }

            updatePreviews() {
                // Update before canvas
                this.beforeCtx.putImageData(
                    this.resizeImageData(this.originalImageData, 140, 120),
                    0, 0
                );

                // Update after canvas
                this.afterCtx.putImageData(
                    this.resizeImageData(this.currentImageData, 140, 120),
                    0, 0
                );
            }

            resizeImageData(imageData, newWidth, newHeight) {
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = imageData.width;
                tempCanvas.height = imageData.height;

                tempCtx.putImageData(imageData, 0, 0);

                const resizedCanvas = document.createElement('canvas');
                const resizedCtx = resizedCanvas.getContext('2d');
                resizedCanvas.width = newWidth;
                resizedCanvas.height = newHeight;

                resizedCtx.drawImage(tempCanvas, 0, 0, newWidth, newHeight);
                return resizedCtx.getImageData(0, 0, newWidth, newHeight);
            }

            updateDetailView(e) {
                const rect = this.canvas.getBoundingClientRect();
                const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
                const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);

                // Extract detail area (100x100 pixels around click point)
                const detailSize = 100;
                const startX = Math.max(0, x - detailSize / 2);
                const startY = Math.max(0, y - detailSize / 2);

                const detailData = this.ctx.getImageData(startX, startY, detailSize, detailSize);

                // Scale up for detail view
                this.detailCtx.clearRect(0, 0, 270, 200);
                this.detailCtx.imageSmoothingEnabled = false;

                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = detailSize;
                tempCanvas.height = detailSize;
                tempCtx.putImageData(detailData, 0, 0);

                this.detailCtx.drawImage(tempCanvas, 0, 0, 270, 200);

                // Draw crosshair
                this.detailCtx.strokeStyle = '#00d4ff';
                this.detailCtx.lineWidth = 1;
                this.detailCtx.beginPath();
                this.detailCtx.moveTo(135, 0);
                this.detailCtx.lineTo(135, 200);
                this.detailCtx.moveTo(0, 100);
                this.detailCtx.lineTo(270, 100);
                this.detailCtx.stroke();
            }

            updateStats() {
                document.getElementById('statDistortion').textContent = this.corrections.distortion + '%';
                document.getElementById('statRedCA').textContent = this.corrections.redFringe + 'px';
                document.getElementById('statBlueCA').textContent = this.corrections.blueFringe + 'px';
                document.getElementById('statHorizontal').textContent = this.corrections.horizontal + '°';
                document.getElementById('statVertical').textContent = this.corrections.vertical + '°';
                document.getElementById('statRotation').textContent = this.corrections.rotation + '°';
            }

            updateCorrectionInfo() {
                document.getElementById('infoDistortion').textContent = this.corrections.distortion + '%';

                const hasChromaticCorrection = this.corrections.redFringe !== 0 || this.corrections.blueFringe !== 0;
                document.getElementById('infoChromaticAberration').textContent = hasChromaticCorrection ? 'On' : 'Off';
            }

            drawGrid() {
                const svg = document.getElementById('gridOverlay');
                svg.innerHTML = '';

                if (!this.showGrid) return;

                const width = 800;
                const height = 600;

                // Vertical lines
                for (let x = 0; x <= width; x += width / 8) {
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', x);
                    line.setAttribute('y1', 0);
                    line.setAttribute('x2', x);
                    line.setAttribute('y2', height);
                    line.setAttribute('class', 'distortion-grid');
                    svg.appendChild(line);
                }

                // Horizontal lines
                for (let y = 0; y <= height; y += height / 6) {
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', 0);
                    line.setAttribute('y1', y);
                    line.setAttribute('x2', width);
                    line.setAttribute('y2', y);
                    line.setAttribute('class', 'distortion-grid');
                    svg.appendChild(line);
                }
            }
        }

        // Global functions
        function toggleGrid() {
            if (!lensCorrection) return;

            lensCorrection.showGrid = !lensCorrection.showGrid;
            document.getElementById('gridToggle').classList.toggle('active', lensCorrection.showGrid);
            lensCorrection.drawGrid();
        }

        function togglePreview() {
            if (!lensCorrection) return;

            lensCorrection.showPreview = !lensCorrection.showPreview;
            document.getElementById('previewToggle').classList.toggle('active', lensCorrection.showPreview);
        }

        function applyLensPreset(presetName) {
            if (!lensCorrection) return;

            const presets = {
                standard: { distortion: 0, vignetting: 0, redFringe: 0, blueFringe: 0 },
                wideangle: { distortion: -15, vignetting: 25, redFringe: 2, blueFringe: -1 },
                fisheye: { distortion: -45, vignetting: 40, redFringe: 5, blueFringe: -3 },
                telephoto: { distortion: 5, vignetting: -10, redFringe: -1, blueFringe: 1 },
                macro: { distortion: 8, vignetting: 15, redFringe: 3, blueFringe: -2 },
                vintage: { distortion: 12, vignetting: 35, redFringe: 4, blueFringe: -2 },
                gopro: { distortion: -25, vignetting: 20, redFringe: 3, blueFringe: -2 },
                drone: { distortion: -18, vignetting: 15, redFringe: 2, blueFringe: -1 }
            };

            const preset = presets[presetName];
            if (preset) {
                Object.keys(preset).forEach(key => {
                    const slider = document.getElementById(key);
                    if (slider) {
                        slider.value = preset[key];
                        lensCorrection.updateCorrection(key, preset[key]);
                    }
                });
            }
        }

        function resetCorrections() {
            if (!lensCorrection) return;

            Object.keys(lensCorrection.corrections).forEach(key => {
                const slider = document.getElementById(key);
                if (slider) {
                    const defaultValue = key === 'scale' ? 100 : 0;
                    slider.value = defaultValue;
                    lensCorrection.updateCorrection(key, defaultValue);
                }
            });
        }

        function autoCorrectPerspective() {
            showProgress('Analyzing perspective...', 3000);
            setTimeout(() => {
                // Simulate auto correction
                if (lensCorrection) {
                    document.getElementById('horizontal').value = -2;
                    document.getElementById('vertical').value = 1;
                    lensCorrection.updateCorrection('horizontal', -2);
                    lensCorrection.updateCorrection('vertical', 1);
                }
            }, 1500);
        }

        function autoCorrectDistortion() {
            showProgress('Detecting lens distortion...', 4000);
            setTimeout(() => {
                if (lensCorrection) {
                    document.getElementById('distortion').value = -12;
                    lensCorrection.updateCorrection('distortion', -12);
                }
            }, 2000);
        }

        function autoCorrectChromatic() {
            showProgress('Analyzing chromatic aberration...', 3500);
            setTimeout(() => {
                if (lensCorrection) {
                    document.getElementById('redFringe').value = 2;
                    document.getElementById('blueFringe').value = -1;
                    lensCorrection.updateCorrection('redFringe', 2);
                    lensCorrection.updateCorrection('blueFringe', -1);
                }
            }, 1800);
        }

        function showProgress(message, duration) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            progressBar.style.display = 'block';
            progressFill.style.width = '0%';

            let progress = 0;
            const interval = setInterval(() => {
                progress += 100 / (duration / 50);
                progressFill.style.width = Math.min(progress, 100) + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                    }, 500);
                }
            }, 50);
        }

        function copySettings() {
            if (!lensCorrection) return;

            const settings = JSON.stringify(lensCorrection.corrections);
            navigator.clipboard.writeText(settings).then(() => {
                alert('Nastavení zkopírována do schránky!');
            });
        }

        function pasteSettings() {
            if (!lensCorrection) return;

            navigator.clipboard.readText().then(text => {
                try {
                    const settings = JSON.parse(text);
                    Object.keys(settings).forEach(key => {
                        const slider = document.getElementById(key);
                        if (slider) {
                            slider.value = settings[key];
                            lensCorrection.updateCorrection(key, settings[key]);
                        }
                    });
                    alert('Nastavení vložena!');
                } catch (error) {
                    alert('Neplatná data v schránce!');
                }
            });
        }

        function savePreset() {
            if (!lensCorrection) return;

            const name = prompt('Název presetu:');
            if (name) {
                const settings = JSON.stringify(lensCorrection.corrections);
                localStorage.setItem('lensPreset_' + name, settings);
                alert('Preset uložen!');
            }
        }

        // Initialize the lens correction
        let lensCorrection;
        document.addEventListener('DOMContentLoaded', () => {
            lensCorrection = new LensCorrection();
        });
